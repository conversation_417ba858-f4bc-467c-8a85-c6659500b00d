package cn.jdl.oms.express.packing.extension.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.BasicTraderResponse;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;


/**
 * @Description:客户配置信息校验
 * @author: sunjingkai5
 * @date: 2022/3/25 11:28
 */
@Extension(code = ExpressOrderProduct.CODE)
public class PackingCustomerConfigExtension implements ICustomerConfigExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(PackingCustomerConfigExtension.class);

    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 客户配置信息校验扩展
     *
     * @param expressOrderContext
     * @throws AbilityExtensionException
     */
    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            //校验履约账号
            checkBasicTraderInfo(expressOrderContext);
        } catch (BusinessDomainException e) {
            LOGGER.error("packing客户配置信息校验扩展点执行异常", e);
            throw e;
        } catch (Exception exception) {
            Profiler.functionError(callerInfo);
            LOGGER.error("packing客户配置信息校验扩展点执行异常", exception);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, exception);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 商家基础资料校验
     *
     * @param expressOrderContext
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();
        String accountNo = expressOrderModel.getCustomer().getAccountNo();

        //根据履约账号获取账号配置信息
        BasicTraderResponse basicTraderResponse = customerConfigFacade.getCustomerConfig(accountNo);
        // 履约账号校验
        if (basicTraderResponse == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_VALIDATE_FAIL).withCustom("客户配置信息校验失败，未查到相关客户信息");
        }

        Integer traderOperateState = basicTraderResponse.getTraderOperateState();
        //青龙业主号校验
        if (!TraderOperateStateEnum.normal.getCode().equals(traderOperateState)) {
            LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                    , accountNo
                    , traderOperateState);
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("客户配置信息校验失败,商家青龙业主号状态异常");
        }
    }

}
