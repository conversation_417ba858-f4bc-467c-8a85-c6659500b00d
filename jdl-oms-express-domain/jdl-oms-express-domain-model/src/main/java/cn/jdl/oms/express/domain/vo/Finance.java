package cn.jdl.oms.express.domain.vo;

import cn.jdl.oms.express.domain.converter.CostMapper;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.model.IFinance;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.constant.FinanceConstants;
import com.alibaba.fastjson.annotation.JSONField;
import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Length;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @ProjectName： jdl-oms-express
 * @Package： cn.jdl.oms.express.domain.vo
 * @ClassName: Finance
 * @Description: 财务信息
 * @Author： wangjingzhao
 * @CreateDate 2021/3/10 11:12 上午
 * @Copyright: Copyright (c)2021 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Data
public class Finance implements IFinance {

    /**
     * 财务信息保留小数位数
     */
    public static final int FINANCE_DECIMAL_SCALE = 3;

    /**
     * 结算方式
     * 1：寄付现结，
     * <p>
     * 2：到付现结，
     * <p>
     * 3：寄付月结
     */
    private SettlementTypeEnum settlementType;
    /**
     * 是否询价
     * 1-不询价
     * <p>
     * 2-询价
     */
    private EnquiryTypeEnum enquiryType;
    /**
     * 预估运费金额，精度3
     */
    private Money estimateAmount;
    /**
     * 财务预占标识, 1：白条预授权
     */
    private Integer preemptType;
    /**
     * 1-先款支付；2-后款支付；
     */
    private PaymentStageEnum paymentStage;
    /**
     * 折后金额
     */
    private Money discountAmount;
    /**
     * 折前金额
     */
    private Money preAmount;
    /**
     * 总优惠金额
     */
    private Money totalDiscountAmount;
    /**
     * 计费重量
     */
    private Weight billingWeight;
    /**
     * 计费体积
     */
    private Volume billingVolume;
    /**
     * 支付账号
     */
    private String paymentAccountNo;
    /**
     * 收款机构
     */
    private String collectionOrgNo;
    /**
     * 收款机构名称
     */
    private String collectionOrgName;
    /**
     * 支付方式
     */
    private PaymentTypeEnum payment;
    /**
     * 结算账号
     */
    private String settlementAccountNo;
    /**
     * 支付状态
     * 1-待支付；
     * 2-已支付；
     */
    private PaymentStatusEnum paymentStatus;
    /**
     * 退款状态
     * 1-退款中；
     * 2-已退款；
     * 3-退款失败；
     */
    private RefundStatusEnum refundStatus;
    /**
     * 支付截止时间
     */
    private Date payDeadline;

    /**
     * 费用明细
     */
    private List<FinanceDetail> financeDetails;

    /**
     * 支付单号
     */
    @Length(max = 50, message = "支付单号(paymentNo)长度不能超过50")
    private String paymentNo;
    /**
     * 支付时间
     */
    private Date paymentTime;

    /**
     * 积分
     */
    private Points points;

    /**
     * 询价状态
     */
    private EnquiryStatusEnum enquiryStatus;

    /**
     * 计费模式
     */
    private String billingMode;

    /**
     * 计费方式
     */
    private String billingType;

    /**
     * 财务备注，用于存储台账加和时原始单加和过来的信息,以及新单的账信息
     */
    private String remark;
    /**
     * 询价时间
     */
    private Date enquireTime;

    /**
     * 抵扣信息
     */
    private DeductionDelegate deductionDelegate;
    /**
     * 收费要求信息
     */
    private List<CostInfo> costInfos;

    /**
     * 附加费用：报关服务费、揽收偏远附加费、派送偏远附加费、燃油附加费
     */
    private List<CostInfo> attachFees;

    /**
     * 预估税金
     */
    private Money estimatedTax;

    /**
     * 真实税金
     */
    private Money actualTax;

    /**
     * 扩展信息
     */
    private Map<String, String> extendProps;

    /**
     * 费用支付状态归集
     */
    private Map<String, String> payStatusMap;

    /**
     * 支付人Pin集合，需要去重存储
     */
    private List<String> payerPins;

    /**
     * 税金结算方式
     */
    private Integer taxSettlementType;

    /**
     * 应收差额
     */
    private Money receivableDifferenceAmount;

    /**
     * 已退金额
     */
    private Money refundedAmount;

    /**
     * 原始实收金额
     */
    private Money originalReceivedAmount;

    /**
     * 待支付金额
     */
    private Money pendingMoney;

    /**
     * 预占金额
     */
    private Money occupyAmount;

    /**
     * 预占模式
     */
    private Integer occupyMode;

    /**
     * 预估财务信息
     */
    private Finance estimateFinanceInfo;

    /**
     * 实际支付方式
     */
    private Integer actualPaymentType;

    /**
     * 加价后总金额
     */
    private Money totalAdditionAmount;

    /**
     * 多方计费收费总额
     */
    private List<CostInfo> multiPartiesTotalAmounts;

    /**
     * 私有构造方法
     */
    private Finance() {
    }

    /**
     * 财务信息
     *
     * @return
     */
    public static Finance financeOf(@NotNull ExpressOrderModelCreator creator) {
        Finance finance = new Finance();
        Optional.ofNullable(creator.getFinanceInfo()).ifPresent(financeInfo -> {
            //结算方式
            finance.setSettlementType(financeInfo.getSettlementType());
            //是否询价
            finance.setEnquiryType(financeInfo.getEnquiryType());
            //预估费用
            Optional.ofNullable(financeInfo.getEstimateAmount()).ifPresent(estimateAmount -> {
                Money amount = new Money();
                amount.setAmount(estimateAmount.getAmount());
                amount.setCurrency(estimateAmount.getCurrencyCode());
                finance.setEstimateAmount(amount);
            });
            // 预占金额
            Optional.ofNullable(financeInfo.getOccupyAmount()).ifPresent(occupyAmount -> {
                Money amount = new Money();
                amount.setAmount(occupyAmount.getAmount());
                amount.setCurrency(occupyAmount.getCurrencyCode());
                finance.setOccupyAmount(amount);
            });
            // 预占模式
            finance.setOccupyMode(financeInfo.getOccupyMode());

            // 预估财务信息
            Optional.ofNullable(financeInfo.getEstimateFinanceInfo()).ifPresent(estimateFinanceInfoDto -> {
                Finance estimateFinanceInfo = new Finance();
                // 预估-折前金额
                Optional.ofNullable(estimateFinanceInfoDto.getPreAmount()).ifPresent(estimatePreAmount -> {
                    estimateFinanceInfo.setPreAmount(MoneyMapper.INSTANCE.toMoney(estimatePreAmount));
                });
                // 预估-折后金额
                Optional.ofNullable(estimateFinanceInfoDto.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                    estimateFinanceInfo.setDiscountAmount(MoneyMapper.INSTANCE.toMoney(estimateDiscountAmount));
                });
                // 预估-计费重量
                Optional.ofNullable(estimateFinanceInfoDto.getBillingWeight()).ifPresent(estimateBillingWeightDto -> {
                    Weight estimateBillingWeight = new Weight();
                    estimateBillingWeight.setValue(estimateBillingWeightDto.getValue());
                    estimateBillingWeight.setUnit(estimateBillingWeightDto.getUnit());
                    estimateFinanceInfo.setBillingWeight(estimateBillingWeight);
                });
                // 预估-计费体积
                Optional.ofNullable(estimateFinanceInfoDto.getBillingVolume()).ifPresent(estimateBillingVolumeDto -> {
                    Volume estimateBillingVolume = new Volume();
                    estimateBillingVolume.setValue(estimateBillingVolumeDto.getValue());
                    estimateBillingVolume.setUnit(estimateBillingVolumeDto.getUnit());
                    estimateFinanceInfo.setBillingVolume(estimateBillingVolume);
                });
                // 预估-加价后总金额
                Optional.ofNullable(estimateFinanceInfoDto.getTotalAdditionAmount()).ifPresent(totalAdditionAmount -> {
                    estimateFinanceInfo.setTotalAdditionAmount(MoneyMapper.INSTANCE.toMoney(totalAdditionAmount));
                });
                // 预估-费用明细
                Optional.ofNullable(estimateFinanceInfoDto.getFinanceDetailInfos()).ifPresent(estimateFinanceDetailInfoDtos -> {
                    // 预估-费用明细信息
                    List<FinanceDetail> estimateFinanceDetails = Lists.newArrayListWithCapacity(estimateFinanceDetailInfoDtos.size());
                    estimateFinanceDetailInfoDtos.forEach(estimateFinanceDetailInfoDto -> {
                        // 预估-费用明细对象
                        FinanceDetail estimateFinanceDetail = new FinanceDetail();
                        // 预估-费用编号
                        estimateFinanceDetail.setCostNo(estimateFinanceDetailInfoDto.getCostNo());
                        // 预估-费用名称
                        estimateFinanceDetail.setCostName(estimateFinanceDetailInfoDto.getCostName());
                        // 预估-费用产品编码
                        estimateFinanceDetail.setProductNo(estimateFinanceDetailInfoDto.getProductNo());
                        // 预估-费用产品名称
                        estimateFinanceDetail.setProductName(estimateFinanceDetailInfoDto.getProductName());
                        // 预估-折扣信息
                        if (CollectionUtils.isNotEmpty(estimateFinanceDetailInfoDto.getDiscountInfoDtos())) {
                            List<Discount> estimateDiscountList = new ArrayList<>(estimateFinanceDetailInfoDto.getDiscountInfoDtos().size());
                            estimateFinanceDetailInfoDto.getDiscountInfoDtos().forEach(estimateDiscountInfoDto -> {
                                // 预估-折扣对象
                                Discount estimateDiscount = new Discount();
                                // 预估-折扣码
                                estimateDiscount.setDiscountNo(estimateDiscountInfoDto.getDiscountNo());
                                // 预估-折扣类型
                                estimateDiscount.setDiscountType(estimateDiscountInfoDto.getDiscountType());
                                if (estimateDiscountInfoDto.getDiscountedAmount() != null) {
                                    // 预估-折扣金额
                                    Money estimateDiscountedAmount = new Money();
                                    estimateDiscountedAmount.setAmount(estimateDiscountInfoDto.getDiscountedAmount().getAmount());
                                    estimateDiscountedAmount.setCurrency(estimateDiscountInfoDto.getDiscountedAmount().getCurrency());
                                    estimateDiscount.setDiscountedAmount(estimateDiscountedAmount);
                                }
                                estimateDiscountList.add(estimateDiscount);
                            });

                            estimateFinanceDetail.setDiscounts(estimateDiscountList);
                        }
                        // 预估-折前金额
                        Optional.ofNullable(estimateFinanceDetailInfoDto.getPreAmount()).ifPresent(estimatePreAmount -> {
                            estimateFinanceDetail.setPreAmount(MoneyMapper.INSTANCE.toMoney(estimatePreAmount));
                        });
                        // 预估-折后金额
                        Optional.ofNullable(estimateFinanceDetailInfoDto.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                            estimateFinanceDetail.setDiscountAmount(MoneyMapper.INSTANCE.toMoney(estimateDiscountAmount));
                        });

                        // 预估-加价后金额
                        Optional.ofNullable(estimateFinanceDetailInfoDto.getAdditionAmount()).ifPresent(additionAmount -> {
                            estimateFinanceDetail.setAdditionAmount(MoneyMapper.INSTANCE.toMoney(additionAmount));
                        });

                        // 预估-扩展字段
                        estimateFinanceDetail.setExtendProps(estimateFinanceDetailInfoDto.getExtendProps());

                        estimateFinanceDetails.add(estimateFinanceDetail);
                    });

                    estimateFinanceInfo.setFinanceDetails(estimateFinanceDetails);
                });
                // 预估-计费模式
                Optional.ofNullable(estimateFinanceInfoDto.getBillingMode()).ifPresent(estimateFinanceInfo::setBillingMode);

                finance.setEstimateFinanceInfo(estimateFinanceInfo);
            });

            //支付账号
            finance.setPaymentAccountNo(financeInfo.getPaymentAccountNo());
            //支付方式
            finance.setPayment(financeInfo.getPayment());
            //实际支付方式
            finance.setActualPaymentType(financeInfo.getActualPaymentType());
            //结算账号
            finance.setSettlementAccountNo(financeInfo.getSettlementAccountNo());
            //财务预占标识
            finance.setPreemptType(financeInfo.getPreemptType());
            //1-先款支付；2-后款支付；
            finance.setPaymentStage(financeInfo.getPaymentStage());
            //询价状态
            finance.setEnquiryStatus(financeInfo.getEnquiryStatus());
            //折前金额
            Optional.ofNullable(financeInfo.getPreAmount()).ifPresent(preAmount -> {
                Money amount = new Money();
                amount.setAmount(preAmount.getAmount());
                amount.setCurrency(preAmount.getCurrencyCode());
                finance.setPreAmount(amount);
            });

            //折后金额
            Optional.ofNullable(financeInfo.getDiscountAmount()).ifPresent(discountAmount -> {
                Money amount = new Money();
                amount.setAmount(discountAmount.getAmount());
                amount.setCurrency(discountAmount.getCurrencyCode());
                finance.setDiscountAmount(amount);
            });

            //总优惠金额
            Optional.ofNullable(financeInfo.getTotalDiscountAmount()).ifPresent(totalDiscountAmount -> {
                Money amount = new Money();
                amount.setAmount(totalDiscountAmount.getAmount());
                amount.setCurrency(totalDiscountAmount.getCurrencyCode());
                finance.setTotalDiscountAmount(amount);
            });

            //计费重量
            Optional.ofNullable(financeInfo.getBillingWeight()).ifPresent(billingWeight -> {
                Weight weight = new Weight();
                weight.setValue(billingWeight.getValue());
                weight.setUnit(billingWeight.getUnit());
                finance.setBillingWeight(weight);
            });

            //计费体积
            Optional.ofNullable(financeInfo.getBillingVolume()).ifPresent(volumeInfo -> {
                Volume volume = new Volume();
                volume.setValue(volumeInfo.getValue());
                volume.setUnit(volumeInfo.getUnit());
                finance.setBillingVolume(volume);
            });

            //计费模式
            if (StringUtils.isNotBlank(financeInfo.getBillingMode())) {
                finance.setBillingMode(financeInfo.getBillingMode());
            }

            //收款机构
            finance.setCollectionOrgNo(financeInfo.getCollectionOrgNo());
            finance.setCollectionOrgName(financeInfo.getCollectionOrgName());
            //费用明细
            Optional.ofNullable(financeInfo.getFinanceDetailInfos()).ifPresent(financeDetailInfos -> {
                //费用明细信息
                List<FinanceDetail> financeDetails = Lists.newArrayListWithCapacity(financeDetailInfos.size());
                //遍历当前费用明细
                financeDetailInfos.forEach(financeDetailInfo -> {
                    //费用明细对象
                    FinanceDetail financeDetail = new FinanceDetail();
                    //费用编号
                    financeDetail.setCostNo(financeDetailInfo.getCostNo());
                    //费用名称
                    financeDetail.setCostName(financeDetailInfo.getCostName());
                    //费用产品编码
                    financeDetail.setProductNo(financeDetailInfo.getProductNo());
                    //产品名称
                    financeDetail.setProductName(financeDetailInfo.getProductName());
                    //向谁收
                    financeDetail.setChargingSource(financeDetailInfo.getChargingSource());

                    if (CollectionUtils.isNotEmpty(financeDetailInfo.getDiscountInfoDtos())) {
                        List<Discount> discounts = new ArrayList<>();
                        financeDetailInfo.getDiscountInfoDtos().forEach(discountInfoDto -> {
                            Discount discount = new Discount();
                            discount.setDiscountNo(discountInfoDto.getDiscountNo());
                            discount.setDiscountType(discountInfoDto.getDiscountType());
                            discount.setDiscountedAmount(discountInfoDto.getDiscountedAmount());
                            //扩展信息
                            discount.setExtendProps(discountInfoDto.getExtendProps());
                            discounts.add(discount);
                        });
                        financeDetail.setDiscounts(discounts);
                    }

                    //折前金额
                    Optional.ofNullable(financeDetailInfo.getPreAmount()).ifPresent(preAmount -> {
                        Money amount = new Money();
                        amount.setAmount(preAmount.getAmount());
                        amount.setCurrency(preAmount.getCurrencyCode());
                        financeDetail.setPreAmount(amount);
                    });
                    //折后金额
                    Optional.ofNullable(financeDetailInfo.getDiscountAmount()).ifPresent(discountAmount -> {
                        Money amount = new Money();
                        amount.setAmount(discountAmount.getAmount());
                        amount.setCurrency(discountAmount.getCurrencyCode());
                        financeDetail.setDiscountAmount(amount);
                    });
                    financeDetail.setRemark(financeDetailInfo.getRemark());
                    //积分
                    financeDetail.setPoints(toPoints(financeDetailInfo.getPointsInfoDto()));
                    //操作类型 TODO extendProps 不需要转换吗
                    if (financeDetailInfo.getExtendProps() != null && StringUtils.isNotBlank(financeDetailInfo.getExtendProps().get("operateType"))) {
                        financeDetail.setOperateType(OperateTypeEnum.fromCode(Integer.parseInt(financeDetailInfo.getExtendProps().get("operateType"))));
                    }

                    financeDetails.add(financeDetail);
                });
                finance.setFinanceDetails(financeDetails);
            });
            //支付账号
            finance.setPaymentNo(financeInfo.getPaymentNo());
            //支付时间
            finance.setPaymentTime(financeInfo.getPaymentTime());
            //支付状态
            finance.setPaymentStatus(financeInfo.getPaymentStatus());
            //支付单号
            finance.setPaymentNo(financeInfo.getPaymentNo());
            //支付截止时间
            finance.setPayDeadline(financeInfo.getPayDeadline());
            //积分支付信息
            finance.setPoints(toPoints(financeInfo.getPointsInfoDto()));
            //计费方式
            finance.setBillingType(financeInfo.getBillingType());
            //询价时间
            finance.setEnquireTime(financeInfo.getEnquireTime());
            //退款状态
            finance.setRefundStatus(financeInfo.getRefundStatus());
            //抵扣信息
            finance.setDeductionDelegate(DeductionDelegate.deductionDelegateOf(creator));
            //收费要求信息
            Optional.ofNullable(financeInfo.getCostInfoDtos()).ifPresent(costInfoDtos -> {
                //收费要求信息
                List<CostInfo> costInfoList = Lists.newArrayListWithCapacity(costInfoDtos.size());
                //遍历当前收费要求信息
                costInfoDtos.forEach(costInfoDto -> {
                    if (costInfoDto != null) {
                        costInfoList.add(toCostInfo(costInfoDto));
                    }
                });
                finance.setCostInfos(costInfoList);
            });

            //报关服务费、揽收偏远附加费、派送偏远附加费、燃油附加费
            Optional.ofNullable(financeInfo.getAttachFees()).ifPresent(attachFees -> {
                //收费要求信息
                List<CostInfo> attachFeesList = Lists.newArrayListWithCapacity(attachFees.size());
                //遍历当前收费要求信息
                attachFees.forEach(costInfoDto -> {
                    if (costInfoDto != null) {
                        attachFeesList.add(toCostInfo(costInfoDto));
                    }
                });
                finance.setAttachFees(attachFeesList);
            });
            //扩展字段
            finance.setExtendProps(financeInfo.getExtendProps());
            //预估税金
            finance.setEstimatedTax(MoneyMapper.INSTANCE.toMoney(financeInfo.getEstimatedTax()));
            //真实税金
            finance.setActualTax(MoneyMapper.INSTANCE.toMoney(financeInfo.getActualTax()));
            //支付状态归集
            finance.setPayStatusMap(financeInfo.getPayStatusMap());
            // 支付人集合
            finance.setPayerPins(financeInfo.getPayerPins());
            // 税金结算方式
            finance.setTaxSettlementType(financeInfo.getTaxSettlementType());
            // 应收差额
            finance.setReceivableDifferenceAmount(MoneyMapper.INSTANCE.toMoney(financeInfo.getReceivableDifferenceAmount()));
            // 已退金额
            finance.setRefundedAmount(MoneyMapper.INSTANCE.toMoney(financeInfo.getRefundedAmount()));
            // 原始实收金额
            finance.setOriginalReceivedAmount(MoneyMapper.INSTANCE.toMoney(financeInfo.getOriginalReceivedAmount()));
            // 待支付金额
            finance.setPendingMoney(MoneyMapper.INSTANCE.toMoney(financeInfo.getPendingMoney()));
            // 多方计费总金额
            finance.setMultiPartiesTotalAmounts(CostMapper.INSTANCE.toCostInfoVos(financeInfo.getMultiPartiesTotalAmounts()));
        });
        return finance;
    }

    /**
     * dto->vo
     *
     * @return
     */
    private static CostInfo toCostInfo(CostInfoDto dto) {
        CostInfo costInfo = new CostInfo();
        //费用编号
        costInfo.setCostNo(dto.getCostNo());
        //费用名称
        costInfo.setCostName(dto.getCostName());
        //收费方	0：向商家收 1：向寄件方收
        costInfo.setChargingSource(dto.getChargingSource());
        //月结费用填写（结算编码）前端卡控条件必填
        costInfo.setSettlementAccountNo(dto.getSettlementAccountNo());
        costInfo.setExtendProps(dto.getExtendProps());
        //加价信息
        costInfo.setAdditionPriceInfo(AdditionPriceInfo.additionPriceInfoOf(dto.getAdditionPriceInfoDto()));
        costInfo.setPreAmount(MoneyMapper.INSTANCE.toMoney(dto.getPreAmount()));
        costInfo.setDiscountAmount(MoneyMapper.INSTANCE.toMoney(dto.getDiscountAmount()));
        costInfo.setSettlementType(dto.getSettlementType());
        costInfo.setPaymentStage(dto.getPaymentStage());
        return costInfo;
    }
    /**
     * 积分信息
     *
     * @param pointsInfoDto
     * @return
     */
    private static Points toPoints(PointsInfoDto pointsInfoDto) {
        if (pointsInfoDto == null) {
            return null;
        }
        Points points = new Points();
        if (pointsInfoDto.getRedeemPointsQuantity() != null) {
            Quantity redeemPointsQuantity = new Quantity();
            redeemPointsQuantity.setUnit(pointsInfoDto.getRedeemPointsQuantity().getUnit());
            redeemPointsQuantity.setValue(pointsInfoDto.getRedeemPointsQuantity().getValue());
            points.setRedeemPointsQuantity(redeemPointsQuantity);
            points.setOriginalPointsQuantity(redeemPointsQuantity);
        }
        if (pointsInfoDto.getRedeemPointsAmount() != null) {
            Money redeemPointsAmount = new Money();
            redeemPointsAmount.setAmount(pointsInfoDto.getRedeemPointsAmount().getAmount());
            redeemPointsAmount.setCurrency(pointsInfoDto.getRedeemPointsAmount().getCurrencyCode());
            points.setRedeemPointsAmount(redeemPointsAmount);
        }
        return points;
    }

    /**
     * 补全财务信息
     *
     * @param modelCreator
     */
    public void complementFinanceInfo(ExpressOrderModelCreator modelCreator) {
        //预估费用
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getEstimateAmount() != null) {
            this.estimateAmount = new Money();
            this.estimateAmount.setAmount(modelCreator.getFinanceInfo().getEstimateAmount().getAmount());
            this.estimateAmount.setCurrency(modelCreator.getFinanceInfo().getEstimateAmount().getCurrencyCode());
        }
        //折前金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getPreAmount() != null) {
            this.preAmount = new Money();
            this.preAmount.setAmount(modelCreator.getFinanceInfo().getPreAmount().getAmount());
            this.preAmount.setCurrency(modelCreator.getFinanceInfo().getPreAmount().getCurrencyCode());
        }
        //折后金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getDiscountAmount() != null) {
            this.discountAmount = new Money();
            this.discountAmount.setAmount(modelCreator.getFinanceInfo().getDiscountAmount().getAmount());
            this.discountAmount.setCurrency(modelCreator.getFinanceInfo().getDiscountAmount().getCurrencyCode());
        }
        //加价后总金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getTotalAdditionAmount() != null) {
            this.totalAdditionAmount = new Money();
            this.totalAdditionAmount.setAmount(modelCreator.getFinanceInfo().getTotalAdditionAmount().getAmount());
            this.totalAdditionAmount.setCurrency(modelCreator.getFinanceInfo().getTotalAdditionAmount().getCurrencyCode());
        }
        //总优惠金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getTotalDiscountAmount() != null) {
            this.totalDiscountAmount = new Money();
            this.totalDiscountAmount.setAmount(modelCreator.getFinanceInfo().getTotalDiscountAmount().getAmount());
            this.totalDiscountAmount.setCurrency(modelCreator.getFinanceInfo().getTotalDiscountAmount().getCurrencyCode());
        }
        //计费重量
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getBillingWeight() != null) {
            this.billingWeight = this.billingWeight == null ? new Weight() : this.billingWeight;
            this.billingWeight.setValue(modelCreator.getFinanceInfo().getBillingWeight().getValue());
            this.billingWeight.setUnit(modelCreator.getFinanceInfo().getBillingWeight().getUnit());
        }
        //计费体积
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getBillingVolume() != null) {
            this.billingVolume = this.billingVolume == null ? new Volume() : this.billingVolume;
            this.billingVolume.setValue(modelCreator.getFinanceInfo().getBillingVolume().getValue());
            this.billingVolume.setUnit(modelCreator.getFinanceInfo().getBillingVolume().getUnit());
        }
        //计费模式
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getBillingMode() != null) {
            this.billingMode = modelCreator.getFinanceInfo().getBillingMode();
        }

        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getPayDeadline() != null) {
            this.payDeadline = modelCreator.getFinanceInfo().getPayDeadline();
        }
        //积分信息
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getPointsInfoDto() != null) {
            this.points = toPoints(modelCreator.getFinanceInfo().getPointsInfoDto());
        }
        //财务备注
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getRemark() != null) {
            this.remark = modelCreator.getFinanceInfo().getRemark();
        }

        this.financeDetails = Collections.emptyList();
        //费用明细
        Optional.ofNullable(modelCreator.getFinanceInfo().getFinanceDetailInfos()).ifPresent(financeDetailInfos -> {
            //费用明细信息
            List<FinanceDetail> financeDetails = Lists.newArrayListWithCapacity(financeDetailInfos.size());
            //遍历当前费用明细
            financeDetailInfos.forEach(financeDetailInfo -> {
                //费用明细对象
                FinanceDetail financeDetail = new FinanceDetail();
                //费用编号
                financeDetail.setCostNo(financeDetailInfo.getCostNo());
                //费用名称
                financeDetail.setCostName(financeDetailInfo.getCostName());
                //费用产品编码
                financeDetail.setProductNo(financeDetailInfo.getProductNo());
                //产品名称
                financeDetail.setProductName(financeDetailInfo.getProductName());
                //折前金额
                Optional.ofNullable(financeDetailInfo.getPreAmount()).ifPresent(preAmount -> {
                    Money amount = new Money();
                    amount.setAmount(preAmount.getAmount());
                    amount.setCurrency(preAmount.getCurrencyCode());
                    financeDetail.setPreAmount(amount);
                });
                //折后金额
                Optional.ofNullable(financeDetailInfo.getDiscountAmount()).ifPresent(discountAmount -> {
                    Money amount = new Money();
                    amount.setAmount(discountAmount.getAmount());
                    amount.setCurrency(discountAmount.getCurrencyCode());
                    financeDetail.setDiscountAmount(amount);
                });
                // 备注
                financeDetail.setRemark(financeDetailInfo.getRemark());
                //折扣明细处理
                if (CollectionUtils.isNotEmpty(financeDetailInfo.getDiscountInfoDtos())) {
                    List<Discount> discounts = new ArrayList<>();
                    for (DiscountInfoDto discountInfoDto : financeDetailInfo.getDiscountInfoDtos()) {
                        Discount discount = new Discount();
                        discount.setDiscountNo(discountInfoDto.getDiscountNo());
                        discount.setDiscountType(discountInfoDto.getDiscountType());
                        Money money = new Money();
                        money.setAmount(discountInfoDto.getDiscountedAmount().getAmount());
                        money.setCurrency(discountInfoDto.getDiscountedAmount().getCurrency());
                        discount.setDiscountedAmount(money);
                        discount.setExtendProps(discountInfoDto.getExtendProps());
                        discounts.add(discount);
                    }
                    financeDetail.setDiscounts(discounts);
                }
                // 向谁收
                financeDetail.setChargingSource(financeDetailInfo.getChargingSource());
                financeDetail.setExtendProps(financeDetailInfo.getExtendProps());
                financeDetails.add(financeDetail);
            });
            this.setFinanceDetails(financeDetails);
        });

        //扩展字段
        if (modelCreator.getFinanceInfo() != null && MapUtils.isNotEmpty(modelCreator.getFinanceInfo().getExtendProps())) {
            if(MapUtils.isNotEmpty(this.extendProps)){
                this.extendProps.putAll(modelCreator.getFinanceInfo().getExtendProps());
            } else {
                this.extendProps = modelCreator.getFinanceInfo().getExtendProps();
            }
        }

        //应收差额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getReceivableDifferenceAmount() != null) {
            this.receivableDifferenceAmount = new Money();
            this.receivableDifferenceAmount.setAmount(modelCreator.getFinanceInfo().getReceivableDifferenceAmount().getAmount());
            this.receivableDifferenceAmount.setCurrency(modelCreator.getFinanceInfo().getReceivableDifferenceAmount().getCurrencyCode());
        }

        //已退金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getRefundedAmount() != null) {
            this.refundedAmount = new Money();
            this.refundedAmount.setAmount(modelCreator.getFinanceInfo().getRefundedAmount().getAmount());
            this.refundedAmount.setCurrency(modelCreator.getFinanceInfo().getRefundedAmount().getCurrencyCode());
        }

        //原始实收金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getOriginalReceivedAmount() != null) {
            this.originalReceivedAmount = new Money();
            this.originalReceivedAmount.setAmount(modelCreator.getFinanceInfo().getOriginalReceivedAmount().getAmount());
            this.originalReceivedAmount.setCurrency(modelCreator.getFinanceInfo().getOriginalReceivedAmount().getCurrencyCode());
        }

        //待支付金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getPendingMoney() != null) {
            this.pendingMoney = new Money();
            this.pendingMoney.setAmount(modelCreator.getFinanceInfo().getPendingMoney().getAmount());
            this.pendingMoney.setCurrency(modelCreator.getFinanceInfo().getPendingMoney().getCurrencyCode());
        }

        // 询价状态
        if(modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getEnquiryStatus() != null) {
            this.enquiryStatus = modelCreator.getFinanceInfo().getEnquiryStatus();
        }
        //多方计费收费总额
        Optional.ofNullable(modelCreator.getFinanceInfo().getMultiPartiesTotalAmounts()).ifPresent(costInfoDtos -> {
            //收费要求信息
            List<CostInfo> costInfoList = Lists.newArrayListWithCapacity(costInfoDtos.size());
            //遍历当前收费要求信息
            costInfoDtos.forEach(costInfoDto -> {
                if (costInfoDto != null) {
                    costInfoList.add(toCostInfo(costInfoDto));
                }
            });
            this.multiPartiesTotalAmounts = costInfoList;
        });

    }

    public void complementFinanceDetails(ExpressOrderModelCreator modelCreator) {
        //费用明细信息
        if (CollectionUtils.isEmpty(this.financeDetails)) {
            this.financeDetails = new ArrayList<>();
        }
        //费用明细
        Optional.ofNullable(modelCreator.getFinanceInfo().getFinanceDetailInfos()).ifPresent(financeDetailInfos -> {
            //遍历当前费用明细
            financeDetailInfos.forEach(financeDetailInfo -> {
                //费用明细对象
                FinanceDetail financeDetail = new FinanceDetail();
                //费用编号
                financeDetail.setCostNo(financeDetailInfo.getCostNo());
                //费用名称
                financeDetail.setCostName(financeDetailInfo.getCostName());
                //费用产品编码
                financeDetail.setProductNo(financeDetailInfo.getProductNo());
                //产品名称
                financeDetail.setProductName(financeDetailInfo.getProductName());
                //折前金额
                Optional.ofNullable(financeDetailInfo.getPreAmount()).ifPresent(preAmount -> {
                    Money amount = new Money();
                    amount.setAmount(preAmount.getAmount());
                    amount.setCurrency(preAmount.getCurrencyCode());
                    financeDetail.setPreAmount(amount);
                });
                //折后金额
                Optional.ofNullable(financeDetailInfo.getDiscountAmount()).ifPresent(discountAmount -> {
                    Money amount = new Money();
                    amount.setAmount(discountAmount.getAmount());
                    amount.setCurrency(discountAmount.getCurrencyCode());
                    financeDetail.setDiscountAmount(amount);
                });
                financeDetail.setRemark(financeDetailInfo.getRemark());
                //折扣明细处理
                if (CollectionUtils.isNotEmpty(financeDetailInfo.getDiscountInfoDtos())) {
                    List<Discount> discounts = new ArrayList<>();
                    for (DiscountInfoDto discountInfoDto : financeDetailInfo.getDiscountInfoDtos()) {
                        Discount discount = new Discount();
                        discount.setDiscountNo(discountInfoDto.getDiscountNo());
                        discount.setDiscountType(discountInfoDto.getDiscountType());
                        Money money = new Money();
                        money.setAmount(discountInfoDto.getDiscountedAmount().getAmount());
                        money.setCurrency(discountInfoDto.getDiscountedAmount().getCurrency());
                        discount.setDiscountedAmount(money);
                        discount.setExtendProps(discountInfoDto.getExtendProps());
                        discounts.add(discount);
                    }
                    financeDetail.setDiscounts(discounts);
                }
                financeDetail.setExtendProps(financeDetailInfo.getExtendProps());
                financeDetails.add(financeDetail);
            });
        });
    }

    /**
     * 补全机构号和机构名称
     *
     * @param modelCreator
     */
    public void complementCollectionOrg(ExpressOrderModelCreator modelCreator) {
        this.collectionOrgNo = modelCreator.getFinanceInfo().getCollectionOrgNo();
        this.collectionOrgName = modelCreator.getFinanceInfo().getCollectionOrgName();
    }

    /**
     * 补全机构号和机构名称
     * @param collectionOrgNo
     * @param collectionOrgName
     */
    public void complementCollectionOrg(String collectionOrgNo, String collectionOrgName) {
        this.collectionOrgNo = collectionOrgNo;
        this.collectionOrgName = collectionOrgName;
    }

    /**
     * 补全计费重量,todo : 只补了重量 单位没有补全
     *
     * @param modelCreator
     */
    public void complementWeight(ExpressOrderModelCreator modelCreator) {
        this.billingWeight = this.billingWeight == null ? new Weight() : this.billingWeight;
        this.billingWeight.setValue(modelCreator.getFinanceInfo().getBillingWeight() != null ? modelCreator.getFinanceInfo().getBillingWeight().getValue() : null);
        this.billingWeight.setUnit(modelCreator.getFinanceInfo().getBillingWeight() != null ? modelCreator.getFinanceInfo().getBillingWeight().getUnit() : null);
    }

    /**
     * 补全支付单号信息
     *
     * @param modelCreator
     */
    public void complementFinanceInfoPaymentNo(ExpressOrderModelCreator modelCreator) {
        this.paymentNo = modelCreator.getFinanceInfo().getPaymentNo();
    }

    /**
     * 补全支付单号信息
     *
     * @param paymentNo
     */
    public void complementFinanceInfoPaymentNo(String paymentNo) {
        this.paymentNo = paymentNo;
    }

    /**
     * 补全支付状态信息
     *
     * @param paymentStatus
     */
    public void complementPaymentStatus(PaymentStatusEnum paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    /**
     * 补全退款状态信息
     *
     * @param refundStatus
     */
    public void complementRefundStatus(RefundStatusEnum refundStatus) {
        this.refundStatus = refundStatus;
    }

    /**
     * 补全询价状态信息
     * @param modelCreator
     */
    public void complementEnquiryStatus(ExpressOrderModelCreator modelCreator) {
        this.enquiryStatus = modelCreator.getFinanceInfo().getEnquiryStatus();
    }



    /**
     * 补全询价状态
     *
     * @param enquiryStatusEnum
     */
    public void complementEnquiryStatus(EnquiryStatusEnum enquiryStatusEnum) {
        this.enquiryStatus = enquiryStatusEnum;
    }

    public void complementBillVolumn(ExpressOrderModelCreator modelCreator) {
        this.billingVolume = this.billingVolume == null ? new Volume() : this.billingVolume;
        this.billingVolume.setValue(modelCreator.getFinanceInfo().getBillingVolume() != null ? modelCreator.getFinanceInfo().getBillingVolume().getValue() : null);
        this.billingVolume.setUnit(modelCreator.getFinanceInfo().getBillingVolume() != null ? modelCreator.getFinanceInfo().getBillingVolume().getUnit() : null);
    }

    /**
     * 补全支付阶段
     *
     * @param paymentStageEnum
     */
    public void complementPaymentStage(PaymentStageEnum paymentStageEnum) {
        this.paymentStage = paymentStageEnum;
    }
    /**
     * 补全结算方式
     *
     * @param settlementTypeEnum
     */
    public void complementSettlementType(SettlementTypeEnum settlementTypeEnum) {
        this.settlementType = settlementTypeEnum;
    }
    /**
     * 追加抵扣项
     * @param creator
     */
    public void appendDeductions(ExpressOrderModelCreator creator) {
        if (null == this.deductionDelegate) {
            this.deductionDelegate = DeductionDelegate.deductionDelegateOf(creator);
        } else {
            this.deductionDelegate.addDeductions(creator);
        }
    }


    /**
     * 补全税金信息
     */
    public void complementTax(Money estimatedTax, Money actualTax, Integer taxSettlementType) {
        this.estimatedTax = estimatedTax;
        this.actualTax = actualTax;
        this.taxSettlementType = taxSettlementType;
    }

    /**
     * 补全税金服务单号
     */
    public void complementTaxBankServiceOrderNo(String taxBankServiceOrderNo) {
        if(MapUtils.isEmpty(this.extendProps)){
            this.extendProps = new HashMap<>();
        }
        this.extendProps.put(FinanceConstants.TAX_BANK_SERVICE_ORDER_NO,taxBankServiceOrderNo);
    }

    /**
     * 补全支付截止时间
     *
     * @param payDeadline
     */
    public void complementPayDeadline(Date payDeadline) {
        this.payDeadline = payDeadline;
    }

    /**
     * 补全财务信息-各种费用
     *
     * @param modelCreator
     */
    public void complementFinanceAmount(ExpressOrderModelCreator modelCreator) {
        //预估费用
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getEstimateAmount() != null) {
            this.estimateAmount = new Money();
            this.estimateAmount.setAmount(modelCreator.getFinanceInfo().getEstimateAmount().getAmount());
            this.estimateAmount.setCurrency(modelCreator.getFinanceInfo().getEstimateAmount().getCurrencyCode());
        }
        //折前金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getPreAmount() != null) {
            this.preAmount = new Money();
            this.preAmount.setAmount(modelCreator.getFinanceInfo().getPreAmount().getAmount());
            this.preAmount.setCurrency(modelCreator.getFinanceInfo().getPreAmount().getCurrencyCode());
        }
        //折后金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getDiscountAmount() != null) {
            this.discountAmount = new Money();
            this.discountAmount.setAmount(modelCreator.getFinanceInfo().getDiscountAmount().getAmount());
            this.discountAmount.setCurrency(modelCreator.getFinanceInfo().getDiscountAmount().getCurrencyCode());
        }
        //总优惠金额
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getTotalDiscountAmount() != null) {
            this.totalDiscountAmount = new Money();
            this.totalDiscountAmount.setAmount(modelCreator.getFinanceInfo().getTotalDiscountAmount().getAmount());
            this.totalDiscountAmount.setCurrency(modelCreator.getFinanceInfo().getTotalDiscountAmount().getCurrencyCode());
        }
    }

    /**
     * 到付现结后款
     * @return
     */
    @JSONField(serialize = false)
    public boolean isReceiverPayOnDelivery() {
        return  SettlementTypeEnum.CASH_ON_DELIVERY == this.settlementType
                && PaymentStageEnum.CASHONDELIVERY == this.paymentStage;
    }

    /**
     * 寄付现结后款
     * @return
     */
    @JSONField(serialize = false)
    public boolean isSenderPayOnPick() {
        return  SettlementTypeEnum.CASH_ON_PICK == this.settlementType
                && PaymentStageEnum.CASHONDELIVERY == this.paymentStage;
    }

    /**
     * 设置扩展信息
     *
     * @param key
     * @param val
     */
    public void putExtendProps(String key,String val){
        if (null == extendProps) {
            this.extendProps = new HashMap<>();
        }
        this.extendProps.put(key, val);
    }

    /**
     * 判断是否支付中
     */
    @JSONField(serialize = false)
    public boolean isPaying() {
        if (null == extendProps) {
            return false;
        }
        String status = extendProps.get(OrderConstants.PAYMENT_FAILED_STATUS);
        // 支付单/代扣单状态是支付进行中or支付成功
        return OrderConstants.PAY_ING_STATUS.equals(status)
                || OrderConstants.PAY_SUCCESS_STATUS.equals(status);
    }

    public void complementEstimateFinanceInfo(ExpressOrderModelCreator modelCreator) {
        //预估费用
        if (modelCreator.getFinanceInfo() != null && modelCreator.getFinanceInfo().getEstimateAmount() != null) {
            this.estimateAmount = new Money();
            this.estimateAmount.setAmount(modelCreator.getFinanceInfo().getEstimateAmount().getAmount());
            this.estimateAmount.setCurrency(modelCreator.getFinanceInfo().getEstimateAmount().getCurrencyCode());
        }

        // 预估财务信息
        if(modelCreator.getFinanceInfo() != null) {
            Optional.ofNullable(modelCreator.getFinanceInfo().getEstimateFinanceInfo()).ifPresent(estimateFinanceInfoDto -> {
                Finance estimateFinanceInfo = new Finance();
                // 预估-折前金额
                Optional.ofNullable(estimateFinanceInfoDto.getPreAmount()).ifPresent(estimatePreAmount -> {
                    estimateFinanceInfo.setPreAmount(MoneyMapper.INSTANCE.toMoney(estimatePreAmount));
                });
                // 预估-折后金额
                Optional.ofNullable(estimateFinanceInfoDto.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                    estimateFinanceInfo.setDiscountAmount(MoneyMapper.INSTANCE.toMoney(estimateDiscountAmount));
                });
                // 预估-计费重量
                Optional.ofNullable(estimateFinanceInfoDto.getBillingWeight()).ifPresent(estimateBillingWeightDto -> {
                    Weight estimateBillingWeight = new Weight();
                    estimateBillingWeight.setValue(estimateBillingWeightDto.getValue());
                    estimateBillingWeight.setUnit(estimateBillingWeightDto.getUnit());
                    estimateFinanceInfo.setBillingWeight(estimateBillingWeight);
                });
                // 预估-计费体积
                Optional.ofNullable(estimateFinanceInfoDto.getBillingVolume()).ifPresent(estimateBillingVolumeDto -> {
                    Volume estimateBillingVolume = new Volume();
                    estimateBillingVolume.setValue(estimateBillingVolumeDto.getValue());
                    estimateBillingVolume.setUnit(estimateBillingVolumeDto.getUnit());
                    estimateFinanceInfo.setBillingVolume(estimateBillingVolume);
                });
                // 预估-加价后总金额
                Optional.ofNullable(estimateFinanceInfoDto.getTotalAdditionAmount()).ifPresent(totalAdditionAmount -> {
                    estimateFinanceInfo.setTotalAdditionAmount(MoneyMapper.INSTANCE.toMoney(totalAdditionAmount));
                });
                // 预估-费用明细
                Optional.ofNullable(estimateFinanceInfoDto.getFinanceDetailInfos()).ifPresent(estimateFinanceDetailInfoDtos -> {
                    // 预估-费用明细信息
                    List<FinanceDetail> estimateFinanceDetails = Lists.newArrayListWithCapacity(estimateFinanceDetailInfoDtos.size());
                    estimateFinanceDetailInfoDtos.forEach(estimateFinanceDetailInfoDto -> {
                        // 预估-费用明细对象
                        FinanceDetail estimateFinanceDetail = new FinanceDetail();
                        // 预估-费用编号
                        estimateFinanceDetail.setCostNo(estimateFinanceDetailInfoDto.getCostNo());
                        // 预估-费用名称
                        estimateFinanceDetail.setCostName(estimateFinanceDetailInfoDto.getCostName());
                        // 预估-费用产品编码
                        estimateFinanceDetail.setProductNo(estimateFinanceDetailInfoDto.getProductNo());
                        // 预估-费用产品名称
                        estimateFinanceDetail.setProductName(estimateFinanceDetailInfoDto.getProductName());
                        // 预估-折扣信息
                        if (CollectionUtils.isNotEmpty(estimateFinanceDetailInfoDto.getDiscountInfoDtos())) {
                            List<Discount> estimateDiscountList = new ArrayList<>(estimateFinanceDetailInfoDto.getDiscountInfoDtos().size());
                            estimateFinanceDetailInfoDto.getDiscountInfoDtos().forEach(estimateDiscountInfoDto -> {
                                // 预估-折扣对象
                                Discount estimateDiscount = new Discount();
                                // 预估-折扣码
                                estimateDiscount.setDiscountNo(estimateDiscountInfoDto.getDiscountNo());
                                // 预估-折扣类型
                                estimateDiscount.setDiscountType(estimateDiscountInfoDto.getDiscountType());
                                if (estimateDiscountInfoDto.getDiscountedAmount() != null) {
                                    // 预估-折扣金额
                                    Money estimateDiscountedAmount = new Money();
                                    estimateDiscountedAmount.setAmount(estimateDiscountInfoDto.getDiscountedAmount().getAmount());
                                    estimateDiscountedAmount.setCurrency(estimateDiscountInfoDto.getDiscountedAmount().getCurrency());
                                    estimateDiscount.setDiscountedAmount(estimateDiscountedAmount);
                                }
                                estimateDiscountList.add(estimateDiscount);
                            });

                            estimateFinanceDetail.setDiscounts(estimateDiscountList);
                        }
                        // 预估-折前金额
                        Optional.ofNullable(estimateFinanceDetailInfoDto.getPreAmount()).ifPresent(estimatePreAmount -> {
                            estimateFinanceDetail.setPreAmount(MoneyMapper.INSTANCE.toMoney(estimatePreAmount));
                        });
                        // 预估-折后金额
                        Optional.ofNullable(estimateFinanceDetailInfoDto.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                            estimateFinanceDetail.setDiscountAmount(MoneyMapper.INSTANCE.toMoney(estimateDiscountAmount));
                        });

                        // 预估-加价后金额
                        Optional.ofNullable(estimateFinanceDetailInfoDto.getAdditionAmount()).ifPresent(additionAmount -> {
                            estimateFinanceDetail.setAdditionAmount(MoneyMapper.INSTANCE.toMoney(additionAmount));
                        });

                        // 预估-扩展字段
                        estimateFinanceDetail.setExtendProps(estimateFinanceDetailInfoDto.getExtendProps());

                        estimateFinanceDetails.add(estimateFinanceDetail);
                    });

                    estimateFinanceInfo.setFinanceDetails(estimateFinanceDetails);
                });

                this.estimateFinanceInfo = estimateFinanceInfo;
            });
        }
    }

    public boolean containsAttachFee(@NotNull String feeType) {
        if (CollectionUtils.isEmpty(attachFees)) {
            return false;
        }
        for (CostInfo attachFee : attachFees) {
            if (feeType.equals(attachFee.getCostNo())) {
                return true;
            }
        }
        return false;
    }
}
