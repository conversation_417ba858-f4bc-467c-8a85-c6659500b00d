package cn.jdl.oms.express.domain.model;

import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.EnquiryStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.RefundStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.TransportTypeEnum;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: ComplementDomainModel
 * @Description: 补全订单中心纯配领域订单模型
 */
public class ComplementDomainModel {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ComplementDomainModel.class);

    /**
     * 配送信息-扩展信息 营业厅编码 KEY
     */
    private static final String BUSINESS_HALL_NO_KEY = "businessHallNo";

    /**
     * 配送信息-扩展信息 营业厅名称 KEY
     */
    private static final String BUSINESS_HALL_NAME_KEY = "businessHallName";


    /**
     * 纯配订单模型
     */
    private final ExpressOrderModel orderModel;


    /**
     * 私有构造方法
     *
     * @param orderModel
     */
    public ComplementDomainModel(ExpressOrderModel orderModel) {
        this.orderModel = orderModel;
    }

    /**
     * 补充发件人Gis解析后地址信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementConsignorAddressGis(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} setConsignorAddressGis:{}", who, modelCreator);
        this.orderModel.getConsignor().complementConsignorAddressGis(who, modelCreator);
    }

    /**
     * 补充收件人Gis解析后地址信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementConsigneeAddressGis(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} setConsigneeAddressGis:{}", who, modelCreator);
        this.orderModel.getConsignee().complementConsigneeAddressGis(who, modelCreator);
    }

    /**
     * 补充发件人地址围栏
     *
     * @param who
     * @param modelCreator
     */
    public void complementConsignorAddressFence(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} setConsignorAddressFence:{}", who, modelCreator);
        this.orderModel.getConsignor().complementConsignorAddressFence(who, modelCreator);
    }

    /**
     * 补充收件人地址围栏
     *
     * @param who
     * @param modelCreator
     */
    public void complementConsigneeAddressFence(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} setConsigneeAddressFence:{}", who, modelCreator);
        this.orderModel.getConsignee().complementConsigneeAddressFence(who, modelCreator);
    }

    /**
     * 补充运单号信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementWaybillNo(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementWaybillNo:{}", who, modelCreator);
        this.orderModel.getRefOrderInfoDelegate().complementWaybillNo(modelCreator);
    }

    /**
     * 补充询价单号信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementEnquiryOrderNo(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEnquiryOrderNo:{}", who, modelCreator);
        this.orderModel.getRefOrderInfoDelegate().complementEnquiryOrderNo(modelCreator);
    }

    /**
     * 补充询价单号信息
     *
     * @param who
     * @param enquiryOrderNo
     */
    public void complementEnquiryOrderNo(Object who, String enquiryOrderNo) {
        LOGGER.info("{} complementEnquiryOrderNo:{}", who, enquiryOrderNo);
        this.orderModel.getRefOrderInfoDelegate().complementRefOrderNo(enquiryOrderNo, RefOrderTypeEnum.ENQUIRY);
    }

    /**
     * 补充询价单号信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementChildrenOrderNos(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementChildrenOrderNos:{}", who, modelCreator);
        this.orderModel.getRefOrderInfoDelegate().complementChildOrderNos(modelCreator);
    }

    /**
     * 补充订单号信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementOrderNo(Object who, ExpressOrderModelCreator modelCreator) {
        this.orderModel.assignOrderNo(who, modelCreator.getOrderNo());
    }

    /**
     * 补充业务单号信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementCustomOrderNo(Object who, ExpressOrderModelCreator modelCreator) {
        this.orderModel.assignCustomOrderNo(who, modelCreator.getCustomOrderNo());
    }

    /**
     * 补齐订单状态及自定义状态
     *
     * @param who
     * @param modelCreator
     */
    public void complementCustomStatus(Object who, ExpressOrderModelCreator modelCreator) {
        this.orderModel.transferOrderStatus(modelCreator.getOrderStatusCustom());
    }

    /**
     * 补齐产品信息（产品名称，类型，父产品）
     *
     * @param who
     * @param modelCreator
     * @param isWarehouseMode
     */
    public void complementProduct(Object who, ExpressOrderModelCreator modelCreator, boolean isWarehouseMode) {
        LOGGER.info("{} complementProduct:{}", who, modelCreator);
        this.orderModel.getProductDelegate().complementProduct(modelCreator, isWarehouseMode);
    }

    /**
     * 补齐产品信息（产品名称，类型，父产品）
     *
     * @param who
     * @param modelCreator
     * @param isWarehouseMode
     */
    public void complementProductB2C_C2B(Object who, ExpressOrderModelCreator modelCreator, boolean isWarehouseMode) {
        LOGGER.info("{} complementProduct:{}", who, modelCreator);
        this.orderModel.getProductDelegate().complementProductB2C_C2B(modelCreator, isWarehouseMode);
    }

    /**
     * 增值产品信息处理
     *
     * @param who
     * @param modelCreator
     */
    public void complementDegradeProduct(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementProduct:{}", who, modelCreator);
        this.orderModel.getProductDelegate().complementDegradeProduct(modelCreator);
    }

    /**
     * 增值商品增值服务处理
     *
     * @param who
     * @param modelCreator
     */
    public void complementGoodsProduct(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementGoodsProduct:{}", who, modelCreator);
        this.orderModel.getGoodsDelegate().complementGoodsProduct(modelCreator);
    }


    /**
     * 补全配送始发站点信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementStartStation(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementStartStation:{}", who, modelCreator);
        if (modelCreator.getExtendProps() != null) {
            for (Map.Entry<String, String> extendProp : modelCreator.getExtendProps().entrySet()) {
                this.orderModel.putAttachment(extendProp.getKey(), extendProp.getValue());
            }
        }

        this.orderModel.getShipment().complementStartStation(modelCreator);
    }

    /**
     * 清空配送始发站点信息
     *
     * @param who
     */
    public void clearStartStation(Object who) {
        LOGGER.info("{} clearStartStation", who);
        this.orderModel.getShipment().clearStartStation();
    }

    /**
     * 补全配送扩展信息
     * @param who
     * @param modelCreator
     */
    public void complementShipmentExtendProps(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementShipmentExtendProps:{}", who, modelCreator);
        this.orderModel.getShipment().complementExtendProps(modelCreator);
    }

    /**
     * 补全配送扩展信息
     * @param who
     * @param extendProps
     */
    public void complementShipmentExtendProps(Object who, Map<String, String> extendProps) {
        LOGGER.info("{} complementShipmentExtendProps:{}", who, extendProps);
        this.orderModel.getShipment().complementExtendProps(extendProps);
    }

    /**
     * 补全配送中心相关信息
     * @param who
     * @param modelCreator
     */
    public void complementCenterInfo(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementCenterInfo:{}", who, modelCreator);
        if (modelCreator.getExtendProps() != null) {
            for (Map.Entry<String, String> extendProp : modelCreator.getExtendProps().entrySet()) {
                this.orderModel.putAttachment(extendProp.getKey(), extendProp.getValue());
            }
        }

        this.orderModel.getShipment().complementStartStation(modelCreator);
    }

    /**
     * 补全配送目的站点信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementEndStation(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEndStation:{}", who, modelCreator);
        if (modelCreator.getExtendProps() != null) {
            for (Map.Entry<String, String> extendProp : modelCreator.getExtendProps().entrySet()) {
                this.orderModel.putAttachment(extendProp.getKey(), extendProp.getValue());
            }
        }
        this.orderModel.getShipment().complementEndStation(modelCreator);
    }

    /**
     * 补全配送目的接驳站点信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementEndTransferStation(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEndTransferStation:{}", who, modelCreator);
        this.orderModel.getShipment().complementEndTransferStation(modelCreator);
    }

    /**
     * 补全配送目的站点类型
     *
     * @param who
     * @param modelCreator
     */
    public void complementEndStationType(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEndStationType:{}", who, modelCreator);
        this.orderModel.getShipment().complementEndStationType(modelCreator);
    }

    /**
     * 补全下发预分捡信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementPresort(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEndStation:{}", who, modelCreator);
        this.orderModel.putAttachment(OrderConstants.PRESORT_EXTEND, modelCreator.getExtendProps().get(OrderConstants.PRESORT_EXTEND));
    }

    /**
     * 补全配送始发站点类型
     *
     * @param who
     * @param modelCreator
     */
    public void complementStartStationType(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementStartStationType:{}", who, modelCreator);
        this.orderModel.getShipment().complementStartStationType(modelCreator);
    }

    /**
     * 补全财务信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementFinanceInfo(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementFinanceInfo:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementFinanceInfo(modelCreator);
    }

    /**
     * 补全财务信息的预估运费字段
     *
     * @param who
     * @param modelCreator
     */
    public void complementEstimateFinanceInfo(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEstimateFinanceInfo:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementEstimateFinanceInfo(modelCreator);
    }

    /**
     * 补全订单ID
     *
     * @param who
     * @param modelCreator
     */
/*    public void complementOrderId(Object who, ExpressOrderModelCreator modelCreator) {
        this.orderModel.assignOrderId(who, modelCreator.getOrderId());
    }*/

    /**
     * 补全机构号和机构名称
     *
     * @param who
     * @param modelCreator
     */
    public void complementFinanceCollectionOrg(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementFinanceCollectionOrg:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementCollectionOrg(modelCreator);
    }

    /**
     * 补全机构号和机构名称
     * @param who
     * @param orgId
     * @param orgName
     */
    public void complementFinanceCollectionOrg(Object who, String orgId, String orgName) {
        LOGGER.info("{} complementFinanceCollectionOrg:{} {}", who, orgId, orgName);
        this.orderModel.getFinance().complementCollectionOrg(orgId, orgName);
    }

    /**
     * 补全计费重量
     *
     * @param who
     * @param modelCreator
     */
    public void complementFinanceInfoWeight(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementFinanceInfoWeight:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementWeight(modelCreator);
    }

    /**
     * 补全支付单号信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementFinanceInfoPaymentNo(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementFinanceInfo:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementFinanceInfoPaymentNo(modelCreator);
    }

    /**
     * 补全支付单号信息
     *
     * @param who
     * @param paymentNo
     */
    public void complementFinanceInfoPaymentNo(Object who, String paymentNo) {
        LOGGER.info("{} complementFinanceInfo:{}", who, paymentNo);
        this.orderModel.getFinance().complementFinanceInfoPaymentNo(paymentNo);
    }

    /**
     * 补全账号id信息
     *
     * @param who
     * @param accountId
     */
    public void complementAccountId(Object who, Long accountId) {
        LOGGER.info("{} complementAccountId:{}", who, accountId);
        this.orderModel.getCustomer().complementAccountId(accountId);
    }

    /**
     * 补全账号名称信息
     *
     * @param who
     * @param accountName
     */
    public void complementAccountName(Object who, String accountName) {
        LOGGER.info("{} complementAccountName:{}", who, accountName);
        this.orderModel.getCustomer().complementAccountName(accountName);
    }

    /**
     * 补全履约账号2名称
     *
     * @param who
     * @param accountName2
     */
    public void complementAccountName2(Object who, String accountName2) {
        LOGGER.info("{} complementAccountName2: {}", who, accountName2);
            this.orderModel.getCustomer().complementAccountName2(accountName2);
    }

    /**
     * 补全支付状态信息
     *
     * @param who
     * @param paymentStatus
     */
    public void complementPaymentStatus(Object who, PaymentStatusEnum paymentStatus) {
        LOGGER.info("{} complementPaymentStatus:{}", who, JSONUtils.beanToJSONDefault(paymentStatus));
        this.orderModel.getFinance().complementPaymentStatus(paymentStatus);
    }

    /**
     * 补全退款状态信息
     *
     * @param who
     * @param refundStatusEnum
     */
    public void complementRefundStatus(Object who, RefundStatusEnum refundStatusEnum) {
        LOGGER.info("{} complementRefundStatus:{}", who, JSONUtils.beanToJSONDefault(refundStatusEnum));
        this.orderModel.getFinance().complementRefundStatus(refundStatusEnum);
    }

    /**
     * 补全订单拓展字段的业务说明
     *
     * @param who
     * @param modelCreator
     */
    public void complementOrderBusinessStatement(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementOrderBusinessStatement:{}", who, modelCreator);
        this.orderModel.putAttachment(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS, modelCreator.getExtendProps().get(OrderConstants.CUSTOMER_INFO_EXTEND_PROPS));
    }

    /**
     * 补全询价状态信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementEnquiryStatus(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEnquiryStatus:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementEnquiryStatus(modelCreator);
    }

    /**
     * 补全询价状态信息
     *
     * @param who
     * @param enquiryStatusEnum
     */
    public void complementEnquiryStatus(Object who, EnquiryStatusEnum enquiryStatusEnum) {
        LOGGER.info("{} complementEnquiryStatus:{}", who, JSONUtils.beanToJSONDefault(enquiryStatusEnum));
        this.orderModel.getFinance().complementEnquiryStatus(enquiryStatusEnum);
    }

    /**
     * 补全计费体积
     *
     * @param who
     * @param modelCreator
     */
    public void complementFinanceBillVolumn(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementFinanceBillVolumn:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementBillVolumn(modelCreator);
    }

    /**
     * 补全支付阶段信息
     *
     * @param who
     * @param paymentStageEnum
     */
    public void complementPaymentStage(Object who, PaymentStageEnum paymentStageEnum) {
        LOGGER.info("{} complementPaymentStage:{}", who, JSONUtils.beanToJSONDefault(paymentStageEnum));
        this.orderModel.getFinance().complementPaymentStage(paymentStageEnum);
    }

    /**
     * 补全结算方式
     *
     * @param who
     * @param settlementTypeEnum
     */
    public void complementSettlementType(Object who, SettlementTypeEnum settlementTypeEnum) {
        LOGGER.info("{} settlementTypeEnum:{}", who, JSONUtils.beanToJSONDefault(settlementTypeEnum));
        this.orderModel.getFinance().complementSettlementType(settlementTypeEnum);
    }

    /**
     * 补全操作类型
     *
     * @param who
     * @param map
     */
    public void complementModifiedFields(Object who, Map<String, String> map) {
        LOGGER.info("{} complementModifiedFields:{}", who, JSONUtils.beanToJSONDefault(map));
        this.orderModel.complementModifiedFields(map);
    }


    /**
     * 补全询价记录
     *
     * @param who
     * @param modelCreator
     */
    public void complementEnquiry(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementEnquiry:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getEnquiry().complementEnquiry(modelCreator);
    }

    /**
     * 补全扩展信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementExtendProps(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementExtendProps:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        if (modelCreator.getExtendProps() != null) {
            for (Map.Entry<String, String> extendProp : modelCreator.getExtendProps().entrySet()) {
                this.orderModel.putAttachment(extendProp.getKey(), extendProp.getValue());
            }
        }
    }

    /**
     * 补充业务hiddenMark
     *
     * @param who
     * @param hiddenMark 隐藏标识
     */
    public void complementHiddenMark(Object who, String hiddenMark) {
        this.orderModel.setHiddenMark(who, hiddenMark);
    }

    /**
     * 补全取件码信息
     *
     * @param who
     * @param pickupCode
     */
    public void complementPickupCode(Object who, String pickupCode) {
        LOGGER.info("{} complementPickupCode:{}", who, pickupCode);
        this.orderModel.getShipment().setPickupCode(pickupCode);
    }

    /**
     * 补全解决方案名称
     *
     * @param who
     * @param businessSolutionName
     */
    public void complementBusinessSolutionName(Object who, String businessSolutionName) {
        LOGGER.info("{} complementBusinessSolutionName:{}", who, businessSolutionName);
        this.orderModel.getBusinessSolution().setBusinessSolutionName(businessSolutionName);
    }

    /**
     * 补全配送-预计送达时间
     *
     * @param who
     * @param planDeliveryTime
     */
    public void complementShipmentPlanDeliveryTime(Object who, Date planDeliveryTime) {
        LOGGER.info("{} complementShipmentPlanDeliveryTime:{}", who, planDeliveryTime);
        this.orderModel.getShipment().setPlanDeliveryTime(planDeliveryTime);
    }

    /**
     * 补全配送-期望揽收开始时间
     * @param who
     * @param expectPickupStartTime
     */
    public void complementShipmentExpectPickupStartTime(Object who, Date expectPickupStartTime) {
        LOGGER.info("{} complementShipmentExpectPickupStartTime: {}", who, expectPickupStartTime);
        this.orderModel.getShipment().setExpectPickupStartTime(expectPickupStartTime);
    }

    /**
     * 补全配送-期望揽收结束时间
     * @param who
     * @param expectPickupEndTime
     */
    public void complementShipmentExpectPickupEndTime(Object who, Date expectPickupEndTime) {
        LOGGER.info("{} complementShipmentExpectPickupEndTime: {}", who, expectPickupEndTime);
        this.orderModel.getShipment().setExpectPickupEndTime(expectPickupEndTime);
    }

    /**
     * 补全配送 - 运输方式
     * @param who
     * @param transportType  运输方式
     */
    public void complementShipmentTransportType(Object who, TransportTypeEnum transportType) {
        LOGGER.info("{} complementShipmentTransportType:{}", who, transportType);
        this.orderModel.getShipment().setTransportType(transportType);
    }

    /**
     * 补全配送 - 承运商信息
     * @param who
     * @param shipperNo
     * @param shipperName
     */
    public void complementShipmentShipperInfo(Object who, String shipperNo, String shipperName) {
        LOGGER.info("{} complementShipmentShipperInfo:{}, {}", who, shipperNo, shipperName);
        this.orderModel.getShipment().setShipperNo(shipperNo);
        if (null != shipperName) {
            this.orderModel.getShipment().setShipperName(shipperName);
        }
    }

    /**
     * 补全关联单扩展单
     * @param who
     * @param creator
     */
    public void complementRefOrderInfoExtendProps(Object who, ExpressOrderModelCreator creator) {
        LOGGER.info("{} complementRefOrderInfoExtendProps:{}", who, creator.getRefOrder().getExtendProps());
        this.orderModel.getRefOrderInfoDelegate().complementExtendProps(creator);
    }


    /**
     * 补齐订单状态及自定义状态及扩展状态
     *
     * @param who
     * @param modelCreator
     */
    public void complementLASOrderStatus(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementLASOrderStatus:{}", who, modelCreator);
        this.orderModel.transferLASOrderStatus(modelCreator);
    }

    /**
     * 补全拦截信息
     * @param who
     * @param modelCreator
     */
    public void complementIntercept(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementIntercept:{}", who, modelCreator);
        this.orderModel.getIntercept().complementIntercept(modelCreator);
    }

    /**
     * 追加抵扣信息
     * @param who
     * @param creator
     */
    public void appendDeductions(Object who, ExpressOrderModelCreator creator) {
        LOGGER.info("{} appendDeductions:{}", who, creator.getFinanceInfo().getDeductionInfoDtos());
        this.orderModel.getFinance().appendDeductions(creator);
    }

    /**
     * 补全配送信息 - 扩展信息 - 营业厅编码、营业厅名称
     * @param who 调用方
     * @param businessHallNo  营业厅编码
     * @param businessHallName  营业厅名称
     */
    public void complementBusinessHall(Object who, String businessHallNo, String businessHallName) {
        LOGGER.info("{} complementBusinessHall:{},{}", who, businessHallNo, businessHallName);
        if (orderModel.getShipment().getExtendProps() == null) {
            orderModel.getShipment().setExtendProps(new HashMap<>());
        }
        this.orderModel.getShipment().getExtendProps().put(BUSINESS_HALL_NO_KEY, businessHallNo);
        this.orderModel.getShipment().getExtendProps().put(BUSINESS_HALL_NAME_KEY, businessHallName);
    }

    /**
     * 补充客户订单号
     *
     * @param who
     * @param customerOrderNo
     */
    public void complementCustomerOrderNo(@NotNull Object who, @NotNull String customerOrderNo) {
        LOGGER.debug("{} complementCustomerOrderNo:{}", who, customerOrderNo);
        this.orderModel.getChannel().setCustomerOrderNo(customerOrderNo);
    }

    /**
     * 补充财务扩展字段
     *
     * @param who
     * @param
     */
    public void complementFinanceInfoExtendProps(@NotNull Object who, @NotNull Map<String, String> extendProps) {
        LOGGER.debug("{} complementFinanceInfoExtendProps:{}", who, extendProps);
        if(null != orderModel.getFinance().getExtendProps()) {
            this.orderModel.getFinance().getExtendProps().putAll(extendProps);
        }else{
            this.orderModel.getFinance().setExtendProps(extendProps);
        }
    }

    /**
     * 补充揽收方式
     *
     * @param who
     * @param pickupType
     */
    public void complementPickupType(@NotNull Object who, @NotNull PickupTypeEnum pickupType) {
        LOGGER.debug("{} complementPickupType:{}", who, pickupType);
        this.orderModel.getShipment().setPickupType(pickupType);
    }

    /**
     * 补充起始站点
     *
     * @param who
     * @param startStationNo
     * @param startStationName
     */
    public void complementStartStationInfo(@NotNull Object who, @NotNull String startStationNo, String startStationName) {
        LOGGER.debug("{} complementStartStationInfo:startStationNo={},startStationName={}", who, startStationNo,startStationName);
        this.orderModel.getShipment().setStartStationNo(startStationNo);
        this.orderModel.getShipment().setStartStationName(startStationName);
    }

    /**
     * 补充派送方式
     *
     * @param who
     * @param deliveryType
     */
    public void complementDeliveryType(@NotNull Object who, @NotNull DeliveryTypeEnum deliveryType) {
        LOGGER.debug("{} complementDeliveryType:{}", who, deliveryType);
        this.orderModel.getShipment().setDeliveryType(deliveryType);
    }

    /**
     * 补充目的站点
     *
     * @param who
     * @param endStationNo
     * @param endStationName
     */
    public void complementEndStationInfo(@NotNull Object who, @NotNull String endStationNo, String endStationName) {
        LOGGER.debug("{} complementEndStationInfo:endStationNo={},endStationName={}", who, endStationNo, endStationName);
        this.orderModel.getShipment().setEndStationNo(endStationNo);
        this.orderModel.getShipment().setEndStationName(endStationName);
    }


    /**
     * 补全客户退款单号
     * @param who
     * @param customsRefundOrderNo
     */
    public void complementCustomsRefundOrderNo(Object who, String customsRefundOrderNo) {
        LOGGER.info("{} customsRefundOrderNo:{}", who, customsRefundOrderNo);
        this.orderModel.getRefundInfoVo().complementCustomsRefundOrderNo(customsRefundOrderNo);
    }

    /**
     * 补全商户ID
     * @param who
     * @param merchantId
     */
    public void complementMerchantId(Object who, String merchantId) {
        LOGGER.info("{} merchantId:{}", who, merchantId);
        this.orderModel.getRefundInfoVo().complementMerchantId(merchantId);
    }


    /**
     * 补充平台退款单号信息
     * @param who
     * @param refundOrderNo
     */
    public void complementRefundNo(Object who, String refundOrderNo) {
        LOGGER.info("{} refundOrderNo:{}", who, refundOrderNo);
        this.orderModel.getRefundInfoVo().complementRefundOrderNo(refundOrderNo);
    }


    /**
     * 补充渠道退款单号信息
     * @param who
     * @param channelRefundOrderNo
     */
    public void complementChannelRefundNo(Object who, String channelRefundOrderNo) {
        LOGGER.info("{} channelRefundOrderNo:{}", who, channelRefundOrderNo);
        this.orderModel.getRefundInfoVo().complementChannelRefundOrderNo(channelRefundOrderNo);
    }

    /**
     * 补全配送-服务要求
     * @param who
     * @param serviceRequirements
     */
    public void complementShipmentServiceRequirements(Object who, Map<String, String> serviceRequirements) {
        LOGGER.info("{} complementShipmentServiceRequirements: {}", who, serviceRequirements);
        this.orderModel.getShipment().setServiceRequirements(serviceRequirements);
    }

    /**
     * 追加配送-服务要求
     * @param who
     * @param serviceRequirements
     */
    public void appendShipmentServiceRequirements(Object who, Map<String, String> serviceRequirements) {
        LOGGER.info("{} appendShipmentServiceRequirements: {}", who, serviceRequirements);
        if(MapUtils.isEmpty(this.orderModel.getShipment().getServiceRequirements())){
            this.orderModel.getShipment().setServiceRequirements(new HashMap<>());
        }
        this.orderModel.getShipment().getServiceRequirements().putAll(serviceRequirements);
    }


    /**
     * 补全财务信息
     *
     * @param who
     * @param modelCreator
     */
    public void complementFinanceDetails(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementFinanceDetails:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementFinanceDetails(modelCreator);
    }

    /**
     * 补充父单号
     *
     * @param who
     * @param
     */
    public void complementParentOrderNo(@NotNull Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.debug("{} complementParentOrderNo:{}", who, modelCreator.getParentOrderNo());
        this.orderModel.setParentOrderNo(modelCreator.getParentOrderNo());
    }

    /**
     * 补全财务信息-支付截止时间
     *
     * @param who
     * @param payDeadline
     */
    public void complementFinancePayDeadline(Object who, Date payDeadline) {
        LOGGER.info("{} complementFinancePayDeadline:{}", who, payDeadline);
        this.orderModel.getFinance().complementPayDeadline(payDeadline);
    }

    /**
     * 补全财务信息-各种费用
     *
     * @param who
     * @param modelCreator
     */
    public void complementFinanceAmount(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementFinanceAmount:{}", who, JSONUtils.beanToJSONDefault(modelCreator));
        this.orderModel.getFinance().complementFinanceAmount(modelCreator);
    }

    /**
     * 补齐新产品信息（产品名称，类型，父产品）
     *
     * @param who
     * @param modelCreator
     * @param orderModel     用于取快照，修改产品时需要将快照的新产品信息删除
     */
    public void complementNewProduct(Object who, ExpressOrderModelCreator modelCreator, ExpressOrderModel orderModel) {
        LOGGER.info("{} complementNewProduct:{}", who, modelCreator);
        this.orderModel.getProductDelegate().complementNewProduct(modelCreator, orderModel);
    }

    public void complementFulfillment(Object who, ExpressOrderModelCreator creator) {
        LOGGER.info("{} complementFulfillment:{}", who, creator);
        this.orderModel.getFulfillment().update(creator);
    }

    /**
     * 补全跨境报关信息
     * @param who
     * @param modelCreator
     */
    public void complementCustoms(Object who, ExpressOrderModelCreator modelCreator) {
        LOGGER.info("{} complementCustoms:{}", who, modelCreator);
        this.orderModel.getCustoms().complementCustoms(modelCreator);
    }

    /**
     * 补全预占模式
     *
     * @param who
     * @param occupyMode
     */
    public void complementOccupyMode(Object who, Integer occupyMode) {
        LOGGER.info("{} complementOccupyMode:{}", who, occupyMode);
        this.orderModel.getFinance().setOccupyMode(occupyMode);
    }

    /**
     * 补全财务信息-多方计费总额
     *
     * @param who
     * @param costInfoList
     */
    public void complementMultiPartiesTotalAmounts(Object who, List<CostInfo> costInfoList) {
        LOGGER.info("{} complementMultiPartiesTotalAmounts:{}", who, JSONUtils.beanToJSONDefault(costInfoList));
        this.orderModel.getFinance().setMultiPartiesTotalAmounts(costInfoList);
    }

    /**
     *
     * @param who
     * @param operateTime 操作时间
     */
    public void complementOperateTime(Object who, Date operateTime) {
        LOGGER.info("{} operateTime:{}", who, JSONUtils.beanToJSONDefault(operateTime));
        this.orderModel.assignOperateTime(who, operateTime);
    }
}
