package cn.jdl.oms.express.domain.ohs.translator;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.core.model.AdditionPriceInfo;
import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.AgreementInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.BusinessSolutionInfo;
import cn.jdl.oms.core.model.CargoInfo;
import cn.jdl.oms.core.model.ChannelInfo;
import cn.jdl.oms.core.model.ConsigneeInfo;
import cn.jdl.oms.core.model.ConsignorInfo;
import cn.jdl.oms.core.model.CostInfo;
import cn.jdl.oms.core.model.CustomerInfo;
import cn.jdl.oms.core.model.CustomsInfo;
import cn.jdl.oms.core.model.DeductionInfo;
import cn.jdl.oms.core.model.DimensionInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.core.model.FinanceInfo;
import cn.jdl.oms.core.model.FulfillmentInfo;
import cn.jdl.oms.core.model.GoodsInfo;
import cn.jdl.oms.core.model.InterceptInfo;
import cn.jdl.oms.core.model.MoneyInfo;
import cn.jdl.oms.core.model.ProductInfo;
import cn.jdl.oms.core.model.PromotionInfo;
import cn.jdl.oms.core.model.QuantityInfo;
import cn.jdl.oms.core.model.ReturnInfo;
import cn.jdl.oms.core.model.RefOrderInfo;
import cn.jdl.oms.core.model.ShipmentInfo;
import cn.jdl.oms.core.model.VolumeInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.core.model.WeightInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.converter.AttachmentMapper;
import cn.jdl.oms.express.domain.converter.InterceptMapper;
import cn.jdl.oms.express.domain.converter.DeductionMapper;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.ActivityInfoDto;
import cn.jdl.oms.express.domain.dto.AdditionPriceInfoDto;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.AgreementInfoDto;
import cn.jdl.oms.express.domain.dto.AttachmentInfoDto;
import cn.jdl.oms.express.domain.dto.BusinessIdentityDto;
import cn.jdl.oms.express.domain.dto.BusinessSolutionInfoDto;
import cn.jdl.oms.express.domain.dto.CargoInfoDto;
import cn.jdl.oms.express.domain.dto.ChannelInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.dto.CostInfoDto;
import cn.jdl.oms.express.domain.dto.CustomerInfoDto;
import cn.jdl.oms.express.domain.dto.CustomsInfoDto;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.DimensionInfoDto;
import cn.jdl.oms.express.domain.dto.DiscountInfoDto;
import cn.jdl.oms.express.domain.dto.FenceInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.FulfillmentInfoDto;
import cn.jdl.oms.express.domain.dto.GoodsInfoDto;
import cn.jdl.oms.express.domain.dto.InterceptInfoDto;
import cn.jdl.oms.express.domain.dto.LengthInfoDto;
import cn.jdl.oms.express.domain.dto.MoneyInfoDto;
import cn.jdl.oms.express.domain.dto.PointsInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.PromotionInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.dto.RefOrderInfoDto;
import cn.jdl.oms.express.domain.dto.ReturnInfoDto;
import cn.jdl.oms.express.domain.dto.ShipmentInfoDto;
import cn.jdl.oms.express.domain.dto.TicketInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WarehouseInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddressSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.AdministrativeRegionEnum;
import cn.jdl.oms.express.domain.spec.dict.ContactlessTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.CoordinateTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.CurrencyCodeEnum;
import cn.jdl.oms.express.domain.spec.dict.CustomsStatusEnum;
import cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.IdentityTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.InitiatorTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.LengthTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SettlementTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.TransportTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.VolumeTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.spec.dict.WeightTypeEnum;
import cn.jdl.oms.express.domain.vo.Money;
import cn.jdl.oms.express.model.ModifyExpressOrderRequest;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessSceneEnum;
import cn.jdl.oms.express.shared.common.dict.ModifiedFieldValueEnum;
import cn.jdl.oms.express.shared.common.dict.ModifyItemConfigEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.ModifyClearUtil;
import com.google.common.collect.Lists;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ProjectName：jdl-oms-express-c2c-infrastructure
 * @Package： cn.jdl.oms.express.domain.ohs.translator
 * @ClassName: ModifyExpressOrderTranslator
 * @Description: 修改环节领域dto到model对象转换
 * @Author： liyong549
 * @CreateDate 2021/3/27 15:56
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version：V1.0
 */
@Translator
public class ModifyExpressOrderTranslator {

    private static final Logger LOGGER = LoggerFactory.getLogger(ModifyExpressOrderTranslator.class);

    /**
     * 纯配订单修改申请数据转订单领域模型
     *
     * @param requestProfile
     * @param request
     * @return
     * <AUTHOR>
     */
    public ExpressOrderModel translator(RequestProfile requestProfile, ModifyExpressOrderRequest request) throws ParseException {
        //订单中心纯配领域模型
        ExpressOrderModelCreator creator = new ExpressOrderModelCreator();
        //修改时需清空的字段初始化
        creator.setClearFields(new ArrayList<>());
        //业务身份
        this.businessIdentityOf(creator, request.getBusinessIdentity());
        //业务场景
        creator.setBusinessScene(BusinessSceneEnum.MODIFY.getCode());
        //订单号
        creator.setOrderNo(request.getOrderNo());
        //业务单号
        creator.setCustomOrderNo(request.getCustomOrderNo());
        //客户信息
        Optional.ofNullable(request.getCustomerInfo()).ifPresent(customerInfo ->
                this.customerOf(creator, customerInfo));
        //渠道信息
        Optional.ofNullable(request.getChannelInfo()).ifPresent(channelInfo ->
                this.channelOf(creator, channelInfo));
        //产品信息/增值产品信息
        Optional.ofNullable(request.getProductInfos()).ifPresent(productInfos ->
                this.productInfosOf(creator, productInfos));
        //发货人信息
        Optional.ofNullable(request.getConsignorInfo()).ifPresent(consignorInfo ->
                this.consignorOf(creator, consignorInfo));
        //收货人信息
        Optional.ofNullable(request.getConsigneeInfo()).ifPresent(consigneeInfo ->
                this.consigneeOf(creator, consigneeInfo));
        //货品信息
        Optional.ofNullable(request.getCargoInfos()).ifPresent(cargoInfos ->
                this.cargoInfosOf(creator, cargoInfos));
        //配送信息
        this.shipmentInfoOf(creator, request.getShipmentInfo());
        //财务信息
        Optional.ofNullable(request.getFinanceInfo()).ifPresent(financeInfo ->
                this.financeInfoOf(creator, financeInfo));
        //营销信息
        Optional.ofNullable(request.getPromotionInfo()).ifPresent(promotionInfo ->
                this.promotionInfoOf(creator, promotionInfo));
        Optional.ofNullable(request.getInitiatorType()).ifPresent(initiatoryType ->
                creator.setInitiatorType(InitiatorTypeEnum.of(initiatoryType)));
        //修改操作人
        creator.setOperator(request.getOperator());
        //订单备注
        creator.setRemark(request.getRemark());
        //订单主档扩展字段
        Optional.ofNullable(request.getExtendProps()).ifPresent(extendProps ->
                creator.setExtendProps(request.getExtendProps()));

        //校验传入的修改类型是否属于当前枚举类型，不属于当前枚举类型则进行打点记录
        this.checkModifiedFields(request);

        //对订单的集合字段标记操作类型
        Optional.ofNullable(request.getModifiedFields()).ifPresent(modifiedFields ->
                creator.setModifiedFields(request.getModifiedFields()));
        //商品信息
        Optional.ofNullable(request.getGoodsInfos()).ifPresent(goodsInfos ->
                this.goodsInfosOf(creator, goodsInfos));
        //协议信息
        Optional.ofNullable(request.getAgreementInfos()).ifPresent(agreementInfos ->
            this.agreementInfosOf(creator, agreementInfos));
        //关联单信息
        Optional.ofNullable(request.getRefOrderInfo()).ifPresent(refOrderInfo ->
                this.refOrderOf(creator, refOrderInfo));

        //退货信息
        Optional.ofNullable(request.getReturnInfo()).ifPresent(returnInfo ->
                this.returnInfoOf(creator,returnInfo));
        //履约信息
        Optional.ofNullable(request.getFulfillmentInfo()).ifPresent(fulfillmentInfo ->
                this.fulfillmentOf(creator, fulfillmentInfo));

        //订单标识
        Optional.ofNullable(request.getOrderSign()).ifPresent(orderSign ->
                creator.setOrderSign(request.getOrderSign()));
        //拦截信息
        Optional.ofNullable(request.getInterceptInfo()).ifPresent(interceptInfo ->
                this.interceptInfoOf(creator, interceptInfo));

        //跨境报关信息
        Optional.ofNullable(request.getCustomsInfo()).ifPresent(customsInfo ->
            this.customsInfoOf(creator, customsInfo));

        //附件列表
        Optional.ofNullable(request.getAttachmentInfos()).ifPresent(attachmentInfos ->
            this.attachmentInfosOf(creator, attachmentInfos));

        //订单总体积
        Optional.ofNullable(request.getOrderVolume()).ifPresent(orderVolumeInfo ->
            this.orderVolumeInfoOf(creator, orderVolumeInfo));

        //订单总重量
        Optional.ofNullable(request.getOrderWeight()).ifPresent(orderWeightInfo ->
            this.orderWeightInfoOf(creator, orderWeightInfo));

        //订单总净重
        Optional.ofNullable(request.getOrderNetWeight()).ifPresent(orderNetWeightInfo ->
            this.orderNetWeightInfoOf(creator, orderNetWeightInfo));

        //复核体积
        Optional.ofNullable(request.getRecheckVolume()).ifPresent(recheckVolume ->
                this.recheckVolumeOf(creator, recheckVolume));

        //复核重量
        Optional.ofNullable(request.getRecheckWeight()).ifPresent(recheckWeight ->
                this.recheckWeightOf(creator, recheckWeight));

        //解决方案信息
        Optional.ofNullable(request.getBusinessSolutionInfo()).ifPresent(businessSolutionInfo ->
                this.businessSolutionInfoOf(creator, businessSolutionInfo));

        return ExpressOrderModel.expressModelOf(creator).withRequestProfile(requestProfile);
    }

    private void returnInfoOf(ExpressOrderModelCreator creator, ReturnInfo returnInfo){
        Optional.ofNullable(returnInfo).ifPresent(returnInfoReq -> {
            ReturnInfoDto returnInfoDto = new ReturnInfoDto();
            returnInfoDto.setReturnType(returnInfoReq.getReturnType());
            Optional.ofNullable(returnInfo.getReturnConsigneeInfo()).ifPresent(consigneeInfo -> {
                //收货人
                ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
                //收货人姓名
                consigneeInfoDto.setConsigneeName(consigneeInfo.getConsigneeName());
                //收货人联系手机
                consigneeInfoDto.setConsigneeMobile(consigneeInfo.getConsigneeMobile());
                //收货人联系电话
                consigneeInfoDto.setConsigneePhone(consigneeInfo.getConsigneePhone());
                //收货人联系邮编
                consigneeInfoDto.setConsigneeZipCode(consigneeInfo.getConsigneeZipCode());
                //收货地公司
                consigneeInfoDto.setConsigneeCompany(consigneeInfo.getConsigneeCompany());
                //收货人国家编号
                consigneeInfoDto.setConsigneeNationNo(consigneeInfo.getConsigneeNationNo());
                //收货人国家名称
                consigneeInfoDto.setConsigneeNation(consigneeInfo.getConsigneeNation());
                //收货人证件类型
                consigneeInfoDto.setConsigneeIdType(IdentityTypeEnum.of(consigneeInfo.getConsigneeIdType()));
                //收货人证件号码
                consigneeInfoDto.setConsigneeIdNo(consigneeInfo.getConsigneeIdNo());
                //收货人证件姓名
                consigneeInfoDto.setConsigneeIdName(consigneeInfo.getConsigneeIdName());
                //收货人地址信息
                Optional.ofNullable(consigneeInfo.getAddressInfo()).ifPresent(addressInfo ->
                        this.consigneeAddressOf(consigneeInfoDto, addressInfo));
                //收货地编码（腾讯云仓使用）
                consigneeInfoDto.setDeliveryPlaceCode(consigneeInfo.getDeliveryPlaceCode());
                //收货仓信息
                Optional.ofNullable(consigneeInfo.getReceiveWarehouse()).ifPresent(warehouseInfo ->
                        this.consigneeReceiveWarehouseOf(consigneeInfoDto, warehouseInfo));
                //数据传输对象防腐转换对象
                returnInfoDto.setReturnConsigneeInfo(consigneeInfoDto);
            });
            creator.setReturnInfoDto(returnInfoDto);
        });
    }

    /**
     * 业务身份转换订单领域模型业务身份类型
     *
     * @param creator
     * @param businessIdentity
     */
    private void businessIdentityOf(ExpressOrderModelCreator creator, BusinessIdentity businessIdentity) {
        BusinessIdentityDto businessIdentityDto = new BusinessIdentityDto();
        businessIdentityDto.setBusinessUnit(businessIdentity.getBusinessUnit());
        businessIdentityDto.setBusinessType(businessIdentity.getBusinessType());
        businessIdentityDto.setBusinessScene(BusinessSceneEnum.MODIFY.getCode());
        businessIdentityDto.setBusinessStrategy(businessIdentity.getBusinessStrategy());
        businessIdentityDto.setFulfillmentUnit(businessIdentity.getFulfillmentUnit());
        creator.setBusinessIdentity(businessIdentityDto);
    }


    /**
     * 申请接单入参客户信息数据对象转换成接单领域模型
     *
     * @param creator
     * @param customerInfo
     */
    private void customerOf(ExpressOrderModelCreator creator, CustomerInfo customerInfo) {
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        //青龙业务主号
        customerInfoDto.setAccountNo(customerInfo.getAccountNo());
        customerInfoDto.setAccountName(customerInfo.getAccountName());
        //事业部编码
        customerInfoDto.setAccount2No(customerInfo.getAccount2No());
        customerInfoDto.setAccount2Name(customerInfo.getAccount2Name());
        //ECP编码
        customerInfoDto.setAccount3No(customerInfo.getAccount3No());
        customerInfoDto.setAccount3Name(customerInfo.getAccount3Name());
        //数据传输对象防腐转换对象
        creator.setCustomerInfo(customerInfoDto);
    }

    /**
     * 申请接单入参渠道数据对象转换成接单领域模型
     *
     * @param creator
     * @param channelInfo
     */
    private void channelOf(ExpressOrderModelCreator creator, ChannelInfo channelInfo) {
        //接受申请处理数据传输对象
        ChannelInfoDto channelInfoDto = new ChannelInfoDto();
        //渠道操作时间
        channelInfoDto.setChannelOperateTime(channelInfo.getChannelOperateTime());
        //渠道订单号
        channelInfoDto.setChannelOrderNo(channelInfo.getChannelOrderNo());
        //系统调用来源
        channelInfoDto.setSystemCaller(SystemCallerEnum.of(channelInfo.getSystemCaller()));
        //二级系统调用来源
        channelInfoDto.setSystemSubCaller(channelInfo.getSystemSubCaller());
        //获取渠道扩展字段
        channelInfoDto.setExtendProps(channelInfo.getExtendProps());
        //领域模型对象防腐转换对象
        creator.setChannelInfo(channelInfoDto);
    }

    /**
     * 申请接单入参产品信息数据对象转换成接单领域模型
     *
     * @param creator
     * @param productInfos
     */
    private void productInfosOf(ExpressOrderModelCreator creator, List<ProductInfo> productInfos) {
        //产品集合
        List<ProductInfoDto> productInfoDtos = Lists.newArrayListWithCapacity(productInfos.size());
        //遍历当前产品明细
        productInfos.forEach(productInfo -> {
            //产品信息
            ProductInfoDto productInfoDto = new ProductInfoDto();
            //产品编码
            productInfoDto.setProductNo(productInfo.getProductNo());
            //产品类型
            productInfoDto.setProductType(productInfo.getProductType());
            //产品关系(所属主产品编码)
            productInfoDto.setParentNo(productInfo.getParentNo());
            //产品要素属性
            productInfoDto.setProductAttrs(productInfo.getProductAttrs());
            //扩展字段说明
            productInfoDto.setExtendProps(productInfo.getExtendProps());
            productInfoDto.setProductName(productInfo.getProductName());
            //产品信息
            productInfoDtos.add(productInfoDto);

        });
        creator.setProducts(productInfoDtos);
    }

    /**
     * 申请接单入参发货人数据对象转换成接单领域模型
     *
     * @param creator
     * @param consignorInfo
     */
    private void consignorOf(ExpressOrderModelCreator creator, ConsignorInfo consignorInfo) {
        //发货人
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();
        //发货人姓名
        consignorInfoDto.setConsignorName(consignorInfo.getConsignorName());
        //发货人联系手机号
        consignorInfoDto.setConsignorMobile(consignorInfo.getConsignorMobile());
        //发货人联系电话
        consignorInfoDto.setConsignorPhone(consignorInfo.getConsignorPhone());
        //发货人证件类型
        if (ModifyClearUtil.isNeedClear(consignorInfo.getConsignorIdType())) {
            //如果需要置空，赋值null，并记录下该属性
            consignorInfoDto.setConsignorIdType(null);
            creator.getClearFields().add(ModifyItemConfigEnum.CONSIGNOR_ID_TYPE.getCode());
        } else {
            consignorInfoDto.setConsignorIdType(IdentityTypeEnum.of(consignorInfo.getConsignorIdType()));
        }
        //发货人证件号码
        consignorInfoDto.setConsignorIdNo(consignorInfo.getConsignorIdNo());
        //发货人证件姓名
        consignorInfoDto.setConsignorIdName(consignorInfo.getConsignorIdName());
        //发货人公司
        consignorInfoDto.setConsignorCompany(consignorInfo.getConsignorCompany());
        //发货人国家编码
        consignorInfoDto.setConsignorNationNo(consignorInfo.getConsignorNationNo());
        //发货人国家名称
        consignorInfoDto.setConsignorNation(consignorInfo.getConsignorNation());
        //发货地编码
        consignorInfoDto.setConsignorZipCode(consignorInfo.getConsignorZipCode());
        //发货人地址信息
        Optional.ofNullable(consignorInfo.getAddressInfo()).ifPresent(addressInfo -> {
            this.consignorAddressOf(consignorInfoDto, addressInfo);
        });
        //发货仓信息
        Optional.ofNullable(consignorInfo.getCustomerWarehouse()).ifPresent(warehouseInfo -> {
            this.consignorCustomerWarehouseOf(consignorInfoDto, warehouseInfo);
        });
        //收货人公司
        consignorInfoDto.setConsignorCompany(consignorInfo.getConsignorCompany());
        //英文发件人姓名
        consignorInfoDto.setConsignorEnName(consignorInfo.getConsignorEnName());
        //数据传输对象防腐转换对象
        creator.setConsignorInfo(consignorInfoDto);
    }

    /**
     * 申请接单入参仓库信息数据对象转换成接单领域模型
     *
     * @param consignorInfoDto
     * @param warehouseInfo
     */
    private void consignorCustomerWarehouseOf(ConsignorInfoDto consignorInfoDto, WarehouseInfo warehouseInfo) {
        WarehouseInfoDto warehouseInfoDto = new WarehouseInfoDto();
        warehouseInfoDto.setWarehouseNo(warehouseInfo.getWarehouseNo());
        warehouseInfoDto.setWarehouseName(warehouseInfo.getWarehouseName());
        warehouseInfoDto.setWarehouseSource(warehouseInfo.getWarehouseSource());
        consignorInfoDto.setCustomerWarehouseDto(warehouseInfoDto);
    }

    /**
     * 申请接单入参收货人数据对象转换成接单领域模型
     *
     * @param creator
     * @param consigneeInfo
     */
    private void consigneeOf(ExpressOrderModelCreator creator, ConsigneeInfo consigneeInfo) {
        //收货人
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        //收货人姓名
        consigneeInfoDto.setConsigneeName(consigneeInfo.getConsigneeName());
        //收货人联系手机号
        consigneeInfoDto.setConsigneeMobile(consigneeInfo.getConsigneeMobile());
        //收货人联系电话
        consigneeInfoDto.setConsigneePhone(consigneeInfo.getConsigneePhone());
        //收货人证件类型
        if (ModifyClearUtil.isNeedClear(consigneeInfo.getConsigneeIdType())) {
            //如果需要置空，赋值null，并记录下该属性
            consigneeInfo.setConsigneeIdType(null);
            creator.getClearFields().add(ModifyItemConfigEnum.CONSIGNEE_ID_TYPE.getCode());
        } else {
            consigneeInfoDto.setConsigneeIdType(IdentityTypeEnum.of(consigneeInfo.getConsigneeIdType()));
        }
        //收货人证件号码
        consigneeInfoDto.setConsigneeIdNo(consigneeInfo.getConsigneeIdNo());
        //收货人证件姓名
        consigneeInfoDto.setConsigneeIdName(consigneeInfo.getConsigneeIdName());
        //收货人公司
        consigneeInfoDto.setConsigneeCompany(consigneeInfo.getConsigneeCompany());
        //收货人国家编码
        consigneeInfoDto.setConsigneeNationNo(consigneeInfo.getConsigneeNationNo());
        //收货人国家名称
        consigneeInfoDto.setConsigneeNation(consigneeInfo.getConsigneeNation());
        //收货地编码
        consigneeInfoDto.setConsigneeZipCode(consigneeInfo.getConsigneeZipCode());
        //收货人地址信息
        Optional.ofNullable(consigneeInfo.getAddressInfo()).ifPresent(addressInfo -> {
            this.consigneeAddressOf(consigneeInfoDto, addressInfo);
        });
        //
        consigneeInfoDto.setConsigneeCompany(consigneeInfo.getConsigneeCompany());
        //收货仓信息
        Optional.ofNullable(consigneeInfo.getReceiveWarehouse()).ifPresent(warehouseInfo -> {
            this.consigneeReceiveWarehouseOf(consigneeInfoDto, warehouseInfo);
        });
        //扩展属性
        consigneeInfoDto.setExtendProps(consigneeInfo.getExtendProps());
        //收件人邮箱
        consigneeInfoDto.setConsigneeEmail(consigneeInfo.getConsigneeEmail());
        //数据传输对象防腐转换对象
        creator.setConsigneeInfo(consigneeInfoDto);
    }

    /**
     * 申请接单入参货品信息数据对象转换成接单领域模型
     *
     * @param creator
     * @param cargoInfos
     */
    private void cargoInfosOf(ExpressOrderModelCreator creator, List<CargoInfo> cargoInfos) {
        //货品集合
        List<CargoInfoDto> cargoInfoDtos = Lists.newArrayListWithCapacity(cargoInfos.size());
        cargoInfos.forEach(cargoInfo -> {
            //货品信息
            CargoInfoDto cargoInfoDto = new CargoInfoDto();
            //货品编码
            cargoInfoDto.setCargoNo(cargoInfo.getCargoNo());
            //货品名称
            cargoInfoDto.setCargoName(cargoInfo.getCargoName());
            //货品类型
            cargoInfoDto.setCargoType(cargoInfo.getCargoType());
            //序列号
            cargoInfoDto.setSerialInfos(cargoInfo.getSerialInfos());
            //货品体积
            Optional.ofNullable(cargoInfo.getCargoVolume()).ifPresent(volumeInfo -> {
                VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
                volumeInfoDto.setValue(volumeInfo.getValue());
                volumeInfoDto.setUnit(VolumeTypeEnum.of(volumeInfo.getUnit()));
                cargoInfoDto.setCargoVolume(volumeInfoDto);
            });

            Optional.ofNullable(cargoInfo.getCargoDimension()).ifPresent(cargoDimension -> {
                DimensionInfoDto dimensionInfo = new DimensionInfoDto();
                dimensionInfo.setLength(cargoDimension.getLength());
                dimensionInfo.setWidth(cargoDimension.getWidth());
                dimensionInfo.setHeight(cargoDimension.getHeight());
                dimensionInfo.setUnit(StringUtils.isNotBlank(cargoDimension.getUnit())
                        ? LengthTypeEnum.of(cargoDimension.getUnit()) : null);

                cargoInfoDto.setDimensionInfo(dimensionInfo);
            });

            //货品重量
            Optional.ofNullable(cargoInfo.getCargoWeight()).ifPresent(weightInfo -> {
                WeightInfoDto weightInfoDto = new WeightInfoDto();
                weightInfoDto.setValue(weightInfo.getValue());
                weightInfoDto.setUnit(WeightTypeEnum.of(weightInfo.getUnit()));
                cargoInfoDto.setCargoWeight(weightInfoDto);
            });

            //货品数量
            Optional.ofNullable(cargoInfo.getCargoQuantity()).ifPresent(cargoQuantity -> {
                QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
                quantityInfoDto.setValue(cargoQuantity.getValue());
                quantityInfoDto.setUnit(cargoQuantity.getUnit());
                cargoInfoDto.setCargoQuantityInfo(quantityInfoDto);
            });
            //货品备注
            cargoInfoDto.setCargoRemark(cargoInfo.getCargoRemark());
            //清真易污染标识
            cargoInfoDto.setPolluteSign(cargoInfo.getPolluteSign());
            //附件信息
            Optional.ofNullable(cargoInfo.getCargoAttachmentInfos()).ifPresent(attachmentInfos -> {
                //遍历附件
                List<AttachmentInfoDto> attachmentInfoDtos = attachmentInfos.stream().map(attachmentInfo -> {
                    AttachmentInfoDto attachmentInfoDto = new AttachmentInfoDto();
                    attachmentInfoDto.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    attachmentInfoDto.setAttachmentName(attachmentInfo.getAttachmentName());
                    attachmentInfoDto.setAttachmentType(attachmentInfo.getAttachmentType());
                    attachmentInfoDto.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    attachmentInfoDto.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    attachmentInfoDto.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachmentInfoDto;
                }).collect(Collectors.toList());
                //附件集
                cargoInfoDto.setCargoAttachmentInfos(attachmentInfoDtos);
            });
            //是否易损
            cargoInfoDto.setCargoVulnerable(cargoInfo.getCargoVulnerable());
            //货品标识
            cargoInfoDto.setCargoSign(cargoInfo.getCargoSign());
            //货品维度增值服务列表
            if (CollectionUtils.isNotEmpty(cargoInfo.getProductInfos())) {
                List<ProductInfoDto> productInfoDtos = new ArrayList<>(cargoInfo.getProductInfos().size());
                cargoInfo.getProductInfos().forEach(productInfo -> {
                    if (productInfo != null) {
                        ProductInfoDto dto = new ProductInfoDto();
                        //主产品编码
                        dto.setParentNo(productInfo.getParentNo());
                        //产品编码
                        dto.setProductNo(productInfo.getProductNo());
                        //产品类型
                        dto.setProductType(productInfo.getProductType());
                        //产品要素属性
                        dto.setProductAttrs(productInfo.getProductAttrs());
                        //扩展字段
                        dto.setExtendProps(productInfo.getExtendProps());
                        //名称
                        dto.setProductName(productInfo.getProductName());
                        productInfoDtos.add(dto);
                    }
                });
                cargoInfoDto.setCargoProductInfos(productInfoDtos);
            }
            //货品维度扩展信息
            cargoInfoDto.setExtendProps(cargoInfo.getExtendProps());

            //隐私货品展示信息
            cargoInfoDto.setPrivacyCargoName(cargoInfo.getPrivacyCargoName());

            //货品信息
            cargoInfoDtos.add(cargoInfoDto);

        });
        creator.setCargoInfos(cargoInfoDtos);
    }

    /**
     * 申请接单入参配送信息数据对象转换成接单领域模型
     *
     * @param creator
     * @param shipmentInfo
     */
    private void shipmentInfoOf(ExpressOrderModelCreator creator, ShipmentInfo shipmentInfo) throws ParseException {
        if (shipmentInfo != null) {
            //配送信息数据
            ShipmentInfoDto shipmentInfoDto = new ShipmentInfoDto();
            //预计送达时间
            if (DateUtils.isEraDateTime(shipmentInfo.getPlanDeliveryTime())) {
                shipmentInfoDto.setPlanDeliveryTime(null);
                creator.getClearFields().add(ModifyItemConfigEnum.PLAN_DELIVERY_TIME.getCode());
            } else {
                shipmentInfoDto.setPlanDeliveryTime(shipmentInfo.getPlanDeliveryTime());
            }
            //期望提货开始时间
            if (DateUtils.isEraDateTime(shipmentInfo.getExpectPickupStartTime())) {
                shipmentInfoDto.setExpectPickupStartTime(null);
                creator.getClearFields().add(ModifyItemConfigEnum.EXPECT_PICKUP_START_TIME.getCode());
            } else {
                shipmentInfoDto.setExpectPickupStartTime(shipmentInfo.getExpectPickupStartTime());
            }
            //期望提货结束时间
            if (DateUtils.isEraDateTime(shipmentInfo.getExpectPickupEndTime())) {
                shipmentInfoDto.setExpectPickupEndTime(null);
                creator.getClearFields().add(ModifyItemConfigEnum.EXPECT_PICKUP_END_TIME.getCode());
            } else {
                shipmentInfoDto.setExpectPickupEndTime(shipmentInfo.getExpectPickupEndTime());
            }
            //todo 持久层只支持修改不支持清空 需要与持久层对齐
            // 期望派送开始时间
            if (DateUtils.isEraDateTime(shipmentInfo.getExpectDeliveryStartTime())) {
                shipmentInfoDto.setExpectDeliveryStartTime(null);
                creator.getClearFields().add(ModifyItemConfigEnum.EXPECT_DELIVERY_START_TIME.getCode());
            } else {
                shipmentInfoDto.setExpectDeliveryStartTime(shipmentInfo.getExpectDeliveryStartTime());
            }
            // 期望派送结束时间
            if (DateUtils.isEraDateTime(shipmentInfo.getExpectDeliveryEndTime())) {
                shipmentInfoDto.setExpectDeliveryEndTime(null);
                creator.getClearFields().add(ModifyItemConfigEnum.EXPECT_DELIVERY_END_TIME.getCode());
            } else {
                shipmentInfoDto.setExpectDeliveryEndTime(shipmentInfo.getExpectDeliveryEndTime());
            }
            //揽收方式
            if (ModifyClearUtil.isNeedClear(shipmentInfo.getPickupType())) {
                shipmentInfoDto.setPickupType(null);
                creator.getClearFields().add(ModifyItemConfigEnum.PICKUP_TYPE.getCode());
            } else {
                shipmentInfoDto.setPickupType(PickupTypeEnum.of(shipmentInfo.getPickupType()));
            }
            //派送方式
            if (ModifyClearUtil.isNeedClear(shipmentInfo.getDeliveryType())) {
                shipmentInfoDto.setDeliveryType(null);
                creator.getClearFields().add(ModifyItemConfigEnum.DELIVERY_TYPE.getCode());
            } else {
                shipmentInfoDto.setDeliveryType(DeliveryTypeEnum.of(shipmentInfo.getDeliveryType()));
            }
            //运输类型 ,1-航空、2-陆运
            if (ModifyClearUtil.isNeedClear(shipmentInfo.getTransportType())) {
                shipmentInfoDto.setTransportType(null);
                creator.getClearFields().add(ModifyItemConfigEnum.TRANSPORT_TYPE.getCode());
            } else {
                shipmentInfoDto.setTransportType(TransportTypeEnum.of(shipmentInfo.getTransportType()));
            }
            //温层要求（来源产品中心）
            shipmentInfoDto.setWarmLayer(WarmLayerEnum.fromCode(shipmentInfo.getWarmLayer()));
            //始发站编码
            shipmentInfoDto.setStartStationNo(shipmentInfo.getStartStationNo());
            //始发站名称
            shipmentInfoDto.setStartStationName(shipmentInfo.getStartStationName());
            //目的站编码
            shipmentInfoDto.setEndStationNo(shipmentInfo.getEndStationNo());
            //目的站名称
            shipmentInfoDto.setEndStationName(shipmentInfo.getEndStationName());
            //预计接单时间
            if (DateUtils.isEraDateTime(shipmentInfo.getPlanReceiveTime())) {
                shipmentInfoDto.setPlanReceiveTime(null);
                creator.getClearFields().add(ModifyItemConfigEnum.PLAN_RECEIVE_TIME.getCode());
            } else {
                shipmentInfoDto.setPlanReceiveTime(shipmentInfo.getPlanReceiveTime());
            }
            //无接触收货方式 1-物业代收；2-门卫代收；3-小区门口自取；4-指定地点存放；
            if (ModifyClearUtil.isNeedClear(shipmentInfo.getContactlessType())) {
                shipmentInfoDto.setContactlessType(null);
                creator.getClearFields().add(ModifyItemConfigEnum.CONTACTLESS_TYPE.getCode());
            } else {
                shipmentInfoDto.setContactlessType(ContactlessTypeEnum.of(shipmentInfo.getContactlessType()));
            }
            //指定地点
            shipmentInfoDto.setAssignedAddress(shipmentInfo.getAssignedAddress());
            //物流服务要求
            shipmentInfoDto.setServiceRequirements(shipmentInfo.getServiceRequirements());
            //收货偏好
            shipmentInfoDto.setReceivingPreference(shipmentInfo.getReceivingPreference());
            //扩展信息
            shipmentInfoDto.setExtendProps(shipmentInfo.getExtendProps());
            //起始分拣中心
            shipmentInfoDto.setStartCenterNo(shipmentInfo.getStartCenterNo());
            //目的分拣中心
            shipmentInfoDto.setEndCenterNo(shipmentInfo.getEndCenterNo());
            //承运商编码
            shipmentInfoDto.setShipperNo(shipmentInfo.getShipperNo());
            //承运商名称
            shipmentInfoDto.setShipperName(shipmentInfo.getShipperName());
            //承运商类型
            shipmentInfoDto.setShipperType(shipmentInfo.getShipperType());
            //预计派货开始时间
            shipmentInfoDto.setExpectDispatchStartTime(shipmentInfo.getExpectDispatchStartTime());
            //预计派货结束时间
            shipmentInfoDto.setExpectDispatchEndTime(shipmentInfo.getExpectDispatchEndTime());
            // 揽收三级网点类型
            shipmentInfoDto.setStartStationTypeL3(shipmentInfo.getStartStationTypeL3());
            // 派送三级网点类型
            shipmentInfoDto.setEndStationTypeL3(shipmentInfo.getEndStationTypeL3());
            //配送要求
            creator.setShipmentInfo(shipmentInfoDto);
        }
    }

    /**
     * 申请接单入参配送信息数据对象转换成接单领域模型
     *
     * @param creator
     * @param financeInfo
     */
    private void financeInfoOf(ExpressOrderModelCreator creator, FinanceInfo financeInfo) {
        //交易费用信息
        FinanceInfoDto financeInfoDto = new FinanceInfoDto();
        //结算方式
        if (ModifyClearUtil.isNeedClear(financeInfo.getSettlementType())) {
            financeInfoDto.setSettlementType(null);
            creator.getClearFields().add(ModifyItemConfigEnum.SETTLEMENT_TYPE.getCode());
        } else {
            financeInfoDto.setSettlementType(SettlementTypeEnum.of(financeInfo.getSettlementType()));
        }
        //预估费用
        Optional.ofNullable(financeInfo.getEstimateAmount()).ifPresent(estimateAmount -> {
            MoneyInfoDto estimateAmountDto = new MoneyInfoDto();
            estimateAmountDto.setAmount(estimateAmount.getAmount());
            estimateAmountDto.setCurrencyCode(CurrencyCodeEnum.of(estimateAmount.getCurrencyCode()));
            financeInfoDto.setEstimateAmount(estimateAmountDto);
        });

        //预估财务信息
        Optional.ofNullable(financeInfo.getEstimateFinanceInfo()).ifPresent(estimateFinanceInfo -> {
            FinanceInfoDto estimateFinanceInfoDto = new FinanceInfoDto();
            // 预估-折前金额
            Optional.ofNullable(estimateFinanceInfo.getPreAmount()).ifPresent(estimatePreAmount -> {
                estimateFinanceInfoDto.setPreAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimatePreAmount));
            });
            // 预估-折后金额
            Optional.ofNullable(estimateFinanceInfo.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                estimateFinanceInfoDto.setDiscountAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimateDiscountAmount));
            });
            // 预估-计费重量
            Optional.ofNullable(estimateFinanceInfo.getBillingWeight()).ifPresent(estimateBillingWeight -> {
                WeightInfoDto estimateBillingWeightInfoDto = new WeightInfoDto();
                estimateBillingWeightInfoDto.setValue(estimateBillingWeight.getValue());
                estimateBillingWeightInfoDto.setUnit(WeightTypeEnum.of(estimateBillingWeight.getUnit()));
                estimateFinanceInfoDto.setBillingWeight(estimateBillingWeightInfoDto);
            });
            // 预估-计费体积
            Optional.ofNullable(estimateFinanceInfo.getBillingVolume()).ifPresent(estimateBillingVolume -> {
                VolumeInfoDto estimateBillingVolumeInfoDto = new VolumeInfoDto();
                estimateBillingVolumeInfoDto.setValue(estimateBillingVolume.getValue());
                estimateBillingVolumeInfoDto.setUnit(VolumeTypeEnum.of(estimateBillingVolume.getUnit()));
                estimateFinanceInfoDto.setBillingVolume(estimateBillingVolumeInfoDto);
            });
            // 预估-加价后总金额
            Optional.ofNullable(estimateFinanceInfo.getTotalAdditionAmount()).ifPresent(totalAdditionAmount -> {
                estimateFinanceInfoDto.setTotalAdditionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(totalAdditionAmount));
            });
            // 预估-费用明细
            Optional.ofNullable(estimateFinanceInfo.getFinanceDetailInfos()).ifPresent(estimateFinanceDetailInfos -> {
                // 预估-费用明细信息
                List<FinanceDetailInfoDto> estimateFinanceDetailInfoDtoList = Lists.newArrayListWithCapacity(estimateFinanceDetailInfos.size());
                estimateFinanceDetailInfos.forEach(estimateFinanceDetailInfo -> {

                    // 预估-费用明细对象
                    FinanceDetailInfoDto estimateFinanceDetailInfoDto = new FinanceDetailInfoDto();
                    // 预估-费用编号
                    estimateFinanceDetailInfoDto.setCostNo(estimateFinanceDetailInfo.getCostNo());
                    // 预估-费用名称
                    estimateFinanceDetailInfoDto.setCostName(estimateFinanceDetailInfo.getCostName());
                    // 预估-费用产品编码
                    estimateFinanceDetailInfoDto.setProductNo(estimateFinanceDetailInfo.getProductNo());
                    // 预估-费用产品名称
                    estimateFinanceDetailInfoDto.setProductName(estimateFinanceDetailInfo.getProductName());
                    // 预估-折扣信息
                    if (CollectionUtils.isNotEmpty(estimateFinanceDetailInfo.getDiscountInfos())) {
                        List<DiscountInfoDto> discountInfoDtos = new ArrayList<>(estimateFinanceDetailInfo.getDiscountInfos().size());
                        estimateFinanceDetailInfo.getDiscountInfos().forEach(estimateDiscountInfo -> {
                            // 预估-折扣信息对象
                            DiscountInfoDto estimateDiscountInfoDto = new DiscountInfoDto();
                            // 预估-折扣码
                            estimateDiscountInfoDto.setDiscountNo(estimateDiscountInfo.getDiscountNo());
                            // 预估-折扣类型
                            estimateDiscountInfoDto.setDiscountType(estimateDiscountInfo.getDiscountType());
                            if (estimateDiscountInfo.getDiscountedAmount() != null) {
                                // 预估-折扣金额
                                Money estimateDiscountedAmount = new Money();
                                estimateDiscountedAmount.setAmount(estimateDiscountInfo.getDiscountedAmount().getAmount());
                                estimateDiscountedAmount.setCurrency(CurrencyCodeEnum.of(estimateDiscountInfo.getDiscountedAmount().getCurrencyCode()));
                                estimateDiscountInfoDto.setDiscountedAmount(estimateDiscountedAmount);
                            }
                            discountInfoDtos.add(estimateDiscountInfoDto);
                        });
                        estimateFinanceDetailInfoDto.setDiscountInfoDtos(discountInfoDtos);
                    }

                    // 预估-折前金额
                    Optional.ofNullable(estimateFinanceDetailInfo.getPreAmount()).ifPresent(estimatePreAmount -> {
                        estimateFinanceDetailInfoDto.setPreAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimatePreAmount));
                    });
                    // 预估-折后金额
                    Optional.ofNullable(estimateFinanceDetailInfo.getDiscountAmount()).ifPresent(estimateDiscountAmount -> {
                        estimateFinanceDetailInfoDto.setDiscountAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(estimateDiscountAmount));
                    });

                    // 预估-加价后金额
                    Optional.ofNullable(estimateFinanceDetailInfo.getAdditionAmount()).ifPresent(additionAmount -> {
                        estimateFinanceDetailInfoDto.setAdditionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(additionAmount));
                    });

                    // 预估-扩展字段
                    estimateFinanceDetailInfoDto.setExtendProps(estimateFinanceDetailInfo.getExtendProps());

                    estimateFinanceDetailInfoDtoList.add(estimateFinanceDetailInfoDto);
                });
                estimateFinanceInfoDto.setFinanceDetailInfos(estimateFinanceDetailInfoDtoList);
            });
            // 预估计费模式
            Optional.ofNullable(estimateFinanceInfo.getBillingMode()).ifPresent(estimateFinanceInfoDto::setBillingMode);

            financeInfoDto.setEstimateFinanceInfo(estimateFinanceInfoDto);
        });

        //客户扣款账号
        financeInfoDto.setPaymentAccountNo(financeInfo.getPaymentAccountNo());
        //支付方式
        if (ModifyClearUtil.isNeedClear(financeInfo.getPayment())) {
            financeInfoDto.setPayment(null);
            creator.getClearFields().add(ModifyItemConfigEnum.PAYMENT.getCode());
        } else {
            financeInfoDto.setPayment(PaymentTypeEnum.of(financeInfo.getPayment()));
        }

        //支付环节
        if (ModifyClearUtil.isNeedClear(financeInfo.getPaymentStage())) {
            financeInfoDto.setPaymentStage(null);
            creator.getClearFields().add(ModifyItemConfigEnum.PAYMENT_STAGE.getCode());
        } else {
            financeInfoDto.setPaymentStage(PaymentStageEnum.of(financeInfo.getPaymentStage()));
        }

        //结算账号
        financeInfoDto.setSettlementAccountNo(financeInfo.getSettlementAccountNo());
        //白条预授权标识
        financeInfoDto.setPreemptType(financeInfo.getPreemptType());
        if (ModifyClearUtil.isNeedClear(financeInfo.getPreemptType())) {
            financeInfoDto.setPreemptType(null);
            creator.getClearFields().add(ModifyItemConfigEnum.PREEMPT_TYPE.getCode());
        } else {
            financeInfoDto.setPreemptType(financeInfo.getPreemptType());
        }
        //积分信息
        Optional.ofNullable(financeInfo.getPointsInfo()).ifPresent(pointsInfo -> {
            PointsInfoDto pointsInfoDto = new PointsInfoDto();
            pointsInfoDto.setRedeemPointsAmount(toRedeemPointsAmount(pointsInfo.getRedeemPointsAmount()));
            pointsInfoDto.setRedeemPointsQuantity(toRedeemPointsQuantity(pointsInfo.getRedeemPointsQuantity()));
            financeInfoDto.setPointsInfoDto(pointsInfoDto);
        });
        //收费要求信息
        if (CollectionUtils.isNotEmpty(financeInfo.getCostInfos())) {
            List<CostInfoDto> costInfoDtos = new ArrayList<>(financeInfo.getCostInfos().size());
            financeInfo.getCostInfos().forEach(costInfo -> {
                if (costInfo != null) {
                    costInfoDtos.add(toCostInfoDto(costInfo));
                }
            });
            financeInfoDto.setCostInfoDtos(costInfoDtos);
        }

        //报关服务费、揽收偏远附加费、派送偏远附加费、燃油附加费
        if (CollectionUtils.isNotEmpty(financeInfo.getAttachFees())) {
            List<CostInfoDto> attachFees = new ArrayList<>(financeInfo.getAttachFees().size());
            financeInfo.getAttachFees().forEach(costInfo -> {
                if (costInfo != null) {
                    attachFees.add(toCostInfoDto(costInfo));
                }
            });
            financeInfoDto.setAttachFees(attachFees);
        }
        //预估税金
        financeInfoDto.setEstimatedTax(MoneyMapper.INSTANCE.toMoneyInfoDto(financeInfo.getEstimatedTax()));
        //真实税金
        financeInfoDto.setActualTax(MoneyMapper.INSTANCE.toMoneyInfoDto(financeInfo.getActualTax()));
        //抵扣信息
        financeInfoDto.setDeductionInfoDtos(this.toDeductionInfoDtos(financeInfo.getDeductionInfos()));
        //扩展属性
        financeInfoDto.setExtendProps(financeInfo.getExtendProps());
        //计费方式
        financeInfoDto.setBillingType(financeInfo.getBillingType());
        //税金结算方式
        financeInfoDto.setTaxSettlementType(financeInfo.getTaxSettlementType());
        if (ModifyClearUtil.isNeedClear(financeInfo.getTaxSettlementType())) {
            creator.getClearFields().add(ModifyItemConfigEnum.TAX_SETTLEMENT_TYPE.getCode());
        }
        //支付状态集合
        financeInfoDto.setPayStatusMap(financeInfo.getPayStatusMap());
        //收款机构
        financeInfoDto.setCollectionOrgNo(financeInfo.getCollectionOrgNo());
        creator.setFinanceInfo(financeInfoDto);
    }

    /**
     * 附加费对象转换
     *
     * @param costInfo
     * @return
     */
    private CostInfoDto toCostInfoDto(CostInfo costInfo) {
        CostInfoDto dto = new CostInfoDto();
        //费用项编码
        dto.setCostNo(costInfo.getCostNo());
        //费用项名称
        dto.setCostName(costInfo.getCostName());
        //收费方	0：向商家收 1：向寄件方收
        dto.setChargingSource(costInfo.getChargingSource());
        //月结费用填写（结算编码）前端卡控条件必填
        dto.setSettlementAccountNo(costInfo.getSettlementAccountNo());
        //加价信息
        dto.setAdditionPriceInfoDto(toAdditionPriceInfoDto(costInfo.getAdditionPriceInfo()));
        //扩展字段
        dto.setExtendProps(costInfo.getExtendProps());
        return dto;
    }

    /**
     * 加价信息对象转换
     *
     * @param additionPriceInfo
     * @return additionPriceInfoDto
     */
    private AdditionPriceInfoDto toAdditionPriceInfoDto(AdditionPriceInfo additionPriceInfo) {
        if (Objects.isNull(additionPriceInfo)) {
            return null;
        }
        AdditionPriceInfoDto dto = new AdditionPriceInfoDto();
        //加价公式
        dto.setFormulaNo(additionPriceInfo.getFormulaNo());
        //价格信息
        dto.setPriceItems(additionPriceInfo.getPriceItems());
        return dto;
    }

    /**
     * 积分使用数量转换
     * @param redeemPointsQuantity
     * @return
     */
    private QuantityInfoDto toRedeemPointsQuantity(QuantityInfo redeemPointsQuantity) {
        if (redeemPointsQuantity == null) {
            return null;
        }
        QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
        quantityInfoDto.setValue(redeemPointsQuantity.getValue());
        quantityInfoDto.setUnit(redeemPointsQuantity.getUnit());
        return quantityInfoDto;

    }

    /**
     * 积分使用金额转换
     * @param redeemPointsAmount
     * @return
     */
    private MoneyInfoDto toRedeemPointsAmount(MoneyInfo redeemPointsAmount) {
        if (redeemPointsAmount == null) {
            return null;
        }
        MoneyInfoDto moneyInfoDto = new MoneyInfoDto();
        moneyInfoDto.setAmount(redeemPointsAmount.getAmount());
        moneyInfoDto.setCurrencyCode(CurrencyCodeEnum.of(redeemPointsAmount.getCurrencyCode()));
        return moneyInfoDto;

    }

    /**
     * 抵扣信息dto转换
     * @param deductionInfos
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<DeductionInfo> deductionInfos) {
        if (CollectionUtils.isEmpty(deductionInfos)) {
            return null;
        }

        return DeductionMapper.INSTANCE.toDeductionInfoDtos(deductionInfos);
    }

    /**
     * 申请入参营销信息转换领域模型
     *
     * @param creator
     * @param promotionInfo
     */
    private void promotionInfoOf(ExpressOrderModelCreator creator, PromotionInfo promotionInfo) {
        Optional.ofNullable(promotionInfo).ifPresent(promotion -> {
            PromotionInfoDto promotionInfoDto = new PromotionInfoDto();
            Optional.ofNullable(promotion.getTicketInfos()).ifPresent(ticketInfos -> {
                //优惠券信息
                List<TicketInfoDto> ticketInfoDtos = ticketInfos.stream().map(ticketInfo -> {
                    TicketInfoDto ticketInfoDto = new TicketInfoDto();
                    ticketInfoDto.setTicketNo(ticketInfo.getTicketNo());
                    ticketInfoDto.setTicketCategory(ticketInfo.getTicketCategory());
                    ticketInfoDto.setTicketType(ticketInfo.getTicketType());
                    Optional.ofNullable(ticketInfo.getTicketDiscountAmount()).ifPresent(ticketDiscountAmount -> {
                        ticketInfoDto.setTicketDiscountAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(ticketDiscountAmount));
                        complementDefaultCurrencyCode(ticketInfoDto.getTicketDiscountAmount());
                    });
                    Optional.ofNullable(ticketInfo.getTicketUseAmount()).ifPresent(ticketUseAmount -> {
                        ticketInfoDto.setTicketUseAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(ticketUseAmount));
                        complementDefaultCurrencyCode(ticketInfoDto.getTicketUseAmount());
                    });
                    ticketInfoDto.setTicketDescription(ticketInfo.getTicketDescription());
                    ticketInfoDto.setTicketDiscountRate(ticketInfo.getTicketDiscountRate());
                    Optional.ofNullable(ticketInfo.getTicketDiscountUpperLimit()).ifPresent(ticketDiscountUpperLimit -> {
                        ticketInfoDto.setTicketDiscountUpperLimit(MoneyMapper.INSTANCE.toMoneyInfoDto(ticketDiscountUpperLimit));
                        complementDefaultCurrencyCode(ticketInfoDto.getTicketDiscountUpperLimit());
                    });
                    ticketInfoDto.setExtendProps(ticketInfo.getExtendProps());
                    //优惠券来源
                    ticketInfoDto.setTicketSource(ticketInfo.getTicketSource());
                    return ticketInfoDto;
                }).collect(Collectors.toList());
                promotionInfoDto.setTicketInfos(ticketInfoDtos);
            });
            //折扣信息
            Optional.ofNullable(promotion.getDiscountInfos()).ifPresent(discountInfos -> {
                //优惠券信息
                List<DiscountInfoDto> discountInfoDtos = discountInfos.stream().map(discountInfo -> {
                    DiscountInfoDto discountInfoDto = new DiscountInfoDto();
                    discountInfoDto.setDiscountNo(discountInfo.getDiscountNo());
                    discountInfoDto.setExtendProps(discountInfo.getExtendProps());
                    return discountInfoDto;
                }).collect(Collectors.toList());
                promotionInfoDto.setDiscountInfos(discountInfoDtos);
            });
            //折扣关联单
            promotionInfoDto.setDiscountRefOrderNo(promotion.getDiscountRefOrderNo());
            //活动信息
            Optional.ofNullable(promotion.getActivityInfos()).ifPresent(activityInfos -> {
                //优惠券信息
                List<ActivityInfoDto> activityInfoDtos = activityInfos.stream().map(activityInfo -> {
                    ActivityInfoDto activityInfoDto = new ActivityInfoDto();
                    activityInfoDto.setActivityNo(activityInfo.getActivityNo());
                    activityInfoDto.setActivityName(activityInfo.getActivityName());
                    activityInfoDto.setActivityStatus(activityInfo.getActivityStatus());
                    activityInfoDto.setActivityValue(activityInfo.getActivityValue());
                    activityInfoDto.setExtendProps(activityInfo.getExtendProps());
                    return activityInfoDto;
                }).collect(Collectors.toList());
                promotionInfoDto.setActivityInfos(activityInfoDtos);
            });
            creator.setPromotionInfo(promotionInfoDto);
        });
    }


    /**
     * 申请接单入参收货人地址数据对象转换成接单领域模型
     *
     * @param consignorInfoDto
     * @param addressInfo
     */
    private void consignorAddressOf(ConsignorInfoDto consignorInfoDto, AddressInfo addressInfo) {

        AddressInfoDto addressInfoDto = new AddressInfoDto();
        //省编码
        addressInfoDto.setProvinceNo(addressInfo.getProvinceNo());
        //省名称
        addressInfoDto.setProvinceName(addressInfo.getProvinceName());
        //市编码
        addressInfoDto.setCityNo(addressInfo.getCityNo());
        //市名称
        addressInfoDto.setCityName(addressInfo.getCityName());
        //区/县编码
        addressInfoDto.setCountyNo(addressInfo.getCountyNo());
        //区/县名称
        addressInfoDto.setCountyName(addressInfo.getCountyName());
        //乡/镇 编码
        addressInfoDto.setTownNo(addressInfo.getTownNo());
        //乡/镇 名称
        addressInfoDto.setTownName(addressInfo.getTownName());
        //详细地址
        addressInfoDto.setAddress(addressInfo.getAddress());
        //坐标系类型
        addressInfoDto.setCoordinateType(CoordinateTypeEnum.of(addressInfo.getCoordinateType()));
        //地址经度
        addressInfoDto.setLongitude(addressInfo.getLongitude());
        //地址纬度
        addressInfoDto.setLatitude(addressInfo.getLatitude());
        //GIS解析后省编码
        addressInfoDto.setProvinceNoGis(addressInfo.getProvinceNoGis());
        //GIS解析后省名称
        addressInfoDto.setProvinceNameGis(addressInfo.getProvinceNameGis());
        //GIS解析后市编码
        addressInfoDto.setCityNoGis(addressInfo.getCityNoGis());
        //GIS解析后市名称
        addressInfoDto.setCityNameGis(addressInfo.getCityNameGis());
        //GIS解析后区编码
        addressInfoDto.setCountyNoGis(addressInfo.getCountyNoGis());
        //GIS解析后区名称
        addressInfoDto.setCountyNameGis(addressInfo.getCountyNameGis());
        //GIS解析后镇编码
        addressInfoDto.setTownNoGis(addressInfo.getTownNoGis());
        //GIS解析后镇名称
        addressInfoDto.setTownNameGis(addressInfo.getTownNameGis());
        //GIS解析后详细地址
        addressInfoDto.setAddressGis(addressInfo.getAddressGis());
        //国家邮政局地址编码
        addressInfoDto.setChinaPostAddressCode(addressInfo.getChinaPostAddressCode());
        //GIS解析精准度
        addressInfoDto.setPreciseGis(addressInfo.getPreciseGis());
        //地址嵌套等级
        addressInfoDto.setConflictLevel(addressInfo.getConflictLevel());
        //地址来源
        addressInfoDto.setAddressSource(addressSourceOf(addressInfo));
        //围栏信任
        addressInfoDto.setFenceTrusted(
            Optional.ofNullable(addressInfo.getFenceTrusted()).orElse(FenceTrustEnum.UNTRUSTED.getCode())
        );
        //围栏信息
        Optional.ofNullable(addressInfo.getFenceInfos()).ifPresent(fenceInfos ->
            this.fenceInfosOf(addressInfoDto, addressInfo.getFenceInfos()));

        //行政区编码
        addressInfoDto.setRegionNo(addressInfo.getRegionNo());
        //行政区名称
        addressInfoDto.setRegionName(addressInfo.getRegionName());
        //英文发件人城市
        addressInfoDto.setEnCityName(addressInfo.getEnCityName());
        //英文发件人地址
        addressInfoDto.setEnAddress(addressInfo.getEnAddress());
        addressInfoDto.setPoiCode(addressInfo.getPoiCode());
        addressInfoDto.setPoiName(addressInfo.getPoiName());
        addressInfoDto.setHouseNumber(addressInfo.getHouseNumber());
        addressInfoDto.setExtendProps(addressInfo.getExtendProps());
        consignorInfoDto.setAddressInfoDto(addressInfoDto);
    }


    /**
     * 申请收货人地址信息转换领域模型转换数据对象
     *
     * @param consigneeInfoDto
     * @param addressInfo
     */
    private void consigneeAddressOf(ConsigneeInfoDto consigneeInfoDto, AddressInfo addressInfo) {
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        //省编码
        addressInfoDto.setProvinceNo(addressInfo.getProvinceNo());
        //省名称
        addressInfoDto.setProvinceName(addressInfo.getProvinceName());
        //市编码
        addressInfoDto.setCityNo(addressInfo.getCityNo());
        //市名称
        addressInfoDto.setCityName(addressInfo.getCityName());
        //区/县编码
        addressInfoDto.setCountyNo(addressInfo.getCountyNo());
        //区/县名称
        addressInfoDto.setCountyName(addressInfo.getCountyName());
        //乡/镇 编码
        addressInfoDto.setTownNo(addressInfo.getTownNo());
        //乡/镇 名称
        addressInfoDto.setTownName(addressInfo.getTownName());
        //详细地址
        addressInfoDto.setAddress(addressInfo.getAddress());
        //坐标系类型
        addressInfoDto.setCoordinateType(CoordinateTypeEnum.of(addressInfo.getCoordinateType()));
        //地址经度
        addressInfoDto.setLongitude(addressInfo.getLongitude());
        //地址纬度
        addressInfoDto.setLatitude(addressInfo.getLatitude());
        //GIS解析后省编码
        addressInfoDto.setProvinceNoGis(addressInfo.getProvinceNoGis());
        //GIS解析后省名称
        addressInfoDto.setProvinceNameGis(addressInfo.getProvinceNameGis());
        //GIS解析后市编码
        addressInfoDto.setCityNoGis(addressInfo.getCityNoGis());
        //GIS解析后市名称
        addressInfoDto.setCityNameGis(addressInfo.getCityNameGis());
        //GIS解析后区编码
        addressInfoDto.setCountyNoGis(addressInfo.getCountyNoGis());
        //GIS解析后区名称
        addressInfoDto.setCountyNameGis(addressInfo.getCountyNameGis());
        //GIS解析后镇编码
        addressInfoDto.setTownNoGis(addressInfo.getTownNoGis());
        //GIS解析后镇名称
        addressInfoDto.setTownNameGis(addressInfo.getTownNameGis());
        //GIS解析后详细地址
        addressInfoDto.setAddressGis(addressInfo.getAddressGis());
        //国家邮政局地址编码
        addressInfoDto.setChinaPostAddressCode(addressInfo.getChinaPostAddressCode());
        //GIS解析精准度
        addressInfoDto.setPreciseGis(addressInfo.getPreciseGis());
        //地址嵌套等级
        addressInfoDto.setConflictLevel(addressInfo.getConflictLevel());
        //地址来源
        addressInfoDto.setAddressSource(addressSourceOf(addressInfo));
        //围栏信任
        addressInfoDto.setFenceTrusted(
            Optional.ofNullable(addressInfo.getFenceTrusted()).orElse(FenceTrustEnum.UNTRUSTED.getCode())
        );
        //围栏信息
        Optional.ofNullable(addressInfo.getFenceInfos()).ifPresent(fenceInfos ->
            this.fenceInfosOf(addressInfoDto, addressInfo.getFenceInfos()));

        //行政区编码
        addressInfoDto.setRegionNo(addressInfo.getRegionNo());
        //行政区名称
        addressInfoDto.setRegionName(addressInfo.getRegionName());
        // 英文发件人城市
        addressInfoDto.setEnCityName(addressInfo.getEnCityName());
        // 英文发件人地址
        addressInfoDto.setEnAddress(addressInfo.getEnAddress());
        addressInfoDto.setPoiCode(addressInfo.getPoiCode());
        addressInfoDto.setPoiName(addressInfo.getPoiName());
        addressInfoDto.setHouseNumber(addressInfo.getHouseNumber());
        addressInfoDto.setExtendProps(addressInfo.getExtendProps());
        //地址信息
        consigneeInfoDto.setAddressInfoDto(addressInfoDto);
    }

    /**
     * 收货仓信息转换
     * @param consigneeInfoDto
     * @param warehouseInfo
     */
    private void consigneeReceiveWarehouseOf(ConsigneeInfoDto consigneeInfoDto, WarehouseInfo warehouseInfo){
        WarehouseInfoDto warehouseInfoDto = new WarehouseInfoDto();
        //仓库名称
        warehouseInfoDto.setWarehouseNo(warehouseInfo.getWarehouseNo());
        //仓库名称
        warehouseInfoDto.setWarehouseName(warehouseInfo.getWarehouseName());
        //仓库类型
        warehouseInfoDto.setWarehouseSource(warehouseInfo.getWarehouseSource());
        //收货仓库信息
        consigneeInfoDto.setReceiveWarehouse(warehouseInfoDto);
    }

    /**
     * 商品信息
     *
     * @param creator
     * @param goodsInfos
     */
    private void goodsInfosOf(ExpressOrderModelCreator creator, List<GoodsInfo> goodsInfos) {
        List<GoodsInfoDto> goodsInfoDtos = new ArrayList<>(goodsInfos.size());
        goodsInfos.forEach(goodsInfo -> {
            GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
            goodsInfoDto.setGoodsNo(goodsInfo.getGoodsNo());
            goodsInfoDto.setGoodsUniqueCode(goodsInfo.getGoodsUniqueCode());
            goodsInfoDto.setChannelGoodsNo(goodsInfo.getChannelGoodsNo());
            goodsInfoDto.setGoodsName(goodsInfo.getGoodsName());
            goodsInfoDto.setGoodsAmount(goodsInfo.getGoodsAmount());
            goodsInfoDto.setGoodsSerialInfos(goodsInfo.getGoodsSerialInfos());
            goodsInfoDto.setGoodsType(goodsInfo.getGoodsType());
            goodsInfoDto.setGoodsPrice(goodsInfo.getGoodsPrice());
            goodsInfoDto.setGoodsQuantity(goodsInfo.getGoodsQuantity());
            // 商品重量
            goodsInfoDto.setGoodsWeight(this.goodsWeightDtoOf(goodsInfo.getGoodsWeight()));
            // 商品体积
            goodsInfoDto.setGoodsVolume(this.goodsVolumeDtoOf(goodsInfo.getGoodsVolume()));
            // 商品三维
            goodsInfoDto.setGoodsDimension(this.goodsDimensionDtoOf(goodsInfo.getGoodsDimension()));
            // 促销信息
            goodsInfoDto.setSalesInfos(goodsInfo.getSalesInfos());

            goodsInfoDto.setCombinationGoodsVersion(goodsInfo.getCombinationGoodsVersion());
            Map<String, String> extendProps = goodsInfo.getExtendProps();
            if(MapUtils.isEmpty(extendProps)){
                extendProps = new HashMap<>();
            }
            extendProps.put(OrderConstants.GOODS_UNIQUE_CODE,goodsInfo.getGoodsUniqueCode());
            goodsInfoDto.setExtendProps(extendProps);
            //商品维度增值服务列表
            if (CollectionUtils.isNotEmpty(goodsInfo.getGoodsProductInfos())) {
                List<ProductInfoDto> productInfoDtos = new ArrayList<>(goodsInfo.getGoodsProductInfos().size());
                goodsInfo.getGoodsProductInfos().forEach(productInfo -> {
                    if (productInfo != null) {
                        ProductInfoDto dto = new ProductInfoDto();
                        //主产品编码
                        dto.setParentNo(productInfo.getParentNo());
                        //产品编码
                        dto.setProductNo(productInfo.getProductNo());
                        //产品类型
                        dto.setProductType(productInfo.getProductType());
                        //产品要素属性
                        dto.setProductAttrs(productInfo.getProductAttrs());
                        // 产品执行结果
                        dto.setProductExecutionResult(productInfo.getProductExecutionResult());
                        productInfoDtos.add(dto);
                    }
                });
                goodsInfoDto.setGoodsProductInfos(productInfoDtos);
            }
            //附件信息
            Optional.ofNullable(goodsInfo.getGoodsAttachmentInfos()).ifPresent(attachmentInfos -> {
                //遍历附件
                List<AttachmentInfoDto> attachmentInfoDtos = attachmentInfos.stream().map(attachmentInfo -> {
                    AttachmentInfoDto attachmentInfoDto = new AttachmentInfoDto();
                    attachmentInfoDto.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    attachmentInfoDto.setAttachmentName(attachmentInfo.getAttachmentName());
                    attachmentInfoDto.setAttachmentType(attachmentInfo.getAttachmentType());
                    attachmentInfoDto.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    attachmentInfoDto.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    attachmentInfoDto.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachmentInfoDto;
                }).collect(Collectors.toList());
                //附件集
                goodsInfoDto.setGoodsAttachmentInfos(attachmentInfoDtos);
            });

            // 商品净重
            goodsInfoDto.setNetWeight(this.goodsWeightDtoOf(goodsInfo.getNetWeight()));

            goodsInfoDtos.add(goodsInfoDto);
        });
        creator.setGoodsInfos(goodsInfoDtos);
    }

    /**
     * 地址来源转换
     * @param addressInfo
     * @return
     */
    private Integer addressSourceOf(AddressInfo addressInfo){
        Integer consignorAddressSource = AddressSourceEnum.USER.getCode();
        if(null != addressInfo.getAddressSource()){
            AddressSourceEnum addressSourceEnum = AddressSourceEnum.of(addressInfo.getAddressSource());
            if(null != addressSourceEnum){
                consignorAddressSource = addressSourceEnum .getCode();
            }else{
                throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
                        .withCustom("无效的地址来源");
            }
        }
        return consignorAddressSource;
    }

    /**
     * 围栏信息转换
     * @param addressInfoDto
     * @param fenceInfos
     */
    private void fenceInfosOf(AddressInfoDto addressInfoDto, List<FenceInfo> fenceInfos) {
        List<FenceInfoDto> fenceInfoDtos = new ArrayList<>(fenceInfos.size());
        fenceInfos.forEach(fenceInfo -> {
            FenceInfoDto fenceInfoDto = new FenceInfoDto();
            fenceInfoDto.setFenceId(fenceInfo.getFenceId());
            fenceInfoDto.setFenceType(fenceInfo.getFenceType());
            fenceInfoDtos.add(fenceInfoDto);
        });
        addressInfoDto.setFenceInfos(fenceInfoDtos);
    }

    /**
     * 协议信息转化
     * @param creator
     * @param agreementInfos
     */
    private void agreementInfosOf(ExpressOrderModelCreator creator, List<AgreementInfo> agreementInfos) {
        List<AgreementInfoDto> agreementInfoDtos = Lists.newArrayListWithCapacity(agreementInfos.size());
        agreementInfos.forEach(agreementInfo -> {
            AgreementInfoDto agreementInfoDto = new AgreementInfoDto();
            agreementInfoDto.setAgreementType(agreementInfo.getAgreementType());
            agreementInfoDto.setAgreementId(agreementInfo.getAgreementId());
            agreementInfoDto.setSigner(agreementInfo.getSigner());
            agreementInfoDto.setSigningTime(agreementInfo.getSigningTime());
            agreementInfoDto.setExtendProps(agreementInfo.getExtendProps());
            agreementInfoDtos.add(agreementInfoDto);

        });
        creator.setAgreementInfoDtos(agreementInfoDtos);
    }

    /**
     * 校验传入的修改类型是否属于当前枚举类型，不属于当前枚举类型则进行打点记录
     * @param request
     */
    private void checkModifiedFields(ModifyExpressOrderRequest request) {
        Map<String, String> modifiedFields = request.getModifiedFields();
        StringBuilder stringBuilder = new StringBuilder();
        // 遍历将不合法入参汇总
        if (null != modifiedFields && modifiedFields.size() > 0) {
            for (String key : modifiedFields.keySet()) {
                if (null == ModifiedFieldValueEnum.fromCode(modifiedFields.get(key))) {
                    stringBuilder.append("**").append(key).append(":").append(modifiedFields.get(key)).append("**");
                }
            }
        }
        // 判断是否有不合法的入参
        if (stringBuilder.length() > 0){
            // 添加打点
            CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".checkModifiedFields"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
            Profiler.functionError(callerInfo);
            LOGGER.info("修改类型为1、2、3；不合法的修改类型: {}, 单号：{}" ,stringBuilder.toString(), request.getOrderNo());
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 拦截信息转换
     */
    private void interceptInfoOf(ExpressOrderModelCreator creator, InterceptInfo interceptInfo) {
        if (interceptInfo == null) {
            return;
        }
        InterceptInfoDto interceptInfoDto = InterceptMapper.INSTANCE.toInterceptInfoDto(interceptInfo);
        creator.setInterceptInfoDto(interceptInfoDto);
    }

    /**
     * 币种类型为空时，默认赋值人民币
     */
    private void complementDefaultCurrencyCode(MoneyInfoDto moneyInfoDto) {
        if (moneyInfoDto == null || moneyInfoDto.getCurrencyCode() != null) {
            return;
        }
        moneyInfoDto.setCurrencyCode(CurrencyCodeEnum.CNY);
    }

    /**
     * 修改入参关联单数据对象转换成接单领域模型
     *
     * @param creator
     * @param refOrderInfo
     */
    private void refOrderOf(ExpressOrderModelCreator creator, RefOrderInfo refOrderInfo) {
        RefOrderInfoDto refOrderInfoDto = new RefOrderInfoDto();
        //集单号
        refOrderInfoDto.setCollectionOrderNo(refOrderInfo.getCollectionOrderNo());
        refOrderInfoDto.setExtendProps(refOrderInfo.getExtendProps());
        // 送取同步-取件单-订单号
        refOrderInfoDto.setPickupOrderNo(refOrderInfo.getPickupOrderNo());
        // 送取同步-取件单-运单号
        refOrderInfoDto.setPickupWaybillNo(refOrderInfo.getPickupWaybillNo());
        // 送取同步-派送单-订单号
        refOrderInfoDto.setDeliveryOrderNo(refOrderInfo.getDeliveryOrderNo());
        // 送取同步-派送单-运单号
        refOrderInfoDto.setDeliveryWaybillNo(refOrderInfo.getDeliveryWaybillNo());
        creator.setRefOrder(refOrderInfoDto);
    }

    /**
     * 履约信息转化
     */
    private void fulfillmentOf(ExpressOrderModelCreator creator, FulfillmentInfo fulfillmentInfo) {
        FulfillmentInfoDto fulfillmentInfoDto = new FulfillmentInfoDto();
        fulfillmentInfoDto.setFulfillmentSign(fulfillmentInfo.getFulfillmentSign());
        fulfillmentInfoDto.setExtendProps(fulfillmentInfo.getExtendProps());
        Optional.ofNullable(fulfillmentInfo.getActualReceivedQuantity()).ifPresent(actualReceivedQuantity -> {
            QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
            quantityInfoDto.setValue(actualReceivedQuantity.getValue());
            quantityInfoDto.setUnit(actualReceivedQuantity.getUnit());
            fulfillmentInfoDto.setActualReceivedQuantity(quantityInfoDto);
        });

        // 包裹最长边
        Optional.ofNullable(fulfillmentInfo.getPackageMaxLen()).ifPresent(packageMaxLen -> {
            LengthInfoDto lengthInfoDto = new LengthInfoDto();
            lengthInfoDto.setValue(packageMaxLen.getValue());
            lengthInfoDto.setUnit(LengthTypeEnum.of(packageMaxLen.getUnit()));
            fulfillmentInfoDto.setPackageMaxLen(lengthInfoDto);
        });
        creator.setFulfillmentInfo(fulfillmentInfoDto);
    }

    /**
     * 商品重量
     * @param goodsWeight
     * @return
     */
    private WeightInfoDto goodsWeightDtoOf(WeightInfo goodsWeight) {
        if(null == goodsWeight){
            return null;
        }
        WeightInfoDto weightInfoDto = new WeightInfoDto();
        weightInfoDto.setValue(goodsWeight.getValue());
        weightInfoDto.setUnit(WeightTypeEnum.of(goodsWeight.getUnit()));
        return weightInfoDto;
    }

    /**
     * 商品体积
     * @param volumeInfo
     * @return
     */
    private VolumeInfoDto goodsVolumeDtoOf(VolumeInfo volumeInfo) {
        if(null == volumeInfo){
            return null;
        }
        VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
        volumeInfoDto.setValue(volumeInfo.getValue());
        volumeInfoDto.setUnit(VolumeTypeEnum.of(volumeInfo.getUnit()));
        return volumeInfoDto;
    }

    /**
     * 商品三维
     * @param dimensionInfo
     * @return
     */
    private DimensionInfoDto goodsDimensionDtoOf(DimensionInfo dimensionInfo) {
        if(null == dimensionInfo){
            return null;
        }
        DimensionInfoDto dimensionInfoDto = new DimensionInfoDto();
        dimensionInfoDto.setHeight(dimensionInfo.getHeight());
        dimensionInfoDto.setWidth(dimensionInfo.getWidth());
        dimensionInfoDto.setLength(dimensionInfo.getLength());
        dimensionInfoDto.setUnit(LengthTypeEnum.of(dimensionInfo.getUnit()));
        return dimensionInfoDto;
    }

    /**
     * 跨境报关信息
     */
    private void customsInfoOf(ExpressOrderModelCreator creator, CustomsInfo customsInfo) {
        if ( customsInfo == null ) {
            return;
        }

        CustomsInfoDto customsInfoDto = new CustomsInfoDto();

        //始发流向
        if (ModifyClearUtil.isNeedClear(customsInfo.getStartFlowDirection())) {
            customsInfoDto.setStartFlowDirection(null);
            creator.getClearFields().add(ModifyItemConfigEnum.CUSTOMS_START_FLOW_DIRECTION.getCode());
        } else {
            customsInfoDto.setStartFlowDirection(AdministrativeRegionEnum.of(customsInfo.getStartFlowDirection()));
        }

        //目的流向
        if (ModifyClearUtil.isNeedClear(customsInfo.getEndFlowDirection())) {
            customsInfoDto.setEndFlowDirection(null);
            creator.getClearFields().add(ModifyItemConfigEnum.CUSTOMS_END_FLOW_DIRECTION.getCode());
        } else {
            customsInfoDto.setEndFlowDirection(AdministrativeRegionEnum.of(customsInfo.getEndFlowDirection()));
        }

        customsInfoDto.setClearanceMode( customsInfo.getClearanceMode() );
        customsInfoDto.setClearanceType( customsInfo.getClearanceType() );

        //关务状态
        if (ModifyClearUtil.isNeedClear(customsInfo.getCustomsStatus())) {
            customsInfoDto.setCustomsStatus(null);
            creator.getClearFields().add(ModifyItemConfigEnum.CUSTOMS_CUSTOMS_STATUS.getCode());
        } else {
            customsInfoDto.setCustomsStatus(CustomsStatusEnum.of(customsInfo.getCustomsStatus()));
        }

        customsInfoDto.setFileTag( customsInfo.getFileTag() );
        customsInfoDto.setSupervision( customsInfo.getSupervision() );
        customsInfoDto.setTransactionMethod( customsInfo.getTransactionMethod() );
        customsInfoDto.setImporterVatNumber(customsInfo.getImporterVatNumber());
        customsInfoDto.setImporterEoriNumber(customsInfo.getImporterEoriNumber());
        customsInfoDto.setImporterIossNumber(customsInfo.getImporterIossNumber());
        customsInfoDto.setExtendProps(customsInfo.getExtendProps());
        creator.setCustomsInfoDto(customsInfoDto);
    }

    /**
     * 附件列表
     */
    private void attachmentInfosOf(ExpressOrderModelCreator creator, List<AttachmentInfo> attachmentInfos) {
        if (CollectionUtils.isEmpty(attachmentInfos)) {
            return;
        }
        List<AttachmentInfoDto> attachmentInfoDtos = AttachmentMapper.INSTANCE.toAttachmentInfoDtos(attachmentInfos);
        creator.setAttachmentInfoDtos(attachmentInfoDtos);
    }


    /**
     * 订单总体积
     *
     * @param creator
     * @param orderVolumeInfo
     */
    private void orderVolumeInfoOf(ExpressOrderModelCreator creator, VolumeInfo orderVolumeInfo) {
        VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
        creator.setOrderVolume(volumeInfoDto);
        volumeInfoDto.setValue(orderVolumeInfo.getValue());
        volumeInfoDto.setUnit(VolumeTypeEnum.of(orderVolumeInfo.getUnit()));
    }

    /**
     * 订单总重量
     *
     * @param creator
     * @param orderWeightInfo
     */
    private void orderWeightInfoOf(ExpressOrderModelCreator creator, WeightInfo orderWeightInfo) {
        WeightInfoDto weightInfoDto = new WeightInfoDto();
        creator.setOrderWeight(weightInfoDto);
        weightInfoDto.setValue(orderWeightInfo.getValue());
        weightInfoDto.setUnit(WeightTypeEnum.of(orderWeightInfo.getUnit()));
    }

    /**
     * 订单总净重
     *
     * @param creator
     * @param orderNetWeightInfo
     */
    private void orderNetWeightInfoOf(ExpressOrderModelCreator creator, WeightInfo orderNetWeightInfo) {
        WeightInfoDto weightInfoDto = new WeightInfoDto();
        creator.setOrderNetWeight(weightInfoDto);
        weightInfoDto.setValue(orderNetWeightInfo.getValue());
        weightInfoDto.setUnit(WeightTypeEnum.of(orderNetWeightInfo.getUnit()));
    }

    /**
     * 复核体积
     *
     * @param creator
     * @param recheckVolume
     */
    private void recheckVolumeOf(ExpressOrderModelCreator creator, VolumeInfo recheckVolume) {
        VolumeInfoDto recheckVolumeDto = new VolumeInfoDto();
        recheckVolumeDto.setValue(recheckVolume.getValue());
        recheckVolumeDto.setUnit(VolumeTypeEnum.of(recheckVolume.getUnit()));
        creator.setRecheckVolumeDto(recheckVolumeDto);
    }

    /**
     * 复核重量
     *
     * @param creator
     * @param recheckWeight
     */
    private void recheckWeightOf(ExpressOrderModelCreator creator, WeightInfo recheckWeight) {
        WeightInfoDto recheckWeightDto = new WeightInfoDto();
        recheckWeightDto.setValue(recheckWeight.getValue());
        recheckWeightDto.setUnit(WeightTypeEnum.of(recheckWeight.getUnit()));
        creator.setRecheckWeightDto(recheckWeightDto);
    }

    /**
     * 解决方案信息
     *
     * @param creator
     * @param businessSolutionInfo
     */
    private void businessSolutionInfoOf(ExpressOrderModelCreator creator, BusinessSolutionInfo businessSolutionInfo) {
        BusinessSolutionInfoDto businessSolutionInfoDto = new BusinessSolutionInfoDto();
        businessSolutionInfoDto.setBusinessSolutionNo(businessSolutionInfo.getBusinessSolutionNo());
        businessSolutionInfoDto.setBusinessSolutionName(businessSolutionInfo.getBusinessSolutionName());
        businessSolutionInfoDto.setProductAttrs(businessSolutionInfo.getProductAttrs());
        creator.setBusinessSolutionInfoDto(businessSolutionInfoDto);
    }
}
