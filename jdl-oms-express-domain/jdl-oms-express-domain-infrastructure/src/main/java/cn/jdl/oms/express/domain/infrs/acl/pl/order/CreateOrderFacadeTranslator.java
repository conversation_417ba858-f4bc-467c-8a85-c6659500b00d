package cn.jdl.oms.express.domain.infrs.acl.pl.order;

import cn.jdl.oms.core.model.AddressInfo;
import cn.jdl.oms.core.model.AttachmentInfo;
import cn.jdl.oms.core.model.FenceInfo;
import cn.jdl.oms.core.model.WarehouseInfo;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.AddressExtend;
import cn.jdl.oms.express.domain.converter.MoneyMapper;
import cn.jdl.oms.express.domain.dto.DeductionInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.AttachmentFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.convertor.CustomsFacadeMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ActivityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AgreementFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.AttachmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessIdentityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.BusinessSolutionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CargoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ChannelFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsigneeFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ConsignorFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.CustomsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.DiscountFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceDetailFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FinanceFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.FulfillmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.GoodsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.MoneyFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PointsFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ProductFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.PromotionFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.QuantityFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.RefOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ReturnInfoFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.ShipmentFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.dto.TicketFacade;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderUsageEnum;
import cn.jdl.oms.express.domain.spec.dict.PaymentStageEnum;
import cn.jdl.oms.express.domain.spec.dict.RefOrderSubType;
import cn.jdl.oms.express.domain.spec.dict.RefOrderTypeEnum;
import cn.jdl.oms.express.domain.vo.Activity;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Agreement;
import cn.jdl.oms.express.domain.vo.AgreementDelegate;
import cn.jdl.oms.express.domain.vo.Attachment;
import cn.jdl.oms.express.domain.vo.BusinessSolution;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.Discount;
import cn.jdl.oms.express.domain.vo.Fence;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Fulfillment;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Points;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.Promotion;
import cn.jdl.oms.express.domain.vo.RefOrder;
import cn.jdl.oms.express.domain.vo.ReturnInfoVo;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Ticket;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Warehouse;
import cn.jdl.oms.express.domain.vo.Weight;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName CreateOrderFacadeTranslator
 * @Description 接单防腐层信息转换
 * <AUTHOR>
 * @Date 2021/3/21 5:00 下午
 * @ModifyDate 2021/3/21 5:00 下午
 * @Version 1.0
 */
@Translator
public class CreateOrderFacadeTranslator {

    /**
     * 接单防腐层请求转换
     *
     * @param context
     * @return
     */
    public CreateOrderFacadeRequest toCreateOrderFacadeRequest(ExpressOrderContext context) {
        CreateOrderFacadeRequest facadeRequest = new CreateOrderFacadeRequest();
        if (context != null && context.getOrderModel() != null) {
            ExpressOrderModel model = context.getOrderModel();
            //业务身份信息
            facadeRequest.setBusinessIdentity(toOrderBusinessIdentity(model.getOrderBusinessIdentity()));
            //订单号
            facadeRequest.setOrderNo(model.orderNo());
            //业务单号
            facadeRequest.setCustomOrderNo(model.getCustomOrderNo());
            //父订单号
            facadeRequest.setParentOrderNo(model.getParentOrderNo());
            //订单类型
            facadeRequest.setOrderType(model.getOrderType().getCode());
            //订单子类型
            facadeRequest.setOrderSubType(null == model.getOrderSubType() ? null : model.getOrderSubType().getCode());
            //订单用途
            facadeRequest.setOrderUsage(model.getOrderUsage());
            //订单主状态
            facadeRequest.setOrderStatus(model.getOrderStatus().getOrderStatus().getCode());
            //订单业务状态
            facadeRequest.setOrderStatusCustom(model.getCustomStatus());
            //订单扩展状态
            facadeRequest.setOrderExtendStatus(model.getExecutedStatus());
            //下单人类型
            facadeRequest.setInitiatorType(model.getInitiatorType() == null ? null : model.getInitiatorType().getCode());
            //下单人
            facadeRequest.setOperator(model.getOperator());
            //oms接单时间
            facadeRequest.setOperateTime(model.getOperateTime());
            //订单备注
            facadeRequest.setRemark(model.getRemark());
            //扩展信息
            facadeRequest.setExtendProps(model.getExtendProps());
            //交易客户信息
            facadeRequest.setCustomer(toCustomerFacade(model.getCustomer()));
            //渠道信息
            facadeRequest.setChannel(toChannelFacade(model.getChannel()));
            //产品信息
            facadeRequest.setProducts(toProductFacades((List<Product>) model.getProductDelegate().getProducts()));
            //发货信息
            facadeRequest.setConsignor(toConsignorFacade(model.getConsignor(), context.getAddressExtend()));
            //收货信息
            facadeRequest.setConsignee(toConsigneeFacade(model.getConsignee(), context.getAddressExtend()));
            //货品信息
            List<Cargo> cargos = (List<Cargo>) model.getCargoDelegate().getCargoList();
            facadeRequest.setCargos(toCargoFacades(cargos));
            //商品信息
            facadeRequest.setGoodsList(toGoodsFacades((List<Goods>) model.getGoodsDelegate().getGoodsList()));
            //配送信息
            facadeRequest.setShipment(toShipmentFacade(model.getShipment()));
            //财务信息
            facadeRequest.setFinance(toFinanceFacade(model.getFinance()));
            //营销信息
            facadeRequest.setPromotion(toPromotionFacade(model.getPromotion()));
            //关联单信息
            facadeRequest.setRefOrders(toRefOrderFacades(model));
            //绑定原单关系
            facadeRequest.setOriginalNo(model.getRefOrderInfoDelegate().getOriginalNo());
            /**
             * 改址单先款订单要隐藏
             */
            if (OrderTypeEnum.READDRESS == context.getOrderModel().getOrderType() && PaymentStageEnum.ONLINEPAYMENT == context.getOrderModel().getFinance().getPaymentStage()) {
                facadeRequest.setOrderUsage(OrderUsageEnum.hidden.getCode());
            }
            //隐藏标
            facadeRequest.setHiddenMark(model.getHiddenMark());
            //解决方案信息
            facadeRequest.setBusinessSolutionFacade(toBusinessSolutionFacade(model.getBusinessSolution()));
            // 协议信息
            facadeRequest.setAgreementFacades(this.toAgreementFacades(model.getAgreementDelegate()));
            // 订单标识
            facadeRequest.setOrderSign(model.getOrderSign());
            // 关联单扩展单据类型
            facadeRequest.setExtendRefOrder(model.getRefOrderInfoDelegate().getExtendProps());
            //退货信息
            facadeRequest.setReturnInfoFacade(this.toReturnInfoFacade(model.getReturnInfoVo()));
            //履约信息
            facadeRequest.setFulfillmentFacade(this.toFulfillmentFacade(model.getFulfillment()));
            //跨境报关信息
            facadeRequest.setCustomsFacade(this.toCustomsFacade(model.getCustoms()));
            //附件列表
            facadeRequest.setAttachmentFacades(toAttachmentFacades(model.getAttachments()));
            //订单总毛重
            facadeRequest.setOrderWeight(toWeightInfo(model.getOrderWeight()));
            //订单总净重
            facadeRequest.setOrderNetWeight(toWeightInfo(model.getOrderNetWeight()));
            //订单总体积
            facadeRequest.setOrderVolume(toVolumeInfo(model.getOrderVolume()));
            //弃货状态
            facadeRequest.setDiscardStatus(model.getDiscardStatus() != null ? model.getDiscardStatus().getCode() : null);
        }
        return facadeRequest;
    }

    /**
     * 退货信息
     * @param returnInfoVo
     * @return
     */
    private ReturnInfoFacade toReturnInfoFacade(ReturnInfoVo returnInfoVo){
        ReturnInfoFacade returnInfoFacade = new ReturnInfoFacade();
        returnInfoFacade.setReturnType(returnInfoVo.getReturnType());
        returnInfoFacade.setConsigneeFacade(toConsigneeFacade(returnInfoVo.getConsignee(),null));
        return returnInfoFacade;
    }

    /**
     * 商品信息
     *
     * @param goodsList
     * @return
     */
    private List<GoodsFacade> toGoodsFacades(List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        List<GoodsFacade> goodsFacadeList = new ArrayList<>(goodsList.size());
        goodsList.forEach(goods -> {
            GoodsFacade goodsFacade = new GoodsFacade();
            goodsFacade.setGoodsQuantity(goods.getGoodsQuantity());
            goodsFacade.setGoodsNo(goods.getGoodsNo());
            goodsFacade.setGoodsUniqueCode(goods.getGoodsUniqueCode());
            goodsFacade.setChannelGoodsNo(goods.getChannelGoodsNo());
            goodsFacade.setGoodsName(goods.getGoodsName());
            goodsFacade.setGoodsAmount(goods.getGoodsAmount());
            goodsFacade.setGoodsType(goods.getGoodsType());
            goodsFacade.setGoodsPrice(goods.getGoodsPrice());
            goodsFacade.setGoodsSerialInfos(goods.getGoodsSerialInfos());
            goodsFacade.setCombinationGoodsVersion(goods.getCombinationGoodsVersion());
            goodsFacade.setExtendProps(goods.getExtendProps());
            goodsFacade.setGoodsProductInfos(goods.getGoodsProductInfos());
            // 商品唯一编码
            goodsFacade.setGoodsUniqueCode(goods.getGoodsUniqueCode());

            if (CollectionUtils.isNotEmpty(goods.getAttachments())) {
                List<AttachmentInfo> attachmentList = goods.getAttachments().stream().map(attachmentInfo -> {
                    AttachmentInfo attachment = new AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());
                goodsFacade.setAttachmentInfos(attachmentList);
            }
            // 商品重量
            goodsFacade.setGoodsWeight(goods.getGoodsWeight());
            // 商品体积
            goodsFacade.setGoodsVolume(goods.getGoodsVolume());
            // 商品三维
            goodsFacade.setGoodsDimension(goods.getGoodsDimension());
            // 商品促销信息
            goodsFacade.setSalesInfos(goods.getSalesInfos());
            // 商品净重
            goodsFacade.setNetWeight(goods.getNetWeight());
            goodsFacadeList.add(goodsFacade);
        });
        return goodsFacadeList;
    }


    /**
     * 业务身份
     *
     * @param businessIdentity
     * @return
     */
    private BusinessIdentityFacade toOrderBusinessIdentity(OrderBusinessIdentity businessIdentity) {
        BusinessIdentityFacade businessIdentityFacade = new BusinessIdentityFacade();
        businessIdentityFacade.setBusinessType(businessIdentity.getBusinessType());
        businessIdentityFacade.setBusinessUnit(businessIdentity.getBusinessUnit());
        businessIdentityFacade.setBusinessScene(businessIdentity.getBusinessScene());
        businessIdentityFacade.setBusinessStrategy(businessIdentity.getBusinessStrategy());
        businessIdentityFacade.setFulfillmentUnit(businessIdentity.getFulfillmentUnit());
        return businessIdentityFacade;
    }


    /**
     * 客户信息转换
     *
     * @param customer
     * @return
     */
    public CustomerFacade toCustomerFacade(Customer customer) {
        if (customer == null) {
            return null;
        }
        CustomerFacade customerFacade = new CustomerFacade();
        //履约账号
        customerFacade.setAccountNo(customer.getAccountNo());
        customerFacade.setAccountId(customer.getAccountId());
        //履约账号名称
        customerFacade.setAccountName(customer.getAccountName());
        //履约账号2
        customerFacade.setAccount2No(customer.getAccountNo2());
        //履约账号名称2
        customerFacade.setAccount2Name(customer.getAccountName2());
        //履约账号名称3
        customerFacade.setAccount3No(customer.getAccountNo3());
        //履约账号名称3
        customerFacade.setAccount3Name(customer.getAccountName3());
        //客户类型(C2C散客挂月结必填)
        customerFacade.setCustomerType(customer.getCustomerType());
        //客户编号
        customerFacade.setCustomerNo(customer.getCustomerNo());
        return customerFacade;
    }

    /**
     * 渠道信息转换
     *
     * @param channel
     * @return
     */
    public ChannelFacade toChannelFacade(Channel channel) {
        if (channel == null) {
            return null;
        }
        ChannelFacade channelFacade = new ChannelFacade();
        //渠道号
        channelFacade.setChannelNo(channel.getChannelNo());
        //渠道客户编码
        channelFacade.setChannelCustomerNo(channel.getChannelCustomerNo());
        //客户订单号
        channelFacade.setCustomerOrderNo(channel.getCustomerOrderNo());
        //渠道订单号
        channelFacade.setChannelOrderNo(channel.getChannelOrderNo());
        //渠道操作时间
        channelFacade.setChannelOperateTime(channel.getChannelOperateTime());
        //客户的二级业务渠道客户编码(clothesCode，马甲公司)
        channelFacade.setSecondLevelChannelCustomerNo(channel.getSecondLevelChannelCustomerNo());
        //客户的二级业务渠道
        channelFacade.setSecondLevelChannel(channel.getSecondLevelChannel());
        //客户的二级业务渠道订单号
        channelFacade.setSecondLevelChannelOrderNo(channel.getSecondLevelChannelOrderNo());
        //客户的三级业务渠道订单号
        channelFacade.setThirdLevelChannelOrderNo(channel.getThirdLevelChannelOrderNo());
        //渠道调用方系统来源
        channelFacade.setSystemCaller(channel.getSystemCaller().getCode());
        //渠道调用方系统子来源
        channelFacade.setSystemSubCaller(channel.getSystemSubCaller());
        //扩展属性
        channelFacade.setExtendProps(channel.getExtendProps());
        return channelFacade;
    }

    /**
     * 产品信息转换
     *
     * @param products
     * @return
     */
    public List<ProductFacade> toProductFacades(List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        List<ProductFacade> productFacades = new ArrayList<>(products.size());
        for (Product product : products) {
            ProductFacade productFacade = new ProductFacade();
            //产品编码
            productFacade.setProductNo(product.getProductNo());
            //产品名称
            productFacade.setProductName(product.getProductName());
            //产品类型
            productFacade.setProductType(product.getProductType());
            //降级前产品编码
            productFacade.setOriginalProductNo(product.getOriginalProductNo());
            //降级前产品名称
            productFacade.setOriginalProductName(product.getOriginalProductName());
            //主产品编码
            productFacade.setParentNo(product.getParentNo());
            //扩展信息
            productFacade.setExtendProps(product.getExtendProps());
            //产品属性
            productFacade.setProductAttrs(product.getProductAttrs());
            productFacades.add(productFacade);
        }
        return productFacades;
    }

    /**
     * 发货信息对象转换
     *
     * @param consignor
     * @return
     */
    public ConsignorFacade toConsignorFacade(Consignor consignor, AddressExtend addressExtend) {
        if (consignor == null) {
            return null;
        }
        ConsignorFacade consignorFacade = new ConsignorFacade();
        //发货人名称
        consignorFacade.setConsignorName(consignor.getConsignorName());
        //发货人手机号
        consignorFacade.setConsignorMobile(consignor.getConsignorMobile());
        //发货人电话
        consignorFacade.setConsignorPhone(consignor.getConsignorPhone());
        //发货人公司
        consignorFacade.setConsignorCompany(consignor.getConsignorCompany());
        //发货人邮编
        consignorFacade.setConsignorZipCode(consignor.getConsignorZipCode());
        //发货人国家编码
        consignorFacade.setConsignorNationNo(consignor.getConsignorNationNo());
        //发货人国家
        consignorFacade.setConsignorNation(consignor.getConsignorNation());
        //发货人证件类型
        if (consignor.getConsignorIdType() != null) {
            consignorFacade.setConsignorIdType(consignor.getConsignorIdType().getCode());
        }
        //发货人证件号
        consignorFacade.setConsignorIdNo(consignor.getConsignorIdNo());
        //发货人证件姓名
        consignorFacade.setConsignorIdName(consignor.getConsignorIdName());
        //扩展信息
        consignorFacade.setExtendProps(consignor.getExtendProps());

        //发货仓信息
        Warehouse customerWarehouse = consignor.getCustomerWarehouse();
        if (null != customerWarehouse) {
            WarehouseInfo warehouseInfo = new WarehouseInfo();
            warehouseInfo.setWarehouseNo(customerWarehouse.getWarehouseNo());
            warehouseInfo.setWarehouseName(customerWarehouse.getWarehouseName());
            warehouseInfo.setWarehouseSource(customerWarehouse.getWarehouseSource());
            // 京东仓编码
            warehouseInfo.setActualWarehouseNo(customerWarehouse.getActualWarehouseNo());
            consignorFacade.setCustomerWarehouse(warehouseInfo);
        }
        //发货地址编码
        consignorFacade.setPickupPlaceCode(consignor.getPickupPlaceCode());
        //发货简称
        consignorFacade.setConsignorAddressAbbreviation(consignor.getConsignorAddressAbbreviation());
        //英文发货人姓名
        consignorFacade.setConsignorEnName(consignor.getConsignorEnName());

        Address consignorAddress = consignor.getAddress();
        if (consignorAddress != null) {
            AddressInfo addressInfo = new AddressInfo();
            //发货人省编码
            addressInfo.setProvinceNo(consignorAddress.getProvinceNo());
            //发货人省名称
            addressInfo.setProvinceName(consignorAddress.getProvinceName());
            //发货人市编码
            addressInfo.setCityNo(consignorAddress.getCityNo());
            //发货人市名称
            addressInfo.setCityName(consignorAddress.getCityName());
            //发货人区编码
            addressInfo.setCountyNo(consignorAddress.getCountyNo());
            //发货人区名称
            addressInfo.setCountyName(consignorAddress.getCountyName());
            //发货人镇编码
            addressInfo.setTownNo(consignorAddress.getTownNo());
            //发货人镇名称
            addressInfo.setTownName(consignorAddress.getTownName());
            //发货人镇详细地址
            addressInfo.setAddress(consignorAddress.getAddress());
            //解析后的发货人省编码
            addressInfo.setProvinceNoGis(consignorAddress.getProvinceNoGis());
            //解析后的发货人省名称
            addressInfo.setProvinceNameGis(consignorAddress.getProvinceNameGis());
            //解析后的发货人市编码
            addressInfo.setCityNoGis(consignorAddress.getCityNoGis());
            //解析后的发货人市名称
            addressInfo.setCityNameGis(consignorAddress.getCityNameGis());
            //解析后的发货人区编码
            addressInfo.setCountyNoGis(consignorAddress.getCountyNoGis());
            //解析后的发货人区名称
            addressInfo.setCountyNameGis(consignorAddress.getCountyNameGis());
            //解析后的发货人镇编码
            addressInfo.setTownNoGis(consignorAddress.getTownNoGis());
            //解析后的发货人镇名称
            addressInfo.setTownNameGis(consignorAddress.getTownNameGis());
            //解析后的详细地址
            addressInfo.setAddressGis(consignorAddress.getAddressGis());
            //gis解析精准度
            addressInfo.setPreciseGis(consignorAddress.getPreciseGis());
            //邮政编码
            addressInfo.setChinaPostAddressCode(consignorAddress.getChinaPostAddressCode());
            //纬度
            addressInfo.setLatitude(consignorAddress.getLatitude());
            //经度
            addressInfo.setLongitude(consignorAddress.getLongitude());
            //坐标系类型
            if (consignorAddress.getCoordinateType() != null) {
                addressInfo.setCoordinateType(consignorAddress.getCoordinateType().getCode());
            }
            // 围栏信任
            addressInfo.setFenceTrusted(consignorAddress.getFenceTrusted());
            // 围栏信息
            addressInfo.setFenceInfos(toFenceInfos(consignorAddress.getFenceInfos()));
            // 地址嵌套等级
            // addressInfo.setConflictLevel(consignorAddress.getConflictLevel());
            //地址标识信息 地址来源
            //上下文取出gis打标 地址来源
            if (null != addressExtend && null != addressExtend.getConsignorAddressSource()) {
                addressInfo.setAddressSource(addressExtend.getConsignorAddressSource());
            }
            //行政区编码
            addressInfo.setRegionNo(consignorAddress.getRegionNo());
            //行政区名称
            addressInfo.setRegionName(consignorAddress.getRegionName());
            //英文城市
            addressInfo.setEnCityName(consignorAddress.getEnCityName());
            //英文地址
            addressInfo.setEnAddress(consignorAddress.getEnAddress());
            addressInfo.setPoiCode(consignorAddress.getPoiCode());
            addressInfo.setPoiName(consignorAddress.getPoiName());
            addressInfo.setHouseNumber(consignorAddress.getHouseNumber());
            addressInfo.setExtendProps(consignorAddress.getExtendProps());
            consignorFacade.setAddress(addressInfo);
        }
        return consignorFacade;
    }

    /**
     * 围栏信息转换
     * @param fenceInfoList
     * @return
     */
    private List<FenceInfo> toFenceInfos(List<Fence> fenceInfoList) {
        if (CollectionUtils.isEmpty(fenceInfoList)) {
            return null;
        }
        List<FenceInfo> fenceInfos = new ArrayList<>(fenceInfoList.size());
        for (Fence fence : fenceInfoList) {
            FenceInfo fenceInfo = new FenceInfo();
            fenceInfo.setFenceId(fence.getFenceId());
            fenceInfo.setFenceType(fence.getFenceType());
            fenceInfos.add(fenceInfo);
        }
        return fenceInfos;
    }

    /**
     * 收货人对象转换
     *
     * @param consignee
     * @return
     */
    public ConsigneeFacade toConsigneeFacade(Consignee consignee, AddressExtend addressExtend) {
        if (consignee == null) {
            return null;
        }
        ConsigneeFacade consigneeFacade = new ConsigneeFacade();
        //收货人名称
        consigneeFacade.setConsigneeName(consignee.getConsigneeName());
        //收货人手机号
        consigneeFacade.setConsigneeMobile(consignee.getConsigneeMobile());
        //收货人电话
        consigneeFacade.setConsigneePhone(consignee.getConsigneePhone());
        //收货人公司
        consigneeFacade.setConsigneeCompany(consignee.getConsigneeCompany());
        //收货人邮编
        consigneeFacade.setConsigneeZipCode(consignee.getConsigneeZipCode());
        //收货人邮箱
        consigneeFacade.setConsigneeEmail(consignee.getConsigneeEmail());
        //收货人国家编码
        consigneeFacade.setConsigneeNationNo(consignee.getConsigneeNationNo());
        //收货人国家
        consigneeFacade.setConsigneeNation(consignee.getConsigneeNation());
        //收货人证件类型
        if (consignee.getConsigneeIdType() != null) {
            consigneeFacade.setConsigneeIdType(consignee.getConsigneeIdType().getCode());
        }
        //收货人证件号
        consigneeFacade.setConsigneeIdNo(consignee.getConsigneeIdNo());
        //收货人证件姓名
        consigneeFacade.setConsigneeIdName(consignee.getConsigneeIdName());
        //收货地编码（腾讯云仓使用）
        consigneeFacade.setDeliveryPlaceCode(consignee.getDeliveryPlaceCode());
        //地址简称
        consigneeFacade.setConsigneeAddressAbbreviation(consignee.getConsigneeAddressAbbreviation());
        //收货仓信息
        Warehouse receiveWarehouse = consignee.getReceiveWarehouse();
        if (receiveWarehouse != null) {
            WarehouseInfo warehouseInfo = new WarehouseInfo();
            //收货仓库编号
            warehouseInfo.setWarehouseNo(receiveWarehouse.getWarehouseNo());
            //收货仓库名称
            warehouseInfo.setWarehouseName(receiveWarehouse.getWarehouseName());
            //收货仓库类型
            warehouseInfo.setWarehouseSource(receiveWarehouse.getWarehouseSource());
            //收货京东发货仓
            warehouseInfo.setActualWarehouseNo(receiveWarehouse.getActualWarehouseNo());
            //收货仓信息
            consigneeFacade.setReceiveWarehouse(warehouseInfo);
        }

        Address consigneeAddress = consignee.getAddress();
        if (consigneeAddress != null) {
            AddressInfo addressInfo = new AddressInfo();
            //收货人省编码
            addressInfo.setProvinceNo(consigneeAddress.getProvinceNo());
            //收货人省名称
            addressInfo.setProvinceName(consigneeAddress.getProvinceName());
            //收货人市编码
            addressInfo.setCityNo(consigneeAddress.getCityNo());
            //收货人市名称
            addressInfo.setCityName(consigneeAddress.getCityName());
            //收货人区编码
            addressInfo.setCountyNo(consigneeAddress.getCountyNo());
            //收货人区名称
            addressInfo.setCountyName(consigneeAddress.getCountyName());
            //收货人镇编码
            addressInfo.setTownNo(consigneeAddress.getTownNo());
            //收货人镇名称
            addressInfo.setTownName(consigneeAddress.getTownName());
            //收货人镇详细地址
            addressInfo.setAddress(consigneeAddress.getAddress());
            //解析后的收货人省编码
            addressInfo.setProvinceNoGis(consigneeAddress.getProvinceNoGis());
            //解析后的收货人省名称
            addressInfo.setProvinceNameGis(consigneeAddress.getProvinceNameGis());
            //解析后的收货人市编码
            addressInfo.setCityNoGis(consigneeAddress.getCityNoGis());
            //解析后的收货人市名称
            addressInfo.setCityNameGis(consigneeAddress.getCityNameGis());
            //解析后的收货人区编码
            addressInfo.setCountyNoGis(consigneeAddress.getCountyNoGis());
            //解析后的收货人区名称
            addressInfo.setCountyNameGis(consigneeAddress.getCountyNameGis());
            //解析后的收货人镇编码
            addressInfo.setTownNoGis(consigneeAddress.getTownNoGis());
            //解析后的收货人镇名称
            addressInfo.setTownNameGis(consigneeAddress.getTownNameGis());
            //解析后的详细地址
            addressInfo.setAddressGis(consigneeAddress.getAddressGis());
            //gis解析精准度
            addressInfo.setPreciseGis(consigneeAddress.getPreciseGis());
            //邮政编码
            addressInfo.setChinaPostAddressCode(consigneeAddress.getChinaPostAddressCode());
            //纬度
            addressInfo.setLatitude(consigneeAddress.getLatitude());
            //经度
            addressInfo.setLongitude(consigneeAddress.getLongitude());
            //坐标系类型
            if (consigneeAddress.getCoordinateType() != null) {
                addressInfo.setCoordinateType(consigneeAddress.getCoordinateType().getCode());
            }
            // 围栏信任
            addressInfo.setFenceTrusted(consigneeAddress.getFenceTrusted());
            // 围栏信息
            addressInfo.setFenceInfos(toFenceInfos(consigneeAddress.getFenceInfos()));
            //地址嵌套等级
            addressInfo.setConflictLevel(consigneeAddress.getConflictLevel());
            //上下文取出gis打标 地址来源
            if (null != addressExtend && null != addressExtend.getConsigneeAddressSource()) {
                addressInfo.setAddressSource(addressExtend.getConsigneeAddressSource());
            }
            //行政区编码
            addressInfo.setRegionNo(consigneeAddress.getRegionNo());
            //行政区名称
            addressInfo.setRegionName(consigneeAddress.getRegionName());
            //英文城市
            addressInfo.setEnCityName(consigneeAddress.getEnCityName());
            //英文地址
            addressInfo.setEnAddress(consigneeAddress.getEnAddress());
            addressInfo.setPoiCode(consigneeAddress.getPoiCode());
            addressInfo.setPoiName(consigneeAddress.getPoiName());
            addressInfo.setHouseNumber(consigneeAddress.getHouseNumber());
            addressInfo.setExtendProps(consigneeAddress.getExtendProps());
            consigneeFacade.setAddress(addressInfo);
        }
        //扩展字段
        consigneeFacade.setExtendProps(consignee.getExtendProps());
        return consigneeFacade;
    }

    /**
     * 货品信息转换
     *
     * @param cargos
     * @return
     */
    public List<CargoFacade> toCargoFacades(List<Cargo> cargos) {
        if (CollectionUtils.isEmpty(cargos)) {
            return null;
        }
        List<CargoFacade> cargoFacades = new ArrayList<>(cargos.size());
        for (Cargo cargo : cargos) {
            CargoFacade cargoFacade = new CargoFacade();
            //货品编码
            cargoFacade.setCargoNo(cargo.getCargoNo());
            //货品名称
            cargoFacade.setCargoName(cargo.getCargoName());
            //货品类型
            cargoFacade.setCargoType(cargo.getCargoType());
            //货品重量
            cargoFacade.setCargoWeight(cargo.getCargoWeight());
            //货品体积
            cargoFacade.setCargoVolume(cargo.getCargoVolume());
            //货品尺寸
            cargoFacade.setCargoDimension(cargo.getCargoDimension());
            //货品数量
            cargoFacade.setCargoQuantity(cargo.getCargoQuantity());
            //货品内件数量
            cargoFacade.setCargoInnerQuantity(cargo.getCargoInnerQuantity());
            if (cargo.getPolluteSign() != null) {
                //清真易污染标识
                cargoFacade.setPolluteSign(cargo.getPolluteSign().getCode());
            }
            //货品拓展信cargoFacade.setExtendProps(cargo.getExtendProps());
            if (CollectionUtils.isNotEmpty(cargo.getAttachments())) {
                List<AttachmentInfo> attachmentList = cargo.getAttachments().stream().map(attachmentInfo -> {
                    AttachmentInfo attachment = new AttachmentInfo();
                    attachment.setAttachmentSortNo(attachmentInfo.getAttachmentSortNo());
                    //附件名称
                    attachment.setAttachmentName(attachmentInfo.getAttachmentName());
                    //附件业务类型
                    attachment.setAttachmentType(attachmentInfo.getAttachmentType());
                    //附件文档类型
                    attachment.setAttachmentDocType(attachmentInfo.getAttachmentDocType());
                    //附件路径
                    attachment.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
                    //附件备注
                    attachment.setAttachmentRemark(attachmentInfo.getAttachmentRemark());
                    return attachment;
                }).collect(Collectors.toList());
                cargoFacade.setAttachmentInfos(attachmentList);
            }

            cargoFacade.setSerialInfos(cargo.getSerialInfos());
            //货品备注
            cargoFacade.setCargoRemark(cargo.getCargoRemark());
            //扩展属性
            cargoFacade.setExtendProps(cargo.getExtendProps());
            //是否易损
            if (cargo.getCargoVulnerable() != null) {
                cargoFacade.setCargoVulnerable(cargo.getCargoVulnerable().getCode());
            }
            //货品标识
            cargoFacade.setCargoSign(cargo.getCargoSign());
            // 货品信息的增值服务
            cargoFacade.setCargoProductInfos(cargo.getCargoProductInfos());
            //隐私货品展示信息
            cargoFacade.setPrivacyCargoName(cargo.getPrivacyCargoName());
            cargoFacades.add(cargoFacade);
        }
        return cargoFacades;
    }

    /**
     * 商品信息转换
     *
     * @param goodsList
     * @return
     */
    public List<GoodsFacade> toGoodsFacadeDto(List<Goods> goodsList) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        List<GoodsFacade> goodsFacades = new ArrayList<>(goodsList.size());
        for (Goods goods : goodsList) {
            GoodsFacade goodsFacade = new GoodsFacade();
            //货品编码
            goodsFacade.setGoodsNo(goods.getGoodsNo());
            //货品名称
            goodsFacade.setGoodsName(goods.getGoodsName());
            //货品类型
            goodsFacade.setGoodsAmount(goods.getGoodsAmount());
            //货品重量
            goodsFacade.setGoodsQuantity(goods.getGoodsQuantity());
            //货品体积
            goodsFacade.setGoodsSerialInfos(goods.getGoodsSerialInfos());
            goodsFacades.add(goodsFacade);
        }
        return goodsFacades;
    }

    /**
     * 配送信息对象转换
     *
     * @param shipment
     * @return
     */
    public ShipmentFacade toShipmentFacade(Shipment shipment) {
        if (shipment == null) {
            return null;
        }
        ShipmentFacade shipmentFacade = new ShipmentFacade();
        //预计送达时间
        shipmentFacade.setPlanDeliveryTime(shipment.getPlanDeliveryTime());
        //预计送达时间段
        shipmentFacade.setPlanDeliveryPeriod(shipment.getPlanDeliveryPeriod());
        //期望配送开始时间
        shipmentFacade.setExpectDeliveryStartTime(shipment.getExpectDeliveryStartTime());
        //期望配送结束时间
        shipmentFacade.setExpectDeliveryEndTime(shipment.getExpectDeliveryEndTime());
        //期望取件开始时间
        shipmentFacade.setExpectPickupStartTime(shipment.getExpectPickupStartTime());
        //期望取件结束时间
        shipmentFacade.setExpectPickupEndTime(shipment.getExpectPickupEndTime());
        //取件类型
        if (shipment.getPickupType() != null) {
            shipmentFacade.setPickupType(shipment.getPickupType().getCode());
        }
        //派送类型
        if (shipment.getDeliveryType() != null) {
            shipmentFacade.setDeliveryType(shipment.getDeliveryType().getCode());
        }
        //取件员
        shipmentFacade.setCollector(shipment.getCollector());
        //运输方式
        if (shipment.getTransportType() != null) {
            shipmentFacade.setTransportType(shipment.getTransportType().getCode());
        }
        if (shipment.getWarmLayer() != null) {
            //温层
            shipmentFacade.setWarmLayer(shipment.getWarmLayer().getCode());
        }
        if (shipment.getContactlessType() != null) {
            shipmentFacade.setContactlessType(shipment.getContactlessType().getCode());
        }
        //揽收站点编码
        shipmentFacade.setStartStationNo(shipment.getStartStationNo());
        //揽收站点名称
        shipmentFacade.setStartStationName(shipment.getStartStationName());
        //揽收站点类型
        shipmentFacade.setStartStationType(shipment.getStartStationType());
        //派送站点名称
        shipmentFacade.setEndStationNo(shipment.getEndStationNo());
        //派送站点编码
        shipmentFacade.setEndStationName(shipment.getEndStationName());
        //目的站点类型
        shipmentFacade.setEndStationType(shipment.getEndStationType());
        //物流中转类型
        shipmentFacade.setTransitType(shipment.getTransitType());
        //发货仓库编码
        shipmentFacade.setWarehouseNo(shipment.getWarehouseNo());
        //收货仓库编码
        shipmentFacade.setReceiveWarehouseNo(shipment.getReceiveWarehouseNo());
        //车型
        shipmentFacade.setVehicleType(shipment.getVehicleType());
        //预计接单时间
        shipmentFacade.setPlanReceiveTime(shipment.getPlanReceiveTime());
        //取件码
        shipmentFacade.setPickupCode(shipment.getPickupCode());
        //取件码生成方式
        shipmentFacade.setPickupCodeCreateType(shipment.getPickupCodeCreateType());
        //服务要求
        shipmentFacade.setServiceRequirements(shipment.getServiceRequirements());
        //无接触收货方式
        if (shipment.getContactlessType() != null) {
            shipmentFacade.setContactlessType(shipment.getContactlessType().getCode());
        }
        //指定存放地址
        shipmentFacade.setAssignedAddress(shipment.getAssignedAddress());
        //扩展属性
        shipmentFacade.setExtendProps(shipment.getExtendProps());
        //无接触收货方式
        shipmentFacade.setContactlessType(shipment.getContactlessType() == null ? null : shipment.getContactlessType().getCode());
        //收货偏好
        shipmentFacade.setReceivingPreference(shipment.getReceivingPreference());
        shipmentFacade.setShipperNo(shipment.getShipperNo());
        shipmentFacade.setShipperName(shipment.getShipperName());
        shipmentFacade.setShipperType(shipment.getShipperType());
        //始发中心编码
        shipmentFacade.setStartCenterNo(shipment.getStartCenterNo());
        //目的中心编码
        shipmentFacade.setEndCenterNo(shipment.getEndCenterNo());
        //接驳站点
        shipmentFacade.setEndTransferStationNo(shipment.getEndTransferStationNo());
        // 期望派货开始时间
        shipmentFacade.setExpectDispatchStartTime(shipment.getExpectDispatchStartTime());
        // 期望派货结束时间
        shipmentFacade.setExpectDispatchEndTime(shipment.getExpectDispatchEndTime());
        shipmentFacade.setStartStationTypeL3(shipment.getStartStationTypeL3());
        shipmentFacade.setEndStationTypeL3(shipment.getEndStationTypeL3());
        return shipmentFacade;
    }

    /**
     * 财务信息转换
     *
     * @param finance
     * @return
     */
    public FinanceFacade toFinanceFacade(Finance finance) {
        if (finance == null) {
            return null;
        }
        FinanceFacade financeFacade = new FinanceFacade();
        if (finance.getSettlementType() != null) {
            financeFacade.setSettlementType(finance.getSettlementType().getCode());
        }
        //预估费用
        financeFacade.setEstimateAmount(finance.getEstimateAmount());
        // 预占金额
        financeFacade.setOccupyAmount(finance.getOccupyAmount());
        // 预占模式
        financeFacade.setOccupyMode(finance.getOccupyMode());

        //预估财务信息
        Optional.ofNullable(finance.getEstimateFinanceInfo()).ifPresent(estimateFinanceInfo -> {
            FinanceFacade estimateFinanceFacade = new FinanceFacade();
            // 预估-折前金额
            Optional.ofNullable(estimateFinanceInfo.getPreAmount()).ifPresent(estimateFinanceFacade::setPreAmount);
            // 预估-折后金额
            Optional.ofNullable(estimateFinanceInfo.getDiscountAmount()).ifPresent(estimateFinanceFacade::setDiscountAmount);
            // 预估-计费重量
            Optional.ofNullable(estimateFinanceInfo.getBillingWeight()).ifPresent(estimateFinanceFacade::setBillingWeight);
            // 预估-计费体积
            Optional.ofNullable(estimateFinanceInfo.getBillingVolume()).ifPresent(estimateFinanceFacade::setBillingVolume);
            // 预估-加价后总金额
            Optional.ofNullable(estimateFinanceInfo.getTotalAdditionAmount()).ifPresent(estimateFinanceFacade::setTotalAdditionAmount);
            // 预估-费用明细
            Optional.ofNullable(estimateFinanceInfo.getFinanceDetails()).ifPresent(estimateFinanceDetails -> {
                // 预估-费用明细信息
                List<FinanceDetailFacade> financeDetailFacades = Lists.newArrayListWithCapacity(estimateFinanceDetails.size());
                estimateFinanceDetails.forEach(estimateFinanceDetail -> {
                    // 预估-费用明细对象
                    FinanceDetailFacade estimateFinanceDetailFacade = new FinanceDetailFacade();
                    // 预估-费用编号
                    estimateFinanceDetailFacade.setCostNo(estimateFinanceDetail.getCostNo());
                    // 预估-费用名称
                    estimateFinanceDetailFacade.setCostName(estimateFinanceDetail.getCostName());
                    // 预估-费用产品编码
                    estimateFinanceDetailFacade.setProductNo(estimateFinanceDetail.getProductNo());
                    // 预估-费用产品名称
                    estimateFinanceDetailFacade.setProductName(estimateFinanceDetail.getProductName());
                    // 预估-折扣信息
                    if (CollectionUtils.isNotEmpty(estimateFinanceDetail.getDiscounts())) {
                        List<DiscountFacade> estimateDiscountFacades = new ArrayList<>(estimateFinanceDetail.getDiscounts().size());
                        estimateFinanceDetail.getDiscounts().forEach(estimateDiscount -> {
                            // 预估-折扣信息对象
                            DiscountFacade estimateDiscountFacade = new DiscountFacade();
                            // 预估-折扣码
                            estimateDiscountFacade.setDiscountNo(estimateDiscount.getDiscountNo());
                            // 预估-折扣类型
                            estimateDiscountFacade.setDiscountType(estimateDiscount.getDiscountType());
                            // 预估-折扣金额
                            estimateDiscountFacade.setDiscountedAmount(estimateDiscount.getDiscountedAmount());
                            estimateDiscountFacades.add(estimateDiscountFacade);
                        });
                        estimateFinanceDetailFacade.setDiscountFacades(estimateDiscountFacades);
                    }
                    // 预估-折前金额
                    estimateFinanceDetailFacade.setPreAmount(estimateFinanceDetail.getPreAmount());
                    // 预估-折后金额
                    estimateFinanceDetailFacade.setDiscountAmount(estimateFinanceDetail.getDiscountAmount());
                    // 预估-加价后金额
                    estimateFinanceDetailFacade.setAdditionAmount(estimateFinanceDetail.getAdditionAmount());
                    // 预估-扩展字段
                    estimateFinanceDetailFacade.setExtendProps(estimateFinanceDetail.getExtendProps());

                    financeDetailFacades.add(estimateFinanceDetailFacade);
                });

                estimateFinanceFacade.setFinanceDetails(financeDetailFacades);
            });
            // 预估-计费模式
            Optional.ofNullable(estimateFinanceInfo.getBillingMode()).ifPresent(estimateFinanceFacade::setBillingMode);

            financeFacade.setEstimateFinanceInfo(estimateFinanceFacade);
        });

        //折扣金额
        financeFacade.setDiscountAmount(finance.getDiscountAmount());
        //结算账号
        financeFacade.setSettlementAccountNo(finance.getSettlementAccountNo());
        //支付账号
        financeFacade.setPaymentAccountNo(finance.getPaymentAccountNo());
        //支付方式
        if (finance.getPayment() != null) {
            financeFacade.setPayment(finance.getPayment().getCode());
        }
        //支付状态
        financeFacade.setPaymentStatus(finance.getPaymentStatus() == null ? null : finance.getPaymentStatus().getStatus());
        //询价
        if (finance.getEnquiryType() != null) {
            financeFacade.setEnquiryType(finance.getEnquiryType().getCode());
        }
        // 询价状态
        if (null != finance.getEnquiryStatus()) {
            financeFacade.setEnquiryStatus(finance.getEnquiryStatus().getCode());
        }
        //支付截止时间
        financeFacade.setPayDeadline(finance.getPayDeadline());
        //退款状态
        financeFacade.setRefundStatus(finance.getRefundStatus() == null ? null : finance.getRefundStatus().getStatus());
        //折前金额
        financeFacade.setPreAmount(finance.getPreAmount());
        //总优惠金额
        financeFacade.setTotalDiscountAmount(finance.getTotalDiscountAmount());
        //计费重量
        financeFacade.setBillingWeight(finance.getBillingWeight());
        //计费体积
        financeFacade.setBillingVolume(finance.getBillingVolume());
        //计费模式
        financeFacade.setBillingMode(finance.getBillingMode());

        //收款机构
        financeFacade.setCollectionOrgNo(finance.getCollectionOrgNo());
        //付款环节
        financeFacade.setPaymentStage(finance.getPaymentStage() == null ? null : finance.getPaymentStage().getCode());
        //费用明细
        if (CollectionUtils.isNotEmpty(finance.getFinanceDetails())) {
            List<FinanceDetailFacade> financeDetailFacades = new ArrayList<>(finance.getFinanceDetails().size());
            for (FinanceDetail financeDetail : finance.getFinanceDetails()) {
                FinanceDetailFacade financeDetailFacade = new FinanceDetailFacade();
                //费用编码
                financeDetailFacade.setCostNo(financeDetail.getCostNo());
                //费用名称
                financeDetailFacade.setCostName(financeDetail.getCostName());
                //产品编码
                financeDetailFacade.setProductNo(financeDetail.getProductNo());
                //产品名称
                financeDetailFacade.setProductName(financeDetail.getProductName());
                //折前金额
                financeDetailFacade.setPreAmount(financeDetail.getPreAmount());
                if (CollectionUtils.isNotEmpty(financeDetail.getDiscounts())) {
                    List<DiscountFacade> discountFacades = new ArrayList<>(financeDetail.getDiscounts().size());
                    financeDetail.getDiscounts().forEach(discount -> {
                        DiscountFacade discountFacade = new DiscountFacade();
                        discountFacade.setDiscountNo(discount.getDiscountNo());
                        discountFacade.setDiscountType(discount.getDiscountType());
                        discountFacade.setDiscountedAmount(discount.getDiscountedAmount());
                        discountFacade.setOperateType(discount.getOperateType());
                        discountFacade.setExtendProps(discount.getExtendProps());
                        discountFacades.add(discountFacade);
                    });
                    financeDetailFacade.setDiscountFacades(discountFacades);
                }

                //折扣金额
                financeDetailFacade.setDiscountAmount(financeDetail.getDiscountAmount());
                //备注
                financeDetailFacade.setRemark(financeDetail.getRemark());

                financeDetailFacades.add(financeDetailFacade);
            }
            financeFacade.setFinanceDetails(financeDetailFacades);
        }
        // 支付单号
        financeFacade.setPaymentNo(finance.getPaymentNo());
        //积分信息
        financeFacade.setPointsFacade(toPointsFacade(finance.getPoints()));
        //白条预授权
        financeFacade.setPreemptType(finance.getPreemptType());
        //抵扣信息
        financeFacade.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) finance.getDeductionDelegate().getDeductions()));
        //计费方式
        financeFacade.setBillingType(finance.getBillingType());
        //收费要求
        financeFacade.setCostInfos(finance.getCostInfos());
        //附加费用
        financeFacade.setAttachFees(finance.getAttachFees());
        //附加费用
        financeFacade.setAttachFees(finance.getAttachFees());
        //预估税金
        financeFacade.setEstimatedTax(finance.getEstimatedTax());
        //真实税金
        financeFacade.setActualTax(finance.getActualTax());
        //扩展属性
        financeFacade.setExtendProps(finance.getExtendProps());
        //费用支付状态归集
        financeFacade.setPayStatusMap(finance.getPayStatusMap());
        // 税金结算方式
        if (finance.getTaxSettlementType() != null) {
            financeFacade.setTaxSettlementType(finance.getTaxSettlementType());
        }
        return financeFacade;
    }

    /**
     * 抵扣信息转换
     *
     * @param deductions
     * @return
     */
    private List<DeductionInfoDto> toDeductionInfoDtos(List<Deduction> deductions) {
        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }
        List<DeductionInfoDto> deductionInfoDtos = new ArrayList<>(deductions.size());
        deductions.forEach(deduction -> {
            if (deduction != null) {
                DeductionInfoDto dto = new DeductionInfoDto();
                //抵扣编码
                dto.setDeductionNo(deduction.getDeductionNo());
                // 抵扣金额
                dto.setDeductionAmount(MoneyMapper.INSTANCE.toMoneyInfoDto(deduction.getDeductionAmount()));
                //扩展信息
                dto.setExtendProps(deduction.getExtendProps());
                deductionInfoDtos.add(dto);
            }
        });
        return deductionInfoDtos;
    }

    /**
     * 积分信息转换
     * @param points
     * @return
     */
    private PointsFacade toPointsFacade(Points points) {
        if (points == null) {
            return null;
        }
        PointsFacade pointsFacade = new PointsFacade();
        if (points.getRedeemPointsQuantity() != null) {
            QuantityFacade quantityFacade = new QuantityFacade();
            quantityFacade.setUnit(points.getRedeemPointsQuantity().getUnit());
            quantityFacade.setValue(points.getRedeemPointsQuantity().getValue());
            pointsFacade.setRedeemPointsQuantity(quantityFacade);
        }
        if (points.getRedeemPointsAmount() != null) {
            MoneyFacade moneyFacade = new MoneyFacade();
            moneyFacade.setAmount(points.getRedeemPointsAmount().getAmount());
            moneyFacade.setCurrency(points.getRedeemPointsAmount().getCurrency());
            pointsFacade.setRedeemPointsAmount(moneyFacade);
        }
        return pointsFacade;
    }

    /**
     * 营销信息
     *
     * @param promotion
     * @return
     */
    public PromotionFacade toPromotionFacade(Promotion promotion) {
        if (promotion == null) {
            return null;
        }
        PromotionFacade promotionFacade = new PromotionFacade();
        //优惠券信息
        if (CollectionUtils.isNotEmpty(promotion.getTickets())) {
            List<TicketFacade> ticketFacades = new ArrayList<>(promotion.getTickets().size());
            for (Ticket ticket : promotion.getTickets()) {
                TicketFacade ticketFacade = new TicketFacade();
                //优惠券编码
                ticketFacade.setTicketNo(ticket.getTicketNo());
                ticketFacade.setTicketCategory(ticket.getTicketCategory());
                ticketFacade.setTicketType(ticket.getTicketType());
                ticketFacade.setTicketDescription(ticket.getTicketDescription());
                ticketFacade.setTicketDiscountAmount(ticket.getTicketDiscountAmount());
                ticketFacade.setTicketDiscountRate(ticket.getTicketDiscountRate());
                ticketFacade.setTicketDiscountUpperLimit(ticket.getTicketDiscountUpperLimit());
                ticketFacade.setCouponStatus(ticket.getCouponStatus());
                ticketFacade.setTicketUseAmount(ticket.getTicketUseAmount());
                //优惠券来源
                ticketFacade.setTicketSource(ticket.getTicketSource());
                ticketFacade.setTicketBatchNo(ticket.getTicketBatchNo());
                ticketFacades.add(ticketFacade);
            }
            promotionFacade.setTickets(ticketFacades);
        }
        //折扣信息
        if (CollectionUtils.isNotEmpty(promotion.getDiscounts())) {
            List<DiscountFacade> discountFacades = new ArrayList<>(promotion.getDiscounts().size());
            for (Discount discount : promotion.getDiscounts()) {
                DiscountFacade discountFacade = new DiscountFacade();
                //折扣码
                discountFacade.setDiscountNo(discount.getDiscountNo());
                //扩展信息
                discountFacade.setExtendProps(discount.getExtendProps());
                discountFacades.add(discountFacade);
            }
            promotionFacade.setDiscounts(discountFacades);
        }
        //营销标识
        if (CollectionUtils.isNotEmpty(promotion.getActivities())) {
            List<ActivityFacade> activityFacades = new ArrayList<>(promotion.getActivities().size());
            for (Activity activity : promotion.getActivities()) {
                ActivityFacade activityFacade = new ActivityFacade();
                //活动编码
                activityFacade.setActivityNo(activity.getActivityNo());
                //活动名称
                activityFacade.setActivityName(activity.getActivityName());
                //参加状态
                activityFacade.setActivityStatus(activity.getActivityStatus());
                activityFacade.setActivityValue(activity.getActivityValue());
                activityFacades.add(activityFacade);
            }
            promotionFacade.setActivities(activityFacades);
        }
        promotionFacade.setDiscountRefOrderNo(promotion.getDiscountRefOrderNo());
        return promotionFacade;
    }

    /**
     * 关联单信息转换
     *
     * @param model
     * @return
     */
    public List<RefOrderFacade> toRefOrderFacades(ExpressOrderModel model) {
        List<RefOrderFacade> refOrderFacades = new ArrayList<>();
        if (!model.getRefOrderInfoDelegate().isEmpty()) {
            //关联单信息转换
            for (RefOrder refOrder : model.getRefOrderInfoDelegate().getRefOrders()) {
                RefOrderFacade refOrderFacade = new RefOrderFacade();
                refOrderFacade.setRefOrderNo(refOrder.getRefOrderNo());
                refOrderFacade.setRefOrderType(refOrder.getRefOrderType().getCode());
                refOrderFacade.setRefOrderSubType(refOrder.getRefOrderSubType());
                refOrderFacade.setRemark(refOrder.getRemark());
                refOrderFacades.add(refOrderFacade);
            }
        }
        //TODO 折扣关联单,逻辑上提
        if (model.getPromotion() != null && StringUtils.isNotBlank(model.getPromotion().getDiscountRefOrderNo())) {
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            refOrderFacade.setRefOrderNo(model.getPromotion().getDiscountRefOrderNo());
            refOrderFacade.setRefOrderType(RefOrderTypeEnum.WORK_ORDER.getCode());
            refOrderFacade.setRefOrderSubType(RefOrderSubType.WorkOrderEnum.DISCOUNT.getCode());
            refOrderFacades.add(refOrderFacade);
        }
        /*新增，逆向单正向订单号和逆向订单号绑定新增一条数据，绑定关系为500与50000-1*/
        if (model.getOrderType().getCode().equals(OrderTypeEnum.RETURN_ORDER.getCode())) {
            RefOrderFacade refOrderFacade = new RefOrderFacade();
            // todo : 原单号临时定义已和品训沟通
            refOrderFacade.setRefOrderNo(model.getRefOrderInfoDelegate().getOriginalNo());
            refOrderFacade.setRefOrderType(Integer.parseInt(model.getOrderSnapshot().getOrderType().getCode()));
            refOrderFacades.add(refOrderFacade);
        }
        return refOrderFacades;
    }

    /**
     * 解决方案信息防腐层转换
     *
     * @param businessSolution
     * @return
     */
    public BusinessSolutionFacade toBusinessSolutionFacade(BusinessSolution businessSolution) {
        if (businessSolution == null) {
            return null;
        }
        BusinessSolutionFacade facade = new BusinessSolutionFacade();
        //编码
        facade.setBusinessSolutionNo(businessSolution.getBusinessSolutionNo());
        //名称
        facade.setBusinessSolutionName(businessSolution.getBusinessSolutionName());
        //要素
        facade.setProductAttrs(businessSolution.getProductAttrs());
        return facade;
    }

    /**
     * 协议信息集合转换
     * @param agreementDelegate
     * @return
     */
    private List<AgreementFacade> toAgreementFacades(AgreementDelegate agreementDelegate) {
        if (null == agreementDelegate || agreementDelegate.isEmpty()) {
            return null;
        }

        return agreementDelegate.getAgreementList()
                .stream()
                .filter(Objects::nonNull)
                .map(this::toAgreementFacade)
                .collect(Collectors.toList());
    }

    /**
     * 协议信息转换
     * @param agreement
     * @return
     */
    private AgreementFacade toAgreementFacade(Agreement agreement) {
        AgreementFacade agreementFacade = new AgreementFacade();
        agreementFacade.setAgreementType(agreement.getAgreementType());
        agreementFacade.setAgreementId(agreement.getAgreementId());
        agreementFacade.setSigner(agreement.getSigner());
        agreementFacade.setSigningTime(agreement.getSigningTime());
        agreementFacade.setExtendProps(agreement.getExtendProps());
        return agreementFacade;
    }

    /**
     * 履约信息
     * @param fulfillment
     */
    private FulfillmentFacade toFulfillmentFacade(Fulfillment fulfillment) {
        FulfillmentFacade fulfillmentFacade = new FulfillmentFacade();
        fulfillmentFacade.setFulfillmentSign(fulfillment.getFulfillmentSign());
        fulfillmentFacade.setExtendProps(fulfillment.getExtendProps());
        return fulfillmentFacade;
    }

    /**
     * 跨境报关信息转换
     */
    private CustomsFacade toCustomsFacade(Customs customs) {
        if (customs == null) {
            return null;
        }
        return CustomsFacadeMapper.INSTANCE.toCustomsFacade(customs);
    }

    /**
     * 附件列表转换
     */
    private List<AttachmentFacade> toAttachmentFacades(List<Attachment> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            return null;
        }
        return AttachmentFacadeMapper.INSTANCE.toAttachmentFacades(attachments);
    }


    /**
     * 重量信息转换
     */
    private WeightInfoDto toWeightInfo(Weight weight) {
        if (weight == null) {
            return null;
        }
        WeightInfoDto weightInfoDto = new WeightInfoDto();
        weightInfoDto.setValue(weight.getValue());
        weightInfoDto.setUnit(weight.getUnit());
        return weightInfoDto;
    }

    /**
     * 体积信息转换
     */
    private VolumeInfoDto toVolumeInfo(Volume volume) {
        if (volume == null) {
            return null;
        }
        VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
        volumeInfoDto.setValue(volume.getValue());
        volumeInfoDto.setUnit(volume.getUnit());
        return volumeInfoDto;
    }
}
