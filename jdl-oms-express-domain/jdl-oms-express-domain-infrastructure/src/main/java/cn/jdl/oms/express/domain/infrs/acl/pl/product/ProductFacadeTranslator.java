package cn.jdl.oms.express.domain.infrs.acl.pl.product;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.converter.AgreementMapper;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.AgreementInfoDto;
import cn.jdl.oms.express.domain.dto.BusinessSolutionInfoDto;
import cn.jdl.oms.express.domain.dto.CargoInfoDto;
import cn.jdl.oms.express.domain.dto.ChannelInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.dto.CustomerInfoDto;
import cn.jdl.oms.express.domain.dto.CustomsInfoDto;
import cn.jdl.oms.express.domain.dto.DimensionInfoDto;
import cn.jdl.oms.express.domain.dto.FenceInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.GoodsInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.dto.ShipmentInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.pl.orderbank.util.FreightGetFieldUtils;
import cn.jdl.oms.express.domain.infrs.acl.util.BusinessSceneUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ModifySceneRuleUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.SystemCallerUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedBusinessIdentityUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.WarehouseModeUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.PmsCheckSolutionOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.acl.util.ordersign.SupplyChainDeliveryOrderSignUtil;
import cn.jdl.oms.express.domain.infrs.annotation.ConvertMode;
import cn.jdl.oms.express.domain.infrs.annotation.UnitedBusinessIdentityConverter;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductAttrEnum;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.AttachmentKeyEnum;
import cn.jdl.oms.express.domain.spec.dict.FenceTrustEnum;
import cn.jdl.oms.express.domain.spec.dict.LengthTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.OrderSignEnum;
import cn.jdl.oms.express.domain.spec.dict.ShipmentExtendPropsEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.domain.spec.dict.WarmLayerEnum;
import cn.jdl.oms.express.domain.spec.model.ICargo;
import cn.jdl.oms.express.domain.infrs.acl.util.UnitedB2CUtil;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.AgreementDelegate;
import cn.jdl.oms.express.domain.vo.BusinessSolution;
import cn.jdl.oms.express.domain.vo.Cargo;
import cn.jdl.oms.express.domain.vo.CargoDelegate;
import cn.jdl.oms.express.domain.vo.Channel;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Customs;
import cn.jdl.oms.express.domain.vo.Dimension;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.GoodsDelegate;
import cn.jdl.oms.express.domain.vo.LengthInfo;
import cn.jdl.oms.express.domain.vo.OrderBusinessIdentity;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.domain.vo.ProductDelegate;
import cn.jdl.oms.express.domain.vo.Quantity;
import cn.jdl.oms.express.domain.vo.Shipment;
import cn.jdl.oms.express.domain.vo.Volume;
import cn.jdl.oms.express.domain.vo.Weight;
import cn.jdl.oms.express.shared.common.config.ExpressUccConfigCenter;
import cn.jdl.oms.express.shared.common.constant.BatrixSwitchKey;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.constant.ProductConstants;
import cn.jdl.oms.express.shared.common.constant.ShipmentConstants;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.oms.express.shared.common.dict.OperateTypeEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.utils.BatrixSwitch;
import cn.jdl.oms.express.shared.common.utils.DateUtils;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ProjectName：cn.jdl.oms.express.domain.infrs.acl.pl.product
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.product
 * @ClassName: ProductFacadeTranslator
 * @Description: 产品门面层转换器
 * @Author： zhangqi1026
 * @CreateDate 2020/3/20
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Translator
public class ProductFacadeTranslator {
    /** logger */
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductFacadeTranslator.class);

    /**
     * 签单返还快递
     */
    private static final String RE_RECEIVE_MODE = "reReceiveMode";

    /**
     * 发件地址围栏
     */
    private static final String START_FENCE = "startBarrierInfo";

    /**
     * 收件地址围栏
     */
    private static final String END_FENCE = "endBarrierInfo";

    /**
     * 散客挂月结 结算账户
     */
    private static final String SETTLEMENT_ACCOUNT = "settlementAccount";

    /** 运营模式 */
    private static final String OPERATION_MODE = "operationMode";

    /** 运输方式 */
    private static final String TRANSPORT_TYPE = "transportType";

    /**
     * 操作人
     */
    private static final String PIN = "pin";

    /**
     * 托寄物类型
     */
    private static final String CONSIGNMENT_CODE = "consignmentCode";

    /** 承运商预计送达时间 */
    private static final String CARRIER_PROMISE_DELIVERED_DATE = "carrierPromiseDeliveredDate";

    /**
     * 降级规则类型
     */
    private static final String RULE_TYPE = "ruleType";

    /**
     * 降级规则类型
     */
    private static final String DOWN_GRADE_MARK = "downGradeMark";

    /**
     * fr-a-0044产品要素-收货时间
     */
    private static final String ACTUAL_RECEIPT_DATE = "actualReceiptDate";

    /**
     * 订单所属人
     */
    private static final String ORDER_OWNER = "orderOwner";

    /**
     * 是否途中修改
     */
    private static final String MODIFY_EN_ROUTE = "modifyEnRoute";

    /**
     * 运单号
     */
    private static final String WAYBILL_CODE = "waybillCode";

    /**
     * 下单入口
     */
    private static final String ORDER_ORIGIN = "orderOrigin";
    /**
     * 下单人手机号
     */
    private static final String SECRET_SUPPLIER_IDENTITY = "secretSupplierIdentity";

    /**
     * 产品中心校验需要传入的业务身份
     */
    @Value("${pms.business.type:pms_service}")
    private String PMS_BUSINESS_TYPE;

    /**
     * 产品中心校验需要传入的业务类型
     */
    @Value("${pms.business.scene:pmscheck}")
    private String PMS_BUSINESS_SCENE;

    /**
     * DUCC 开关
     */
    @Resource
    private ExpressUccConfigCenter expressUccConfigCenter;

    /**
     * 构建请求入参
     *
     * @param context
     * @return
     */
    @UnitedBusinessIdentityConverter(convertMode = ConvertMode.BEFORE_ORIGINAL_AFTER_REAL)
    public ProductFacadeRequest toProductFacadeRequest(ExpressOrderContext context) {
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        ProductFacadeRequest productFacadeRequest = new ProductFacadeRequest();
        productFacadeRequest.setRequestProfile(expressOrderModel.requestProfile());
        OrderBusinessIdentity requestIdentity = expressOrderModel.getOrderBusinessIdentity();
        //优先取orderSign.pmsCheckSolution里的值，若为空使用默认值兜底
        String businessType = PmsCheckSolutionOrderSignUtil.value(expressOrderModel);
        if (StringUtils.isBlank(businessType)) {
            businessType = PMS_BUSINESS_TYPE;
        }
        BusinessIdentity identity = new BusinessIdentity(requestIdentity.getBusinessUnit(), businessType, requestIdentity.getBusinessScene(), requestIdentity.getBusinessStrategy());
        identity.setFulfillmentUnit(requestIdentity.getFulfillmentUnit());
        productFacadeRequest.setBusinessIdentity(identity);
        productFacadeRequest.setCustomerInfoDto(toCustomerInfoDto(expressOrderModel.getCustomer()));
        this.updateByIndividualMsType(expressOrderModel, productFacadeRequest.getCustomerInfoDto());
        List<Cargo> cargoList = (List<Cargo>) expressOrderModel.getCargoDelegate().getCargoList();
        productFacadeRequest.setCargoInfoDtoList(toCargoInfoDtoList(cargoList));
        productFacadeRequest.setChannelInfoDto(toChannelInfoDto(expressOrderModel.getChannel()));
        productFacadeRequest.setShipmentInfoDto(toShipmentInfoDto(expressOrderModel.getShipment()));
        productFacadeRequest.setFinanceInfoDto(toFinanceInfoDto(expressOrderModel.getFinance()));
        productFacadeRequest.setGoodsInfoDtoList(toGoodsInfoDtoList((List<Goods>)expressOrderModel.getGoodsDelegate().getGoodsList(), expressOrderModel.isHKMO()));
        productFacadeRequest.setProductInfoDtoList(toProductInfoDtoList((List<Product>) expressOrderModel.getProductDelegate().getProducts(), expressOrderModel.getShipment().getServiceRequirements(), context.isNeedDeleteOperationMode()));
        productFacadeRequest.setConsigneeInfoDto(toConsigneeInfoDto(expressOrderModel.getConsignee()));
        productFacadeRequest.setConsignorInfoDto(toConsignorInfoDto(expressOrderModel.getConsignor()));
        productFacadeRequest.setOperateTime(expressOrderModel.getOperateTime());
        //解决方案
        productFacadeRequest.setBusinessSolutionInfoDto(toBusinessSolutionInfoDto(expressOrderModel.getBusinessSolution()));
        // 协议信息
        productFacadeRequest.setAgreementInfoList(this.toAgreementInfoDtoList(expressOrderModel.getAgreementDelegate()));
        // 扩展字段
        productFacadeRequest.setExtendProps(this.toExtendProps(context));
        // 跨境报关域信息
        productFacadeRequest.setCustomsInfoDto(toCustomsInfoDto(expressOrderModel.getCustoms()));
        return productFacadeRequest;
    }

    /**
     * 构建请求入参
     *
     * @param context
     * @return
     */
    public ProductFacadeRequest toUEPC2BProductFacadeRequest(ExpressOrderContext context) {
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        ProductFacadeRequest productFacadeRequest = new ProductFacadeRequest();
        productFacadeRequest.setRequestProfile(expressOrderModel.requestProfile());
        OrderBusinessIdentity requestIdentity = expressOrderModel.getOrderBusinessIdentity();
        BusinessIdentity identity = new BusinessIdentity(requestIdentity.getBusinessUnit(), requestIdentity.getBusinessType(), requestIdentity.getBusinessScene(), requestIdentity.getBusinessStrategy());
        identity.setFulfillmentUnit(requestIdentity.getFulfillmentUnit());
        productFacadeRequest.setBusinessIdentity(identity);
        productFacadeRequest.setCustomerInfoDto(toCustomerInfoDto(expressOrderModel.getCustomer()));
        List<Cargo> cargoList = (List<Cargo>) expressOrderModel.getCargoDelegate().getCargoList();
        productFacadeRequest.setCargoInfoDtoList(toCargoInfoDtoList(cargoList));
        productFacadeRequest.setChannelInfoDto(toChannelInfoDto(expressOrderModel.getChannel()));
        productFacadeRequest.setShipmentInfoDto(toShipmentInfoDto(expressOrderModel.getShipment()));
        productFacadeRequest.setFinanceInfoDto(toFinanceInfoDto(expressOrderModel.getFinance()));
        productFacadeRequest.setGoodsInfoDtoList(toUEPGoodsInfoDtoList(expressOrderModel.getGoodsDelegate()));
        productFacadeRequest.setProductInfoDtoList(toProductInfoDtoList((List<Product>) expressOrderModel.getProductDelegate().getProducts(), expressOrderModel.getShipment().getServiceRequirements(), context.isNeedDeleteOperationMode()));
        productFacadeRequest.setConsigneeInfoDto(toConsigneeInfoDto(expressOrderModel.getConsignee()));
        productFacadeRequest.setConsignorInfoDto(toConsignorInfoDto(expressOrderModel.getConsignor()));
        productFacadeRequest.setOperateTime(expressOrderModel.getOperateTime());
        //解决方案
        productFacadeRequest.setBusinessSolutionInfoDto(toBusinessSolutionInfoDto(expressOrderModel.getBusinessSolution()));
        // 扩展字段
        productFacadeRequest.setExtendProps(this.toExtendProps(context));
        return productFacadeRequest;
    }

    /**
     * 构建请求入参
     *
     * @param context
     * @return
     */
    public ProductFacadeRequest toUEPC2BModifyProductFacadeRequest(ExpressOrderContext context) {
        //源订单
        ExpressOrderModel snapshot = context.getOrderModel().getOrderSnapshot();
        //现订单
        ExpressOrderModel model = context.getOrderModel();
        ProductFacadeRequest productFacadeRequest = new ProductFacadeRequest();
        /*业务信息*/
        productFacadeRequest.setRequestProfile(model.requestProfile());
        OrderBusinessIdentity requestIdentity = model.getOrderBusinessIdentity();
        BusinessIdentity businessIdentity = new BusinessIdentity(requestIdentity.getBusinessUnit(), requestIdentity.getBusinessType(), requestIdentity.getBusinessScene());
        businessIdentity.setBusinessStrategy(StringUtils.isNotBlank(requestIdentity.getBusinessStrategy()) ? requestIdentity.getBusinessStrategy() : model.getOrderSnapshot().getOrderBusinessIdentity().getBusinessStrategy());
        productFacadeRequest.setBusinessIdentity(businessIdentity);
        /*客户信息*/
        productFacadeRequest.setCustomerInfoDto(toCustomerModifyInfoDto(snapshot.getCustomer(), model.getCustomer()));
        /*货品信息 只支持全量更新 */
        if (model.getCargoDelegate().isEmpty()) {
            // 当前无，取快照
            productFacadeRequest.setCargoInfoDtoList(toCargoInfoDtoList((List<Cargo>) snapshot.getCargoDelegate().getCargoList()));
        } else {
            productFacadeRequest.setCargoInfoDtoList(toCargoInfoDtoList((List<Cargo>) model.getCargoDelegate().getCargoList()));
        }
        /*商品信息 只支持全量更新 目前UEP业务不会修改商品信息 */
        if (model.getGoodsDelegate().isEmpty()) {
            productFacadeRequest.setGoodsInfoDtoList(this.toUEPGoodsInfoDtoList(snapshot.getGoodsDelegate()));
        } else {
            productFacadeRequest.setGoodsInfoDtoList(this.toUEPGoodsInfoDtoList(model.getGoodsDelegate()));
        }
        /*渠道信息*/
        productFacadeRequest.setChannelInfoDto(toChannelModifyInfoDto(snapshot.getChannel(), model.getChannel()));
        /*配送信息*/
        productFacadeRequest.setShipmentInfoDto(toShipmentModifyInfoDto(snapshot, model));
        /*财务信息*/
        productFacadeRequest.setFinanceInfoDto(toFinanceModifyInfoDto(snapshot.getFinance(), model.getFinance()));
        /*产品信息*/
        List<Product> sourceProducts = (List<Product>) snapshot.getProductDelegate().getProducts();
        List<Product> products = (List<Product>) model.getProductDelegate().getProducts();
        productFacadeRequest.setProductInfoDtoList(
                toProductModifyInfoDtoList(
                    sourceProducts.stream().filter(product -> !OperateTypeEnum.DELETE.equals(product.getOperateType())).collect(Collectors.toList()),
                    products == null ? null : products.stream().filter(product -> !OperateTypeEnum.DELETE.equals(product.getOperateType())).collect(Collectors.toList()),
                    model.getShipment().getServiceRequirements(), context.isNeedDeleteOperationMode()
                ));
        productFacadeRequest.setConsigneeInfoDto(toConsigneeModifyInfoInfoDto(snapshot.getConsignee(), model.getConsignee()));
        productFacadeRequest.setConsignorInfoDto(toConsignorModifyInfoDto(snapshot.getConsignor(), model.getConsignor()));
        productFacadeRequest.setOperateTime(model.getOperateTime());
        // 扩展字段
        productFacadeRequest.setExtendProps(this.toModifyExtendProps(context));
        return productFacadeRequest;
    }

    /**
     * 协议信息列表转换
     * @param context
     * @return
     */
    private List<AgreementInfoDto> toModifyAgreementInfoDtoList(ExpressOrderContext context) {
        // 如果全量删除场景，不传协议信息
        if (context.getChangedPropertyDelegate().agreementInfosHaveDeleted()) {
            return null;
        }

        AgreementDelegate agreementDelegate = context.getOrderModel().getAgreementDelegate();

        // 判断当前单有无协议信息
        // 协议信息只能全量更新，所以当前单有协议信息则使用当前单
        List<AgreementInfoDto> agreementInfoDtos = toAgreementInfoDtoList(agreementDelegate);
        if (CollectionUtils.isNotEmpty(agreementInfoDtos)) {
            return agreementInfoDtos;
        }

        // 当前单无协议信息，取快照协议信息
        if (null != context.getOrderModel().getOrderSnapshot()) {
            return toAgreementInfoDtoList(context.getOrderModel().getOrderSnapshot().getAgreementDelegate());
        }
        // 无快照 返回空
        return null;
    }

    /**
     * 协议信息列表转换
     * @param agreementDelegate
     * @return
     */
    private List<AgreementInfoDto> toAgreementInfoDtoList(AgreementDelegate agreementDelegate) {
        if (null == agreementDelegate || agreementDelegate.isEmpty()) {
            return null;
        }

        return agreementDelegate.getAgreementList().stream()
                .map(AgreementMapper.INSTANCE::toAgreementInfoDto)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 产品校验请求扩展信息转换
     * @param context 上下文
     * @return Map<String, String>
     */
    private Map<String, String> toExtendProps(ExpressOrderContext context) {
        Map<String, String> extendProps = new HashMap<>();
        ExpressOrderModel orderModel = context.getOrderModel();
        // 始发围栏信任
        if (null != orderModel.getConsignor() && null != orderModel.getConsignor().getAddress()) {
            Address consignorAddress = orderModel.getConsignor().getAddress();
            if (FenceTrustEnum.TRUSTED.getCode().equals(consignorAddress.getFenceTrusted())) {
                extendProps.put(START_FENCE, toProductFenceInfo(consignorAddress));
            }
        }
        // 目的围栏信任
        if (null != orderModel.getConsignee() && null != orderModel.getConsignee().getAddress()) {
            Address consigneeAddress = orderModel.getConsignee().getAddress();
            if (FenceTrustEnum.TRUSTED.getCode().equals(consigneeAddress.getFenceTrusted())) {
                extendProps.put(END_FENCE, toProductFenceInfo(consigneeAddress));
            }
        }

        // 【散客挂月结 结算账户】
        Optional.ofNullable(orderModel.getFinance())
                .map(Finance::getSettlementAccountNo)
                .filter(StringUtils::isNotBlank)
                .ifPresent(settlementAccNo -> extendProps.put(SETTLEMENT_ACCOUNT, settlementAccNo));

        // 操作人
        extendProps.put(PIN, getPin(context));

        // 托寄物编码
        Optional.ofNullable(orderModel.getCargoDelegate())
                .map(CargoDelegate::firstCargo)
                .map(ICargo::getCargoType)
                .filter(StringUtils::isNotBlank)
                .ifPresent(cargoType -> extendProps.put(CONSIGNMENT_CODE, cargoType));

        // TODO secretSupplierIdentity orderOrigin dadaOperationMode
        String creatorPhone = orderModel.getAttachment(AttachmentKeyEnum.CREATOR_PHONE.getKey());
        if (StringUtils.isNotBlank(creatorPhone)) {
            extendProps.put(SECRET_SUPPLIER_IDENTITY, creatorPhone);
        }

        Channel channel = orderModel.getChannel();
        String orderOrigin = MapUtils.getString(channel.getExtendProps(), AttachmentKeyEnum.DADA_ENTRANCE.getKey());
        if (StringUtils.isNotBlank(orderOrigin)) {
            extendProps.put(ORDER_ORIGIN, orderOrigin);
        }

        String originId = MapUtils.getString(channel.getExtendProps(), AttachmentKeyEnum.ORIGIN_ID.getKey());
        if (StringUtils.isNotBlank(originId)) {
            extendProps.put(AttachmentKeyEnum.ORIGIN_ID.getKey(), originId);
        }

        String dadaOpMode = MapUtils.getString(channel.getExtendProps(), AttachmentKeyEnum.DADA_OP_MODE.getKey());
        if (StringUtils.isNotBlank(dadaOpMode)) {
            extendProps.put(AttachmentKeyEnum.DADA_OP_MODE.getKey(), dadaOpMode);
        }

        return extendProps;
    }

    /**
     * 围栏信息序列化 （按产品中心文档要求）
     * @param address
     * @return
     */
    private static String toProductFenceInfo(Address address) {
        List<Fence> fences = Optional.ofNullable(address.getFenceInfos())
                                    .orElse(Collections.emptyList())
                                    .stream()
                                    .map(fenceInfo -> new Fence(fenceInfo.getFenceId(), fenceInfo.getFenceType()))
                                    .collect(Collectors.toList());
        return JSONUtils.beanToJSONDefault(fences);
    }

    /**
     * 构建UEP商品信息列表
     * @param goodsDelegate
     * @return
     */
    private List<GoodsInfoDto> toUEPGoodsInfoDtoList(GoodsDelegate goodsDelegate) {
        if (null == goodsDelegate || CollectionUtils.isEmpty(goodsDelegate.getGoodsList())) {
            return null;
        }

        return ((List<Goods>) goodsDelegate.getGoodsList())
                .stream()
                .map(this::toUEPGoodsInfoDto)
                .collect(Collectors.toList());
    }

    /**
     * 商品编码
     */
    private static final String GOODS_NO = "goodsNo";

    /**
     * 商品信息转换
     * @param goods
     * @return
     */
    private GoodsInfoDto toUEPGoodsInfoDto(Goods goods) {
        GoodsInfoDto goodsInfoDto = this.toGoodsInfoDto(goods);
        // 扩展信息
        Map<String, String> extProps = (null == goods.getExtendProps()) ? new HashMap<>() : goods.getExtendProps();
        // 商品编码
        extProps.put(GOODS_NO, goods.getGoodsNo());
        goodsInfoDto.setExtendProps(extProps);
        return goodsInfoDto;
    }

    /**
     * 商品信息转换
     * @param goods
     * @return
     */
    private GoodsInfoDto toGoodsInfoDto(Goods goods) {
        GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
        // 商品唯一码
        goodsInfoDto.setGoodsUniqueCode(goods.getGoodsUniqueCode());
        // 商品维度增值服务
        goodsInfoDto.setGoodsProductInfos(this.toProductInfoDtos(goods.getGoodsProductInfos()));
        return goodsInfoDto;
    }

    /**
     * 产品信息列表转换
     * @param products
     * @return
     */
    private List<ProductInfoDto> toProductInfoDtos(List<Product> products) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }

        return products.stream()
                .map(this::toProductInfoDto)
                .collect(Collectors.toList());
    }

    /**
     * 产品信息转换
     * @param product
     * @return
     */
    private ProductInfoDto toProductInfoDto(Product product) {
        ProductInfoDto productInfo = new ProductInfoDto();
        productInfo.setProductNo(product.getProductNo());
        productInfo.setProductName(product.getProductName());
        productInfo.setParentNo(product.getParentNo());
        productInfo.setProductType(product.getProductType());
        productInfo.setExtendProps(product.getExtendProps());
        productInfo.setProductAttrs(product.getProductAttrs());
        return productInfo;
    }

    /**
     * 构建商品信息
     * @param goodsList
     * @return
     */
    private List<GoodsInfoDto> toGoodsInfoDtoList(List<Goods> goodsList, boolean isHKMO) {
        if (CollectionUtils.isEmpty(goodsList)) {
            return null;
        }
        List<GoodsInfoDto> goodsInfoDtoList = new ArrayList<>(goodsList.size());
        goodsList.forEach(good -> {
            String goodsUniqueCode = null;
            // 商品唯一码
            if(StringUtils.isNotBlank(good.getGoodsUniqueCode())){
                goodsUniqueCode = good.getGoodsUniqueCode();
            }else{
                Map<String, String> extendProps = good.getExtendProps();
                if(MapUtils.isNotEmpty(extendProps)){
                    goodsUniqueCode = extendProps.get(OrderConstants.GOODS_UNIQUE_CODE);
                }
            }
            if(StringUtils.isNotBlank(goodsUniqueCode)){
                GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
                goodsInfoDto.setGoodsUniqueCode(goodsUniqueCode);
                // 商品维度增值服务列表
                if(CollectionUtils.isNotEmpty(good.getGoodsProductInfos())){
                    List<ProductInfoDto> productInfoDtos = good.getGoodsProductInfos().stream().map(goodsProduct ->{
                        ProductInfoDto productInfo = new ProductInfoDto();
                        productInfo.setProductNo(goodsProduct.getProductNo());
                        productInfo.setProductName(goodsProduct.getProductName());
                        productInfo.setParentNo(goodsProduct.getParentNo());
                        productInfo.setProductType(goodsProduct.getProductType());
                        productInfo.setExtendProps((HashMap<String, String>) goodsProduct.getExtendProps());
                        productInfo.setProductAttrs(goodsProduct.getProductAttrs());
                        return productInfo;
                    }).collect(Collectors.toList());
                    goodsInfoDto.setGoodsProductInfos(productInfoDtos);
                }
                // 商品类型
                if(StringUtils.isNotBlank(good.getGoodsType())){
                    goodsInfoDto.setGoodsType(good.getGoodsType());
                }
                // 商品编码
                if(StringUtils.isNotBlank(good.getGoodsNo())){
                    goodsInfoDto.setGoodsNo(good.getGoodsNo());
                }
                if(isHKMO){
                    //港澳订单goodsNo被赋值为托寄物ID,调用产品中心需要进行商品品类校验
                    //后续大网怎么兼容goods，cargo进行托寄物品类校验待评估拉齐@shenyuxiao
                    try {
                        Long goodsCategoryCode = Long.valueOf(good.getGoodsNo());
                        goodsInfoDto.setGoodsCategoryCode(goodsCategoryCode);
                    }catch (Exception e){
                        LOGGER.error("港澳订单托寄物商品品类转换异常",e);
                    }
                }
                goodsInfoDtoList.add(goodsInfoDto);
            }
        });

        // 港澳项目上线，需要传 goodsType ，因此不过滤没有 goodsProductInfos 的商品

        // todo 之后是否继续过滤 or 更改返回的对象
        return goodsInfoDtoList;
    }

    /**
     * 构建发货信息
     *
     * @param consignor
     * @return
     */
    private ConsignorInfoDto toConsignorInfoDto(Consignor consignor) {
        if (consignor == null) {
            return null;
        }
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();
        consignorInfoDto.setConsignorName(consignor.getConsignorName());
        consignorInfoDto.setConsignorMobile(consignor.getConsignorMobile());
        consignorInfoDto.setConsignorPhone(consignor.getConsignorPhone());
        consignorInfoDto.setAddressInfoDto(toAddressInfoDto(consignor.getAddress()));
        return consignorInfoDto;
    }

    /**
     * 构建地址信息
     *
     * @param address
     * @return
     */
    private AddressInfoDto toAddressInfoDto(Address address) {
        if (address == null) {
            return null;
        }
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        addressInfoDto.setRegionNo(address.getRegionNo());
        addressInfoDto.setRegionName(address.getRegionName());
        addressInfoDto.setProvinceNo(address.getProvinceNo());
        addressInfoDto.setProvinceName(address.getProvinceName());
        addressInfoDto.setCityNo(address.getCityNo());
        addressInfoDto.setCityName(address.getCityName());
        addressInfoDto.setCountyNo(address.getCountyNo());
        addressInfoDto.setCountyName(address.getCountyName());
        addressInfoDto.setTownNo(address.getTownNo());
        addressInfoDto.setTownName(address.getTownName());
        addressInfoDto.setAddress(address.getAddress());
        addressInfoDto.setCoordinateType(address.getCoordinateType());
        addressInfoDto.setLongitude(address.getLongitude());
        addressInfoDto.setLatitude(address.getLatitude());
        addressInfoDto.setProvinceNoGis(address.getProvinceNoGis());
        addressInfoDto.setProvinceNameGis(address.getProvinceNameGis());
        addressInfoDto.setCityNoGis(address.getCityNoGis());
        addressInfoDto.setCityNameGis(address.getCityNameGis());
        addressInfoDto.setCountyNoGis(address.getCountyNoGis());
        addressInfoDto.setCountyNameGis(address.getCountyNameGis());
        addressInfoDto.setTownNoGis(address.getTownNoGis());
        addressInfoDto.setTownNameGis(address.getTownNameGis());
        addressInfoDto.setAddressGis(address.getAddressGis());
        //GIS地址可信度
        addressInfoDto.setPreciseGis(address.getPreciseGis());
        // 围栏信任
        addressInfoDto.setFenceTrusted(address.getFenceTrusted());
        // 围栏信息 目前只有传信任标识才会传入围栏信息
        if (FenceTrustEnum.TRUSTED.getCode().equals(address.getFenceTrusted())) {
            Optional.ofNullable(address.getFenceInfos()).ifPresent(fenceInfos -> {
                List<FenceInfoDto> fenceInfoDtos = new ArrayList<>(fenceInfos.size());
                fenceInfos.forEach(fenceInfo -> {
                    FenceInfoDto fenceInfoDto = new FenceInfoDto();
                    fenceInfoDto.setFenceId(fenceInfo.getFenceId());
                    fenceInfoDto.setFenceType(fenceInfo.getFenceType());
                    fenceInfoDtos.add(fenceInfoDto);
                });
                addressInfoDto.setFenceInfos(fenceInfoDtos);
            });
        }
        return addressInfoDto;

    }

    /**
     * 构建收货信息
     *
     * @param consignee
     * @return
     */
    private ConsigneeInfoDto toConsigneeInfoDto(Consignee consignee) {
        if (consignee == null) {
            return null;
        }
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        consigneeInfoDto.setConsigneeName(consignee.getConsigneeName());
        consigneeInfoDto.setConsigneeMobile(consignee.getConsigneeMobile());
        consigneeInfoDto.setConsigneePhone(consignee.getConsigneePhone());
        consigneeInfoDto.setAddressInfoDto(toAddressInfoDto(consignee.getAddress()));
        return consigneeInfoDto;
    }

    /**
     * 构建产品信息
     *
     * @param products
     * @return
     */
    private List<ProductInfoDto> toProductInfoDtoList(List<Product> products, Map<String,String> map, boolean needDeleteOperationMode) {
        if (CollectionUtils.isEmpty(products)) {
            return null;
        }
        List<ProductInfoDto> productInfoDtoList = new LinkedList<>();
        for (Product product : products) {
            // 过滤产品
            if (isSkipProduct(product)) {
                continue;
            }
            ProductInfoDto productInfoDto = new ProductInfoDto();
            productInfoDto.setProductNo(product.getProductNo());
            productInfoDto.setProductName(product.getProductName());
            productInfoDto.setProductType(product.getProductType());
            productInfoDto.setParentNo(product.getParentNo());
            productInfoDto.setProductAttrs(product.getProductAttrs());
            productInfoDto.setExtendProps(product.getExtendProps());
            if (map != null) {
                if (productInfoDto.getExtendProps() == null) {
                    Map<String, String> hashMap = new HashMap<>();
                    hashMap.put(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode(), map.get(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode()));
                    productInfoDto.setExtendProps(hashMap);
                } else {
                    productInfoDto.getExtendProps().put(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode(), map.get(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode()));
                }
            }
            if(1 == productInfoDto.getProductType() && needDeleteOperationMode){
                if(MapUtils.isNotEmpty(productInfoDto.getExtendProps())){
                    productInfoDto.getExtendProps().remove(ShipmentConstants.OPERATION_MODE);
                }
            }
            productInfoDtoList.add(productInfoDto);
        }
        return productInfoDtoList;
    }

    /**
     * 构建财务信息
     *
     * @param finance
     * @return
     */
    private FinanceInfoDto toFinanceInfoDto(Finance finance) {
        if (finance == null) {
            return null;
        }
        FinanceInfoDto productFinanceInfoDto = new FinanceInfoDto();
        productFinanceInfoDto.setSettlementType(finance.getSettlementType());
        productFinanceInfoDto.setSettlementAccountNo(finance.getSettlementAccountNo());
        return productFinanceInfoDto;
    }


    /**
     * 构建配送信息
     *
     * @param shipment
     * @return
     */
    private ShipmentInfoDto toShipmentInfoDto(Shipment shipment) {
        if (shipment == null) {
            return null;
        }
        ShipmentInfoDto shipmentInfoDto = new ShipmentInfoDto();
        shipmentInfoDto.setPickupType(shipment.getPickupType());
        shipmentInfoDto.setDeliveryType(shipment.getDeliveryType());
        shipmentInfoDto.setStartStationNo(shipment.getStartStationNo());
        shipmentInfoDto.setEndStationNo(shipment.getEndStationNo());
        shipmentInfoDto.setWarmLayer(shipment.getWarmLayer());
        shipmentInfoDto.setExpectPickupStartTime(shipment.getExpectPickupStartTime());
        shipmentInfoDto.setExpectPickupEndTime(shipment.getExpectPickupEndTime());
        return shipmentInfoDto;
    }

    /**
     * 构建渠道信息
     *
     * @param channel
     * @return
     */
    private ChannelInfoDto toChannelInfoDto(Channel channel) {
        if (channel == null) {
            return null;
        }
        ChannelInfoDto channelInfoDto = new ChannelInfoDto();
        channelInfoDto.setCustomerOrderNo(channel.getCustomerOrderNo());
        channelInfoDto.setChannelNo(channel.getChannelNo());
        channelInfoDto.setChannelOrderNo(channel.getChannelOrderNo());
        channelInfoDto.setChannelCustomerNo(channel.getChannelCustomerNo());
        channelInfoDto.setChannelOperateTime(channel.getChannelOperateTime());
        channelInfoDto.setSecondLevelChannel(channel.getSecondLevelChannel());
        channelInfoDto.setSecondLevelChannelOrderNo(channel.getSecondLevelChannelOrderNo());
        channelInfoDto.setSecondLevelChannelCustomerNo(channel.getSecondLevelChannelCustomerNo());
        channelInfoDto.setSystemCaller(channel.getSystemCaller());
        channelInfoDto.setSystemSubCaller(channel.getSystemSubCaller());
        return channelInfoDto;

    }

    /**
     * 构建货品信息
     *
     * @param cargoList
     * @return
     */
    private List<CargoInfoDto> toCargoInfoDtoList(List<Cargo> cargoList) {
        if (CollectionUtils.isEmpty(cargoList)) {
            return null;
        }
        List<CargoInfoDto> cargoInfoDtoList = new LinkedList<>();
        for (Cargo cargo : cargoList) {
            CargoInfoDto cargoInfoDto = new CargoInfoDto();
            cargoInfoDto.setCargoName(cargo.getCargoName());
            cargoInfoDto.setCargoNo(cargo.getCargoNo());
            cargoInfoDto.setCargoType(cargo.getCargoType());
            cargoInfoDto.setCargoVolume(toVolumeInfoDto(cargo.getCargoVolume()));
            cargoInfoDto.setCargoWeight(toWeightInfoDto(cargo.getCargoWeight()));
            cargoInfoDto.setCargoQuantityInfo(toCargoQuantityInfo(cargo.getCargoQuantity()));
            cargoInfoDto.setDimensionInfo(toDimensionInfoDto(cargo.getCargoDimension()));
            cargoInfoDto.setCargoRemark(cargo.getCargoRemark());
            cargoInfoDto.setPolluteSign(cargo.getPolluteSign() == null ? null : cargo.getPolluteSign().getCode());
            cargoInfoDtoList.add(cargoInfoDto);
        }
        return cargoInfoDtoList;
    }

    /**
     * 货物数量
     *
     * @param cargoQuantity
     * @return
     */
    private QuantityInfoDto toCargoQuantityInfo(Quantity cargoQuantity) {
        if (cargoQuantity == null) {
            return null;
        }
        QuantityInfoDto quantityInfoDto = new QuantityInfoDto();
        quantityInfoDto.setUnit(cargoQuantity.getUnit());
        quantityInfoDto.setValue(cargoQuantity.getValue());
        return quantityInfoDto;
    }

    /**
     * 构建长宽高
     *
     * @param dimension
     * @return
     */
    private DimensionInfoDto toDimensionInfoDto(Dimension dimension) {
        if (dimension == null) {
            return null;
        }
        DimensionInfoDto dimensionInfoDto = new DimensionInfoDto();
        dimensionInfoDto.setLength(dimension.getLength());
        dimensionInfoDto.setWidth(dimension.getWidth());
        dimensionInfoDto.setHeight(dimension.getHeight());
        dimensionInfoDto.setUnit(dimension.getUnit());
        return dimensionInfoDto;
    }

    /**
     * 构建长宽高
     * 修改API请求中，传入包裹最长边，优先用包裹最长边走产品校验，将包裹最长边传入产品校验整单维度的长、宽、高上；
     * 修改API请求中，没有传入包裹最长边，传入整单长宽高，优先用整单长宽高传入产品校验，对应传入整单长、宽、高；——现有逻辑；
     * 修改API请求中，没有传入包裹最长边、也没有传入整单长宽高，优先用订单快照的包裹最长边，其次用整单的长宽高，传入产品校验，其中：
     * 包裹最长边，映射产品产品校验整单维度的长、宽、高上；
     *
     * @param dimension
     * @return
     */
    private DimensionInfoDto toDimensionInfoDtoForPackageMaxLen(Dimension dimension, LengthInfo packageMaxLen, LengthInfo sourcePackageMaxLen, boolean isSource) {
        DimensionInfoDto dimensionInfoDto = new DimensionInfoDto();
        if (packageMaxLen != null) {
            dimensionInfoDto = getDimensionInfoDtoByDimension(
                    packageMaxLen.getValue(), packageMaxLen.getValue(), packageMaxLen.getValue(), packageMaxLen.getUnit());
            return dimensionInfoDto;
        }

        if (isSource) {
            if (sourcePackageMaxLen != null) {
                dimensionInfoDto = getDimensionInfoDtoByDimension(
                        sourcePackageMaxLen.getValue(), sourcePackageMaxLen.getValue(), sourcePackageMaxLen.getValue(), sourcePackageMaxLen.getUnit());
                return dimensionInfoDto;
            }

            if (isDimensionNotNull(dimension)) {
                dimensionInfoDto = getDimensionInfoDtoByDimension(
                        dimension.getLength(), dimension.getWidth(), dimension.getHeight(), dimension.getUnit());
                return dimensionInfoDto;
            }
        } else {
            if (isDimensionNotNull(dimension)) {
                dimensionInfoDto = getDimensionInfoDtoByDimension(
                        dimension.getLength(), dimension.getWidth(), dimension.getHeight(), dimension.getUnit());
                return dimensionInfoDto;
            }

            if (sourcePackageMaxLen != null) {
                dimensionInfoDto = getDimensionInfoDtoByDimension(
                        sourcePackageMaxLen.getValue(), sourcePackageMaxLen.getValue(), sourcePackageMaxLen.getValue(), sourcePackageMaxLen.getUnit());
                return dimensionInfoDto;
            }
        }
        return null;
    }

    private boolean isDimensionNotNull(Dimension dimension) {
        return dimension != null && (dimension.getLength() != null || dimension.getWidth() != null || dimension.getHeight() != null);
    }

    private DimensionInfoDto getDimensionInfoDtoByDimension(BigDecimal length, BigDecimal width, BigDecimal height, LengthTypeEnum unit) {
        DimensionInfoDto dimensionInfoDto = new DimensionInfoDto();
        dimensionInfoDto.setLength(length);
        dimensionInfoDto.setWidth(width);
        dimensionInfoDto.setHeight(height);
        dimensionInfoDto.setUnit(unit);
        return dimensionInfoDto;
    }


    /**
     * 构建重量信息
     *
     * @param weight
     * @return
     */
    private WeightInfoDto toWeightInfoDto(Weight weight) {
        if (weight == null) {
            return null;
        }
        WeightInfoDto weightInfoDto = new WeightInfoDto();
        weightInfoDto.setValue(weight.getValue());
        weightInfoDto.setUnit(weight.getUnit());
        return weightInfoDto;
    }

    /**
     * 构建体积信息
     *
     * @param volume
     * @return
     */
    private VolumeInfoDto toVolumeInfoDto(Volume volume) {
        if (volume == null) {
            return null;
        }
        VolumeInfoDto volumeInfoDto = new VolumeInfoDto();
        volumeInfoDto.setValue(volume.getValue());
        volumeInfoDto.setUnit(volume.getUnit());
        return volumeInfoDto;
    }

    /**
     * 构建交易客户信息
     *
     * @param customer
     * @return
     */
    private CustomerInfoDto toCustomerInfoDto(Customer customer) {
        if (customer == null) {
            return null;
        }
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        customerInfoDto.setAccountNo(customer.getAccountNo());
        customerInfoDto.setAccountName(customer.getAccountName());
        customerInfoDto.setAccount2No(customer.getAccountNo2());
        customerInfoDto.setAccount2Name(customer.getAccountName2());
        customerInfoDto.setAccount3No(customer.getAccountNo3());
        customerInfoDto.setAccount3Name(customer.getAccountName3());
        return customerInfoDto;

    }

    /**
     * 根据是否散客挂月结，判断是否需要覆盖履约账号信息
     * 快递-生鲜产品需求-校验商家配置的时效信息，根据履约账户，散客挂月结用的C散客，无此配置，需要替换为结算账户
     * @param orderModel
     * @param customer
     */
    private void updateByIndividualMsType(ExpressOrderModel orderModel, CustomerInfoDto customer) {
        if (null == customer || null == orderModel) {
            return;
        }
        // 散客挂月结单独赋值履约账号
        String individualMsType = orderModel.getAttachment(AttachmentKeyEnum.INDIVIDUAL_MS_TYPE.getKey());
        if (OrderConstants.YES_VAL.equals(individualMsType) && StringUtils.isNotBlank(orderModel.getFinance().getSettlementAccountNo())) {
            customer.setAccountNo(orderModel.getFinance().getSettlementAccountNo());
        }
    }




    /**
     * 补齐产品信息（产品名称，类型，父产品）
     *
     * @param orderModel
     * @param productFacadeResult
     */
    public void complementProduct(ExpressOrderModel orderModel, ProductFacadeResponse.ProductFacadeResult productFacadeResult) {
        if (productFacadeResult == null) {
            return;
        }
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        List<ProductInfoDto> productInfoDtoList = new LinkedList<>();
        if (productFacadeResult.getMainProductCheckResult() != null) {
            ProductInfoDto productInfoDto = this.toProductInfoDto(productFacadeResult.getMainProductCheckResult());
            productInfoDtoList.add(productInfoDto);
        }
        if (CollectionUtils.isNotEmpty(productFacadeResult.getValueAddedProductCheckResultList())) {
            for (ProductCheckResult productCheckResult : productFacadeResult.getValueAddedProductCheckResultList()) {
                productInfoDtoList.add(this.toProductInfoDto(productCheckResult));
            }
        }
        String mainProductNo = productFacadeResult.getMainProductCheckResult() != null ? productFacadeResult.getMainProductCheckResult().getProductNo() : null;
        addSkipProducts(orderModel, mainProductNo, productInfoDtoList);
        modelCreator.setProducts(productInfoDtoList);
        // 下单渠道来源是SupplyOFC 并且 派送信息deliveryPattern是仓配
        boolean isWarehouseMode = WarehouseModeUtil.flag(orderModel);
        boolean isSupplyOFC = false;
        if (orderModel.getOrderSnapshot() != null) {
            // 取下单渠道，所以优先看快照。仓配逆向单接单无产品校验、无修改流程，不考虑当前单为逆向单且快照是原单 fixme 出现前面情况需要修改
            isSupplyOFC = SystemCallerUtil.currentIsSupplyOFC(orderModel.getOrderSnapshot());
        } else {
            // 没快照，取当前单
            isSupplyOFC = SystemCallerUtil.currentIsSupplyOFC(orderModel);
        }
        boolean specialWarehouseMode = isWarehouseMode && isSupplyOFC;
        if (orderModel.getOrderBusinessIdentity().getBusinessUnit().equals(BusinessUnitEnum.CN_JDL_B2C.getCode())
                || orderModel.getOrderBusinessIdentity().getBusinessUnit().equals(BusinessUnitEnum.CN_JDL_C2B.getCode())
                || orderModel.getOrderBusinessIdentity().getBusinessUnit().equals(BusinessUnitEnum.CN_JDL_CC_B2C.getCode())) {
            // 补齐 parentNo productName productType planDeliveryTime productAttrs extendProps
            orderModel.getComplementModel().complementProductB2C_C2B(this, modelCreator, specialWarehouseMode);
            return;
        }
        // 补齐 parentNo productName productType planDeliveryTime
        orderModel.getComplementModel().complementProduct(this, modelCreator, specialWarehouseMode);
    }

    /**
     * 降级产品处理
     * @param orderModel
     * @param productFacadeResult
     */
    public void complementDegradeProduct(ExpressOrderModel orderModel, ProductFacadeResponse.ProductFacadeResult productFacadeResult) {
        if (productFacadeResult == null) {
            return;
        }
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        List<ProductInfoDto> productInfoDtoList = new LinkedList<>();
        ShipmentInfoDto shipmentInfoDto = new ShipmentInfoDto();
        // 下单渠道来源是SupplyOFC 并且 派送信息deliveryPattern是仓配
        boolean isWarehouseMode = WarehouseModeUtil.flag(orderModel);
        boolean isSupplyOFC = false;
        if (orderModel.getOrderSnapshot() != null) {
            // 取下单渠道，所以优先看快照。仓配逆向单接单无产品校验、无修改流程，不考虑当前单为逆向单且快照是原单 fixme 出现前面情况需要修改
            isSupplyOFC = SystemCallerUtil.currentIsSupplyOFC(orderModel.getOrderSnapshot());
        } else {
            // 没快照，取当前单
            isSupplyOFC = SystemCallerUtil.currentIsSupplyOFC(orderModel);
        }
        // 仓配需求，满足specialWarehouseMode=true不用产品中心返回的planDeliveryTime、transportType、operationMode覆盖已有值。
        // 实际给产品中心传businessType=pms_sc不会返回planDeliveryTime、transportType，以下存在部分无效代码【仓配订单，不补齐xxx】，为避免影响线上暂不删除
        boolean specialWarehouseMode = isWarehouseMode && isSupplyOFC;
        if (productFacadeResult.getMainProductCheckResult() != null) {
            ProductInfoDto productInfoDto = this.toProductInfoDto(productFacadeResult.getMainProductCheckResult());
            // 补齐扩展字段 && 运营模式
            // 从产品中心补齐，重新实例化信息的扩展字段对象
            Product product = null;
            if (orderModel.getProductDelegate().getMainProduct() instanceof Product) {
                product = (Product) orderModel.getProductDelegate().getMainProduct();
                Map<String, String> extendProps = (null == product || null == product.getExtendProps())
                    ? new HashMap<>()
                    : product.getExtendProps();
                productInfoDto.setExtendProps(extendProps);
            } else {
                productInfoDto.setExtendProps(new HashMap<>());
            }
            // 补齐 extendProps-operationMode extendProps-transportType extendProps-carrierPromiseDeliveredDate
            this.complementMainProductExtend(productInfoDto.getExtendProps(), shipmentInfoDto, productFacadeResult.getMainProductCheckResult(), specialWarehouseMode, orderModel);
            // 仓配，不覆盖planDeliveryTime
            if (specialWarehouseMode && product != null) {
                LOGGER.info("仓配订单，不补齐预计送达时间");
                productInfoDto.setPlanDeliveryTime(product.getPlanDeliveryTime());
            }
            productInfoDtoList.add(productInfoDto);
        }

        if (CollectionUtils.isNotEmpty(productFacadeResult.getValueAddedProductCheckResultList())) {
            for (ProductCheckResult productCheckResult : productFacadeResult.getValueAddedProductCheckResultList()) {
                productInfoDtoList.add(this.toProductInfoDto(productCheckResult));
            }
        }
        String mainProductNo = productFacadeResult.getMainProductCheckResult() != null ? productFacadeResult.getMainProductCheckResult().getProductNo() : null;
        addSkipProducts(orderModel, mainProductNo, productInfoDtoList);
        modelCreator.setProducts(productInfoDtoList);
        modelCreator.setShipmentInfo(shipmentInfoDto);

        // 覆盖所有字段，包括 planDeliveryTime productAttrs extendProps
        orderModel.getComplementModel().complementDegradeProduct(this, modelCreator);
        orderModel.getComplementModel().complementShipmentExtendProps(this, modelCreator);
    }

    /**
     * 补齐主产产品扩展信息
     * <p>
     * <b>与业务zhangyong确认，信任产品中心返回的结果，订单中心不做校验</b>
     * @param extendProps
     * @param mainProductCheckResult
     * @param isWarehouseMode
     * @param orderModel
     */
    private void complementMainProductExtend(Map<String, String> extendProps, ShipmentInfoDto shipmentInfoDto,
                                             ProductCheckResult mainProductCheckResult, boolean isWarehouseMode,
                                             ExpressOrderModel orderModel) {
        if (MapUtils.isEmpty(mainProductCheckResult.getExtendProps())) {
            // 产品中心返回主产品信息不包括扩展信息
            // B2C清空路由代码：修改场景为null，且修改前有值，落库并下发空串执行清空
            if (orderModel.isB2C()
                    && BatrixSwitch.applyByBoolean(BatrixSwitchKey.BACK_FILL_SHIPMENT_VRS_PRODUCT_CODE_SWITCH)
                    && BusinessSceneUtil.isModify(orderModel)
                    && orderModel.getOrderSnapshot() != null
                    && orderModel.getOrderSnapshot().getShipment() != null
                    && StringUtils.isNoneBlank(orderModel.getOrderSnapshot().getShipment().getExtendProps(ShipmentExtendPropsEnum.VRS_PRODUCT_CODE.getCode()))) {
                String vrsProductCode = "";
                shipmentInfoDto.complementExtendProps(ShipmentExtendPropsEnum.VRS_PRODUCT_CODE.getCode(), vrsProductCode);
            }
            return;
        }
        // 仓配订单，不补齐运营模式、运输方式
        if (isWarehouseMode) {
            LOGGER.info("仓配订单，不补齐运营模式、运输方式");
        } else {
            // 从产品中心结果补齐运营模式
            if (mainProductCheckResult.getExtendProps().containsKey(OPERATION_MODE)) {
                String operationMode = mainProductCheckResult.getExtendProps().get(OPERATION_MODE);
                extendProps.put(OPERATION_MODE, operationMode);
            }
            // 从产品中心结果补齐运输方式
            if (mainProductCheckResult.getExtendProps().containsKey(TRANSPORT_TYPE)) {
                String transportType = mainProductCheckResult.getExtendProps().get(TRANSPORT_TYPE);
                extendProps.put(TRANSPORT_TYPE, transportType);
            }
        }
        // 从产品中心结果补齐承运商预计送达时间
        if (mainProductCheckResult.getExtendProps().containsKey(CARRIER_PROMISE_DELIVERED_DATE)) {
            String deliveryTime = mainProductCheckResult.getExtendProps().get(CARRIER_PROMISE_DELIVERED_DATE);
            extendProps.put(CARRIER_PROMISE_DELIVERED_DATE, deliveryTime);
        }
        // 从产品中心结果补齐路由降级规则类型
        if (mainProductCheckResult.getExtendProps().containsKey(RULE_TYPE)) {
            String ruleType = mainProductCheckResult.getExtendProps().get(RULE_TYPE);
            shipmentInfoDto.complementExtendProps(ShipmentExtendPropsEnum.DOWN_GRADE_RULE_TYPE.getCode(), ruleType);
        }
        // 从产品中心结果补齐路由降级说明
        if (mainProductCheckResult.getExtendProps().containsKey(DOWN_GRADE_MARK)) {
            String downGradeMark = mainProductCheckResult.getExtendProps().get(DOWN_GRADE_MARK);
            shipmentInfoDto.complementExtendProps(ShipmentExtendPropsEnum.DOWN_GRADE_MARK.getCode(), downGradeMark);
        }
        // 从产品中心结果补齐路由代码（当前只有B2C需要）
        if (orderModel.isB2C() && BatrixSwitch.applyByBoolean(BatrixSwitchKey.BACK_FILL_SHIPMENT_VRS_PRODUCT_CODE_SWITCH)) {
            // 接单场景为null，不落库不下发；修改场景为null，且修改前有值，落库并下发空串执行清空
            String vrsProductCode = mainProductCheckResult.getExtendProps().get(ProductExtendPropsEnum.VRS_PRODUCT_CODE.getCode());
            if (StringUtils.isNoneBlank(vrsProductCode)) {
                shipmentInfoDto.complementExtendProps(ShipmentExtendPropsEnum.VRS_PRODUCT_CODE.getCode(), vrsProductCode);
            } else if (BusinessSceneUtil.isModify(orderModel)
                    && orderModel.getOrderSnapshot() != null
                    && orderModel.getOrderSnapshot().getShipment() != null
                    && StringUtils.isNoneBlank(orderModel.getOrderSnapshot().getShipment().getExtendProps(ShipmentExtendPropsEnum.VRS_PRODUCT_CODE.getCode()))) {
                vrsProductCode = "";
                shipmentInfoDto.complementExtendProps(ShipmentExtendPropsEnum.VRS_PRODUCT_CODE.getCode(), vrsProductCode);
            }
        }
    }

    /**
     * 功能：修改产品转换
     *
     * @param context 1
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductFacadeRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 14:51
     */
    @UnitedBusinessIdentityConverter(convertMode = ConvertMode.BEFORE_ORIGINAL_AFTER_REAL)
    public ProductFacadeRequest toProductModifyFacadeRequest(ExpressOrderContext context) {
        //源订单
        ExpressOrderModel sourceModel = context.getOrderModel().getOrderSnapshot();
        //现订单
        ExpressOrderModel model = context.getOrderModel();
        ProductFacadeRequest productFacadeRequest = new ProductFacadeRequest();
        /*业务信息*/
        productFacadeRequest.setRequestProfile(model.requestProfile());
        OrderBusinessIdentity requestIdentity = model.getOrderBusinessIdentity();
        BusinessIdentity identity = new BusinessIdentity();
        // 优先取主档扩展字段里的业务身份pmsCheckUnit，若为空默认使用订单model中的业务身份
        String pmsCheckUnit = model.getExtendProps().get(ProductConstants.PMS_CHECK_UNIT);
        if (StringUtils.isNotBlank(pmsCheckUnit)) {
            identity.setBusinessUnit(pmsCheckUnit);
        } else {
            identity.setBusinessUnit(requestIdentity.getBusinessUnit());
        }
        //优先取orderSign.pmsCheckSolution里的值，若为空使用默认值兜底
        String businessType = PmsCheckSolutionOrderSignUtil.value(model);
        if (StringUtils.isBlank(businessType)) {
            businessType = PMS_BUSINESS_TYPE;
        }
        identity.setBusinessType(businessType);

        // 终端修改的产品中心->产品使用场景
        String businessScene = model.getExtendProps().get(OrderSignEnum.PMS_CHECK_SCENE.getCode());
        if (StringUtils.isNotBlank(businessScene)) {
            identity.setBusinessScene(businessScene);
        } else {
            identity.setBusinessScene(PMS_BUSINESS_SCENE);
        }
        identity.setBusinessStrategy(StringUtils.isNotBlank(requestIdentity.getBusinessStrategy())
                ? requestIdentity.getBusinessStrategy() : model.getOrderSnapshot().getOrderBusinessIdentity().getBusinessStrategy());
        identity.setFulfillmentUnit(requestIdentity.getFulfillmentUnit());

        productFacadeRequest.setBusinessIdentity(identity);
        /*客户信息*/
        productFacadeRequest.setCustomerInfoDto(toCustomerModifyInfoDto(sourceModel.getCustomer(), model.getCustomer()));
        // 散客挂月结逻辑
        if (context.getChangedPropertyDelegate().individualMsTypeHaveChange()) {
            // 修改了散客挂月结，从当前单信息取
            this.updateByIndividualMsType(model, productFacadeRequest.getCustomerInfoDto());
        } else {
            // 未修改散客挂月结，从快照信息取
            this.updateByIndividualMsType(sourceModel, productFacadeRequest.getCustomerInfoDto());
        }
        /*货品信息*/
        List<Cargo> cargoList = (List<Cargo>) model.getCargoDelegate().getCargoList();
        List<Cargo> sourceCargoList = (List<Cargo>) sourceModel.getCargoDelegate().getCargoList();
        productFacadeRequest.setCargoInfoDtoList(toCargoInfoModifyDtoList(sourceCargoList, cargoList, sourceModel, model));
        /*商品信息*/
        productFacadeRequest.setGoodsInfoDtoList(this.toGoodsInfoModifyDtoList(sourceModel, model));
        /*渠道信息*/
        productFacadeRequest.setChannelInfoDto(toChannelModifyInfoDto(identity, sourceModel, model));
        /*配送信息*/
        productFacadeRequest.setShipmentInfoDto(toShipmentModifyInfoDto(sourceModel, model));
        /*财务信息*/
        productFacadeRequest.setFinanceInfoDto(toFinanceModifyInfoDto(sourceModel.getFinance(), model.getFinance()));
        /*产品信息*/ // TODO getProductDelegate().nonDeletedProducts()
        List<Product> sourceProducts = (List<Product>) sourceModel.getProductDelegate().getProducts();
        List<Product> products = (List<Product>) model.getProductDelegate().getProducts();
        productFacadeRequest.setProductInfoDtoList(toProductModifyInfoDtoList(
                sourceProducts.stream().filter(product -> !OperateTypeEnum.DELETE.equals(product.getOperateType())).collect(Collectors.toList()),
                products == null ? null : products.stream().filter(product -> !OperateTypeEnum.DELETE.equals(product.getOperateType())).collect(Collectors.toList()),
                model.getShipment().getServiceRequirements(), context.isNeedDeleteOperationMode()));
        productFacadeRequest.setConsigneeInfoDto(toConsigneeModifyInfoInfoDto(sourceModel.getConsignee(), model.getConsignee()));
        productFacadeRequest.setConsignorInfoDto(toConsignorModifyInfoDto(sourceModel.getConsignor(), model.getConsignor()));
        productFacadeRequest.setOperateTime(model.getOperateTime());
        // 协议信息 -- 修改场景
        productFacadeRequest.setAgreementInfoList(this.toModifyAgreementInfoDtoList(context));
        // 扩展字段
        productFacadeRequest.setExtendProps(this.toModifyExtendProps(context));
        // 跨境报关域信息
        productFacadeRequest.setCustomsInfoDto(toCustomsInfoDto(sourceModel.getCustoms()));
        return productFacadeRequest;
    }

    /**
     * 修改场景扩展信息转换
     * @param context
     * @return
     */
    private Map<String, String> toModifyExtendProps(ExpressOrderContext context) {
        Map<String, String> extendProps = new HashMap<>();

        if (null != context.getOrderModel().getConsignor() && null != context.getOrderModel().getConsignor().getAddress()) {
            Address consignorAddress = context.getOrderModel().getConsignor().getAddress();
            if (FenceTrustEnum.TRUSTED.getCode().equals(consignorAddress.getFenceTrusted())) {
                extendProps.put(START_FENCE, toProductFenceInfo(consignorAddress));
            }
        } else {
            // 修改场景取原单
            Address originConsignorAddress = context.getOrderModel().getOrderSnapshot().getConsignor().getAddress();
            if (FenceTrustEnum.TRUSTED.getCode().equals(originConsignorAddress.getFenceTrusted())) {
                extendProps.put(START_FENCE, toProductFenceInfo(originConsignorAddress));
            }
        }

        if (null != context.getOrderModel().getConsignee() && null != context.getOrderModel().getConsignee().getAddress()) {
            Address consigneeAddress = context.getOrderModel().getConsignee().getAddress();
            if (FenceTrustEnum.TRUSTED.getCode().equals(consigneeAddress.getFenceTrusted())) {
                extendProps.put(END_FENCE, toProductFenceInfo(consigneeAddress));
            }
        } else {
            // 修改场景取原单
            Address originConsigneeAddress = context.getOrderModel().getOrderSnapshot().getConsignee().getAddress();
            if (FenceTrustEnum.TRUSTED.getCode().equals(originConsigneeAddress.getFenceTrusted())) {
                extendProps.put(END_FENCE, toProductFenceInfo(originConsigneeAddress));
            }
        }

        // 【散客挂月结 结算账户】
        Optional.ofNullable(context.getOrderModel())
                .map(ExpressOrderModel::getFinance)
                .map(Finance::getSettlementAccountNo)
                .filter(StringUtils::isNotBlank)
                .ifPresent(settlementAccNo -> extendProps.put(SETTLEMENT_ACCOUNT, settlementAccNo));

        // 操作人
        extendProps.put(PIN, getPin(context));
        //托寄物编码
        ICargo firstCargo = context.getOrderModel().getCargoDelegate().firstCargo();
        if (firstCargo == null) {
            firstCargo = context.getOrderModel().getOrderSnapshot().getCargoDelegate().firstCargo();
        }
        if (firstCargo != null && StringUtils.isNotBlank(firstCargo.getCargoType())) {
            extendProps.put(CONSIGNMENT_CODE, firstCargo.getCargoType());
        }

        // 根据订单状态判断:
        // 揽收前修改modifyEnRoute=false
        // 揽收后修改modifyEnRoute=true
        if (context.getOrderModel().getOrderSnapshot().getOrderStatus().isBeforePickedUp()) {
            extendProps.put(MODIFY_EN_ROUTE, "false");
        } else {
            extendProps.put(MODIFY_EN_ROUTE, "true");
        }

        // 运单号
        extendProps.put(WAYBILL_CODE, context.getOrderModel().getOrderSnapshot().getCustomOrderNo());

        return extendProps;
    }

    /**
     * 改单功能：构建发货信息
     *
     * @param sourceConsignor 源发货信息
     * @param consignor       2 修改后发货信息
     * @return cn.jdl.oms.express.domain.dto.ConsignorInfoDto
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:45
     */
    private ConsignorInfoDto toConsignorModifyInfoDto(Consignor sourceConsignor, Consignor consignor) {
        if (consignor == null && sourceConsignor == null) {
            return null;
        }
        ConsignorInfoDto consignorInfoDto = new ConsignorInfoDto();
        //修改后的地址信息为空，则标示未修改，则取原单地址信息
        if (consignor.getAddress() != null && StringUtils.isNotBlank(consignor.getAddress().getFullAddress())) {
            consignorInfoDto.setAddressInfoDto(toAddressInfoDto(consignor.getAddress()));
        } else {
            consignorInfoDto.setAddressInfoDto(toAddressInfoDto(sourceConsignor.getAddress()));
        }
        return consignorInfoDto;
    }

    /**
     * 改单功能功能：收货信息
     *
     * @param sourceConsignee 1
     * @param consignee       2
     * @return cn.jdl.oms.express.domain.dto.ConsigneeInfoDto
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:24
     */
    private ConsigneeInfoDto toConsigneeModifyInfoInfoDto(Consignee sourceConsignee, Consignee consignee) {
        if (consignee == null && sourceConsignee == null) {
            return null;
        }
        ConsigneeInfoDto consigneeInfoDto = new ConsigneeInfoDto();
        //修改后的地址信息为空，则标示未修改，则取原单地址信息
        if (consignee.getAddress() != null && StringUtils.isNotBlank(consignee.getAddress().getFullAddress())) {
            consigneeInfoDto.setAddressInfoDto(toAddressInfoDto(consignee.getAddress()));
        } else {
            consigneeInfoDto.setAddressInfoDto(toAddressInfoDto(sourceConsignee.getAddress()));
        }
        return consigneeInfoDto;
    }

    /**
     * 功能：构建修改订单产品信息
     *
     * @param sourceAddress 源地址信息
     * @param address       修改后地址信息
     * @return cn.jdl.oms.express.domain.dto.AddressInfoDto
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:43
     */
    private AddressInfoDto toAddressModifyInfoDto(Address sourceAddress, Address address) {
        if (sourceAddress == null && address == null) {
            return null;
        }
        AddressInfoDto addressInfoDto = new AddressInfoDto();
        if (address == null) {
            address = new Address();
        }
        addressInfoDto.setProvinceNo(StringUtils.isNotBlank(address.getProvinceNo())
                ? address.getProvinceNo() : sourceAddress.getProvinceNo());
        addressInfoDto.setProvinceName(StringUtils.isNotBlank(address.getProvinceName())
                ? address.getProvinceName() : sourceAddress.getProvinceName());
        addressInfoDto.setCityNo(StringUtils.isNotBlank(address.getCityNo())
                ? address.getCityNo() : sourceAddress.getCityNo());
        addressInfoDto.setCityName(StringUtils.isNotBlank(address.getCityName())
                ? address.getCityName() : sourceAddress.getCityName());
        addressInfoDto.setCountyNo(StringUtils.isNotBlank(address.getCountyNo())
                ? address.getCountyNo() : sourceAddress.getCountyNo());
        addressInfoDto.setCountyName(StringUtils.isNotBlank(address.getCountyName())
                ? address.getCountyName() : sourceAddress.getCountyName());
        addressInfoDto.setTownNo(StringUtils.isNotBlank(address.getTownNo())
                ? address.getTownNo() : sourceAddress.getTownNo());
        addressInfoDto.setTownName(StringUtils.isNotBlank(address.getTownName())
                ? address.getTownName() : sourceAddress.getTownName());
        addressInfoDto.setAddress(StringUtils.isNotBlank(address.getAddress())
                ? address.getAddress() : sourceAddress.getAddress());
        addressInfoDto.setCoordinateType(address.getCoordinateType() == null
                ? sourceAddress.getCoordinateType() : address.getCoordinateType());
        addressInfoDto.setLongitude(StringUtils.isNotBlank(address.getLongitude())
                ? address.getLongitude() : sourceAddress.getLongitude());
        addressInfoDto.setLatitude(StringUtils.isNotBlank(address.getLatitude())
                ? address.getLatitude() : sourceAddress.getLatitude());
        addressInfoDto.setProvinceNoGis(StringUtils.isNotBlank(address.getProvinceNoGis())
                ? address.getProvinceNoGis() : sourceAddress.getProvinceNoGis());
        addressInfoDto.setProvinceNameGis(StringUtils.isNotBlank(address.getProvinceNameGis())
                ? address.getProvinceNameGis() : sourceAddress.getProvinceNameGis());
        addressInfoDto.setCityNoGis(StringUtils.isNotBlank(address.getCityNoGis())
                ? address.getCityNoGis() : sourceAddress.getCityNoGis());
        addressInfoDto.setCityNameGis(StringUtils.isNotBlank(address.getCityNameGis())
                ? address.getCityNameGis() : sourceAddress.getCityNameGis());
        addressInfoDto.setCountyNoGis(StringUtils.isNotBlank(address.getCountyNoGis())
                ? address.getCountyNoGis() : sourceAddress.getCountyNoGis());
        addressInfoDto.setCountyNameGis(StringUtils.isNotBlank(address.getCountyNameGis())
                ? address.getCountyNameGis() : sourceAddress.getCountyNameGis());
        addressInfoDto.setTownNoGis(StringUtils.isNotBlank(address.getTownNoGis())
                ? address.getTownNoGis() : sourceAddress.getTownNoGis());
        addressInfoDto.setTownNameGis(StringUtils.isNotBlank(address.getTownNameGis())
                ? address.getTownNameGis() : sourceAddress.getTownNameGis());
        addressInfoDto.setAddressGis(StringUtils.isNotBlank(address.getAddressGis())
                ? address.getAddressGis() : sourceAddress.getAddressGis());
        addressInfoDto.setPreciseGis(address.getPreciseGis() != null
                ? address.getPreciseGis() : sourceAddress.getPreciseGis());
        return addressInfoDto;
    }

    /**
     * 改单功能：
     *
     * @param sourceProducts 源产品信息
     * @param products       修改后产品信息
     * @return java.util.List<cn.jdl.oms.express.domain.dto.ProductInfoDto>
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:20
     */
    private List<ProductInfoDto> toProductModifyInfoDtoList(List<Product> sourceProducts, List<Product> products,Map<String,String> map, boolean needDeleteOperationMode) {
        if (CollectionUtils.isEmpty(products) && CollectionUtils.isEmpty(sourceProducts)) {
            return null;
        }
        List<ProductInfoDto> productInfoDtoList = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(products)) {
            // 修改后产品信息
            for (Product product : products) {
                if (isSkipProduct(product)) {
                    continue;
                }
                ProductInfoDto productInfoDto = new ProductInfoDto();
                if (product.getProductAttrs() != null
                        && StringUtils.isNotBlank(product.getProductAttrs().get(RE_RECEIVE_MODE))) {
                    Map<String ,String> attrsMap = product.getProductAttrs() != null ? product.getProductAttrs() : new HashMap<>();
                    //增值服务签单返在回传时，会回传签单返还运单号、签单照片、签单拍照时间三个非增值服务选项，因此在产品校验时需要过滤掉
                    //移除非增值服务选项---签单返还运单号
                    attrsMap.remove("returnWaybillNo");
                    //移除非增值服务选项---签单照片
                    attrsMap.remove("reReceivePhoto");
                    //移除非增值服务选项---签单拍照时间
                    attrsMap.remove("reReceivePhotoTime");
                    productInfoDto.setProductAttrs(attrsMap);
                }else {
                    productInfoDto.setProductAttrs(product.getProductAttrs());
                }
                productInfoDto.setProductNo(product.getProductNo());
                productInfoDto.setProductName(product.getProductName());
                productInfoDto.setProductType(product.getProductType());
                //修改场景不传父产品编码，可能主产品已发生修改变更
                productInfoDto.setParentNo(null);
                if (map != null) {
                    if (product.getExtendProps() == null) {
                        Map<String, String> hashMap = new HashMap<>();
                        hashMap.put(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode(), map.get(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode()));
                        product.setExtendProps(hashMap);
                    } else {
                        product.getExtendProps().put(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode(), map.get(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode()));
                    }
                }
                productInfoDto.setExtendProps(product.getExtendProps());
                productInfoDtoList.add(productInfoDto);
            }
        } else {
            // 源产品信息
            for (Product sourceProduct : sourceProducts) {
                if (isSkipProduct(sourceProduct)) {
                    continue;
                }
                ProductInfoDto productInfoDto = new ProductInfoDto();
                productInfoDto.setProductNo(sourceProduct.getProductNo());
                productInfoDto.setProductName(sourceProduct.getProductName());
                productInfoDto.setProductType(sourceProduct.getProductType());
                //修改场景不传父产品编码，可能主产品已发生修改变更
                productInfoDto.setParentNo(null);
                productInfoDto.setProductAttrs(sourceProduct.getProductAttrs());
                if (map != null) {
                    if (sourceProduct.getExtendProps() == null) {
                        Map<String, String> hashMap = new HashMap<>();
                        hashMap.put(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode(), map.get(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode()));
                        sourceProduct.setExtendProps(hashMap);
                    } else {
                        sourceProduct.getExtendProps().put(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode(), map.get(AddOnProductAttrEnum.GREENPASSAGEFLAG.getCode()));
                    }
                }
                productInfoDto.setExtendProps(sourceProduct.getExtendProps());
                productInfoDtoList.add(productInfoDto);
            }
        }
        productInfoDtoList.forEach(productInfoDto -> {
            if(1 == productInfoDto.getProductType() && needDeleteOperationMode){
                if(MapUtils.isNotEmpty(productInfoDto.getExtendProps())){
                    productInfoDto.getExtendProps().remove(ShipmentConstants.OPERATION_MODE);
                }
            }
        });
        return productInfoDtoList;
    }

    /**
     * 产品信息转换
     * @param productCheckResult
     * @return
     */
    private ProductInfoDto toProductInfoDto(ProductCheckResult productCheckResult) {
        ProductInfoDto productInfoDto = new ProductInfoDto();
        productInfoDto.setProductNo(productCheckResult.getProductNo());
        productInfoDto.setProductName(productCheckResult.getProductName());
        productInfoDto.setProductType(productCheckResult.getProductType());
        productInfoDto.setParentNo(productCheckResult.getParentNo());
        productInfoDto.setPlanDeliveryTime(productCheckResult.getPlanDeliveryTime());
        productInfoDto.setProductAttrs(productCheckResult.getProductAttrs());
        productInfoDto.setExtendProps(productCheckResult.getExtendProps());
        return productInfoDto;
    }

    /**
     * 改单功能：修改财务信息
     *
     * @param sourceFinance 原订单财务信息
     * @param finance       修改后的财务信息
     * @return cn.jdl.oms.express.domain.dto.FinanceInfoDto
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:17
     */
    private FinanceInfoDto toFinanceModifyInfoDto(Finance sourceFinance, Finance finance) {
        if (finance == null && sourceFinance == null) {
            return null;
        }
        FinanceInfoDto productFinanceInfoDto = new FinanceInfoDto();
        productFinanceInfoDto.setSettlementType(null == finance.getSettlementType() ? sourceFinance.getSettlementType() : finance.getSettlementType());
        productFinanceInfoDto.setSettlementAccountNo(null == finance.getSettlementAccountNo() ? sourceFinance.getSettlementAccountNo() : finance.getSettlementAccountNo());
        return productFinanceInfoDto;
    }

    /**
     * 改单功能：配送信息
     *
     * @param sourceModel 源订单
     * @param model       修改后的订单信息
     * @return cn.jdl.oms.express.domain.dto.ShipmentInfoDto
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:12
     */
    private ShipmentInfoDto toShipmentModifyInfoDto(ExpressOrderModel sourceModel, ExpressOrderModel model) {
        Shipment sourceShipment = sourceModel.getShipment();
        Shipment shipment = model.getShipment();
        if (shipment == null && sourceShipment == null) {
            return null;
        }
        ShipmentInfoDto shipmentInfoDto = new ShipmentInfoDto();
        shipmentInfoDto.setPickupType(null == shipment.getPickupType() ? sourceShipment.getPickupType() : shipment.getPickupType());
        shipmentInfoDto.setDeliveryType(null == shipment.getDeliveryType() ? sourceShipment.getDeliveryType() : shipment.getDeliveryType());
        shipmentInfoDto.setStartStationNo(StringUtils.isNotBlank(shipment.getStartStationNo()) ? shipment.getStartStationNo() : sourceShipment.getStartStationNo());
        shipmentInfoDto.setEndStationNo(StringUtils.isNotBlank(shipment.getEndStationNo()) ? shipment.getEndStationNo() : sourceShipment.getEndStationNo());
        // TODO 温层兼容逻辑，方案待定
        if (WarmLayerEnum.DEFAULT == shipment.getWarmLayer()) {
            shipmentInfoDto.setWarmLayer(null);
        } else {
            shipmentInfoDto.setWarmLayer(shipment.getWarmLayer() == null ? sourceShipment.getWarmLayer() : shipment.getWarmLayer());
        }
        shipmentInfoDto.setExpectPickupStartTime(shipment.getExpectPickupStartTime() == null ? sourceShipment.getExpectPickupStartTime() : shipment.getExpectPickupStartTime());
        shipmentInfoDto.setExpectPickupEndTime(shipment.getExpectPickupEndTime() == null ? sourceShipment.getExpectPickupEndTime() : shipment.getExpectPickupEndTime());

        Date pickupTime = new Date();
        if (shipment.getExpectPickupEndTime() != null) {
            pickupTime = shipment.getExpectPickupEndTime();
        } else if (sourceShipment.getExpectPickupEndTime() != null) {
            pickupTime = sourceShipment.getExpectPickupEndTime();
        }
        // 终端揽收前修改 揽收时间传入当前时间
        if (ModifySceneRuleUtil.isBeforePickUp(model)
                && null != model.getChannel()
                && SystemCallerEnum.PDA == model.getChannel().getSystemCaller()) {
            pickupTime = new Date();
        }
        shipmentInfoDto.setPickupTime(pickupTime);
        return shipmentInfoDto;
    }

    /**
     * 改单功能：构建渠道信息
     *
     * @param sourceChannel 源订单渠道信息
     * @param channel       修改后渠道信息
     * @return cn.jdl.oms.express.domain.dto.ChannelInfoDto
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:07
     */
    private ChannelInfoDto toChannelModifyInfoDto(Channel sourceChannel, Channel channel) {
        if (channel == null && sourceChannel == null) {
            return null;
        }
        ChannelInfoDto channelInfoDto = new ChannelInfoDto();
        channelInfoDto.setCustomerOrderNo(StringUtils.isNotBlank(channel.getChannelOrderNo()) ? channel.getChannelOrderNo() : sourceChannel.getChannelOrderNo());
        channelInfoDto.setChannelNo(StringUtils.isNotBlank(channel.getChannelNo()) ? channel.getChannelNo() : sourceChannel.getChannelNo());
        channelInfoDto.setChannelOrderNo(StringUtils.isNotBlank(channel.getChannelOrderNo()) ? channel.getChannelOrderNo() : sourceChannel.getChannelOrderNo());
        channelInfoDto.setChannelCustomerNo(StringUtils.isNotBlank(channel.getChannelCustomerNo()) ? channel.getChannelCustomerNo() : sourceChannel.getChannelCustomerNo());
        channelInfoDto.setChannelOperateTime(null == channel.getChannelOperateTime() ? sourceChannel.getChannelOperateTime() : channel.getChannelOperateTime());
        channelInfoDto.setSecondLevelChannel(StringUtils.isNotBlank(channel.getSecondLevelChannel()) ? channel.getSecondLevelChannel() : sourceChannel.getSecondLevelChannel());
        channelInfoDto.setSecondLevelChannelOrderNo(StringUtils.isNotBlank(channel.getSecondLevelChannelOrderNo()) ? channel.getSecondLevelChannelOrderNo() : sourceChannel.getSecondLevelChannelOrderNo());
        channelInfoDto.setSecondLevelChannelCustomerNo(StringUtils.isNotBlank(channel.getSecondLevelChannelCustomerNo()) ? channel.getSecondLevelChannelCustomerNo() : sourceChannel.getSecondLevelChannelCustomerNo());
        channelInfoDto.setSystemCaller(channel.getSystemCaller());
        channelInfoDto.setSystemSubCaller(channel.getSystemSubCaller());
        channelInfoDto.setExtendProps(channel.getExtendProps());
        return channelInfoDto;
    }

    /**
     * 改单功能：构建渠道信息
     * @param identity 业务身份
     * @param sourceModel 源订单模型
     * @param model 修改模型
     * @return
     */
    private ChannelInfoDto toChannelModifyInfoDto(BusinessIdentity identity, ExpressOrderModel sourceModel,ExpressOrderModel  model) {
        Channel channel = model.getChannel();
        Channel sourceChannel = sourceModel.getChannel();
        if (channel == null && sourceChannel == null) {
            return null;
        }
        ChannelInfoDto channelInfoDto = this.toChannelModifyInfoDto(sourceChannel, channel);
        if (channelInfoDto != null) {
            String sourceSystemSubCaller = sourceChannel.getSystemSubCaller();

            if (BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(identity.getBusinessUnit())
                    || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(identity.getBusinessUnit())) {
                // 快运修改时取源订单渠道信息
                channelInfoDto.setSystemSubCaller(sourceSystemSubCaller);
            } else if (BusinessUnitEnum.CN_JDL_B2C.getCode().equals(identity.getBusinessUnit())
                    && (SystemSubCallerEnum.CN_JDL_ECP_BYTEDANCE.getCode().equals(sourceSystemSubCaller)
                    || SystemSubCallerEnum.CN_JDL_ECP_TAOTIAN.getCode().equals(sourceSystemSubCaller))) {

                // 查询原单，若原单的 systemsubcaller = cn_jdl_ecp-taotian 或 systemsubcaller = cn_jdl_ecp-bytedance时
                if (StringUtils.isNotBlank(sourceSystemSubCaller)) {
                    // 修改场景调产品中心校验时赋值原单的systemSubCaller
                    channelInfoDto.setSystemSubCaller(sourceSystemSubCaller);
                } else {
                    // 若原单systemSubCaller为空，则按修改下发的systemSubCaller赋值产品校验
                    channelInfoDto.setSystemSubCaller(channel.getSystemSubCaller());
                }
            } else if (model.isUnitedIdentity()) {//判断是否是融合业务身份
                //判断是否是快运主产品（和上面纯快运流程的分开彼此不影响）
                if(UnitedBusinessIdentityUtil.isUnitedTransportProduct(model)){
                    // 快运单修改时取源订单渠道信息（除了有modifySystemCaller的情况以外最后传的都是原单的），modifySystemCaller仅在产品互改请求发送
                    channelInfoDto.setSystemSubCaller(sourceChannel.getSystemSubCaller());
                }
                Map<String, String> channelFacadeExt = channel.getExtendProps();
                //扩展属性
                if (MapUtils.isNotEmpty(channelFacadeExt)) {
                    String modifySystemSubCaller = channelFacadeExt.get(OrderConstants.MODIFY_SYSTEM_SUB_CALLER);
                    // modifySystemSubCaller传值（非空）时，需要将systemSubCaller赋值为modifySystemSubCaller传给产品中心
                    if (StringUtils.isNotBlank(modifySystemSubCaller)) {
                        //修改接单渠道来源
                        channelInfoDto.setSystemSubCaller(modifySystemSubCaller);
                    }
                }
            }
        }
        return channelInfoDto;
    }

    /**
     * 改单功能：构建交易客户信息
     *
     * @param sourceCustomer 源客户信息
     * @param customer       修改后客户信息
     * @return cn.jdl.oms.express.domain.dto.CustomerInfoDto
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 14:58
     */
    private CustomerInfoDto toCustomerModifyInfoDto(Customer sourceCustomer, Customer customer) {
        if (customer == null && null == sourceCustomer) {
            return null;
        }
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        customerInfoDto.setAccountNo(StringUtils.isNotBlank(customer.getAccountNo())
                ? customer.getAccountNo() : sourceCustomer.getAccountNo());
        customerInfoDto.setAccountName(StringUtils.isNotBlank(customer.getAccountName())
                ? customer.getAccountName() : sourceCustomer.getAccountName());
        customerInfoDto.setAccount2No(StringUtils.isNotBlank(customer.getAccountNo2())
                ? customer.getAccountNo2() : sourceCustomer.getAccountNo2());
        customerInfoDto.setAccount2Name(StringUtils.isNotBlank(customer.getAccountName2())
                ? customer.getAccountName2() : sourceCustomer.getAccountName2());
        customerInfoDto.setAccount3No(StringUtils.isNotBlank(customer.getAccountNo3())
                ? customer.getAccountNo3() : sourceCustomer.getAccountNo3());
        customerInfoDto.setAccount3Name(StringUtils.isNotBlank(customer.getAccountName3())
                ? customer.getAccountName3() : sourceCustomer.getAccountName3());
        return customerInfoDto;
    }

    /**
     * 改单功能：构建货品信息
     *
     * @param sourceCargoList 源货品信息
     * @param cargoList       修改后货品信息
     * @param sourceModel
     * @param model
     * @return java.util.List<cn.jdl.oms.express.domain.dto.CargoInfoDto>
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 15:03
     */
    private List<CargoInfoDto> toCargoInfoModifyDtoList(List<Cargo> sourceCargoList, List<Cargo> cargoList, ExpressOrderModel sourceModel, ExpressOrderModel model) {
        if (CollectionUtils.isEmpty(cargoList) && CollectionUtils.isEmpty(sourceCargoList)) {
            return null;
        }

        LengthInfo packageMaxLen = null;
        LengthInfo sourcePackageMaxLen = null;
        if(model.getFulfillment() != null
                && model.getFulfillment().getPackageMaxLen() != null
                && model.getFulfillment().getPackageMaxLen().getValue() != null) {
            packageMaxLen = model.getFulfillment().getPackageMaxLen();
        }
        if(sourceModel.getFulfillment() != null
                && sourceModel.getFulfillment().getPackageMaxLen() != null
                && sourceModel.getFulfillment().getPackageMaxLen().getValue() != null) {
            sourcePackageMaxLen = sourceModel.getFulfillment().getPackageMaxLen();
        }

        // 快运 && 非仓配，修改优先使用recheckVolume、recheckWeight、actualReceivedQuantity
        Volume recheckVolume = FreightGetFieldUtils.getRecheckVolume(model);
        if (recheckVolume == null || recheckVolume.getValue() == null) {
            recheckVolume = FreightGetFieldUtils.getRecheckVolume(sourceModel);
        }
        Weight recheckWeight = FreightGetFieldUtils.getRecheckWeight(model);
        if (recheckWeight == null || recheckWeight.getValue() == null) {
            recheckWeight = FreightGetFieldUtils.getRecheckWeight(sourceModel);
        }
        Quantity actualReceivedQuantity = FreightGetFieldUtils.getActualReceivedQuantity(model);
        if (actualReceivedQuantity == null || actualReceivedQuantity.getValue() == null) {
            actualReceivedQuantity = FreightGetFieldUtils.getActualReceivedQuantity(sourceModel);
        }

        List<CargoInfoDto> cargoInfoDtoList = new LinkedList<>();
        if (CollectionUtils.isNotEmpty(cargoList)) {
            for (Cargo cargo : cargoList) {
                CargoInfoDto cargoInfoDto = new CargoInfoDto();
                cargoInfoDto.setCargoName(cargo.getCargoName());
                cargoInfoDto.setCargoNo(cargo.getCargoNo());
                cargoInfoDto.setCargoType(cargo.getCargoType());
                cargoInfoDto.setCargoVolume(toVolumeInfoDto(cargo.getCargoVolume()));
                cargoInfoDto.setCargoWeight(toWeightInfoDto(cargo.getCargoWeight()));
                cargoInfoDto.setCargoQuantityInfo(toCargoQuantityInfo(cargo.getCargoQuantity()));
                cargoInfoDto.setDimensionInfo(toDimensionInfoDtoForPackageMaxLen(cargo.getCargoDimension(), packageMaxLen, sourcePackageMaxLen, false));
                cargoInfoDto.setCargoRemark(cargo.getCargoRemark());
                cargoInfoDto.setPolluteSign(cargo.getPolluteSign() == null ? null : cargo.getPolluteSign().getCode());
                // 快运 && 非仓配，修改优先使用recheckVolume、recheckWeight、actualReceivedQuantity
                if (model.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(model)
                        && !SupplyChainDeliveryOrderSignUtil.flag(model)
                        && expressUccConfigCenter.isFreightProductPresortUseRecheckInfoSwitch()) {
                    if (recheckVolume != null && recheckVolume.getValue() != null) {
                        cargoInfoDto.setCargoVolume(toVolumeInfoDto(recheckVolume));
                    }
                    if (recheckWeight != null && recheckWeight.getValue() != null) {
                        cargoInfoDto.setCargoWeight(toWeightInfoDto(recheckWeight));
                    }
                    if (actualReceivedQuantity != null && actualReceivedQuantity.getValue() != null) {
                        cargoInfoDto.setCargoQuantityInfo(toCargoQuantityInfo(actualReceivedQuantity));
                    }
                }
                cargoInfoDtoList.add(cargoInfoDto);
            }
        } else {
            for (Cargo sourceCargo : sourceCargoList) {
                CargoInfoDto cargoInfoDto = new CargoInfoDto();
                cargoInfoDto.setCargoName(sourceCargo.getCargoName());
                cargoInfoDto.setCargoNo(sourceCargo.getCargoNo());
                cargoInfoDto.setCargoType(sourceCargo.getCargoType());
                cargoInfoDto.setCargoVolume(toVolumeInfoDto(sourceCargo.getCargoVolume()));
                cargoInfoDto.setCargoWeight(toWeightInfoDto(sourceCargo.getCargoWeight()));
                cargoInfoDto.setCargoQuantityInfo(toCargoQuantityInfo(sourceCargo.getCargoQuantity()));
                cargoInfoDto.setDimensionInfo(toDimensionInfoDtoForPackageMaxLen(sourceCargo.getCargoDimension(), packageMaxLen, sourcePackageMaxLen, true));
                cargoInfoDto.setCargoRemark(sourceCargo.getCargoRemark());
                cargoInfoDto.setPolluteSign(sourceCargo.getPolluteSign() == null ? null : sourceCargo.getPolluteSign().getCode());
                // 快运 && 非仓配，修改优先使用recheckVolume、recheckWeight、actualReceivedQuantity
                if (model.isFreight() || UnitedB2CUtil.isUnitedFreightB2C(model)
                        && !SupplyChainDeliveryOrderSignUtil.flag(model)
                        && expressUccConfigCenter.isFreightProductPresortUseRecheckInfoSwitch()) {
                    if (recheckVolume != null && recheckVolume.getValue() != null) {
                        cargoInfoDto.setCargoVolume(toVolumeInfoDto(recheckVolume));
                    }
                    if (recheckWeight != null && recheckWeight.getValue() != null) {
                        cargoInfoDto.setCargoWeight(toWeightInfoDto(recheckWeight));
                    }
                    if (actualReceivedQuantity != null && actualReceivedQuantity.getValue() != null) {
                        cargoInfoDto.setCargoQuantityInfo(toCargoQuantityInfo(actualReceivedQuantity));
                    }
                }
                cargoInfoDtoList.add(cargoInfoDto);
            }
        }
        return cargoInfoDtoList;
    }

    /**
     * 修改场景产品中心校验-商品信息转换
     * @return
     */
    private List<GoodsInfoDto> toGoodsInfoModifyDtoList(ExpressOrderModel sourceModel, ExpressOrderModel model) {
        List<Goods> goodsList = (List<Goods>) model.getGoodsDelegate().getGoodsList();
        List<Goods> sourceGoodsList = (List<Goods>) sourceModel.getGoodsDelegate().getGoodsList();
        // 修改单中的商品信息
        // ## [是否港澳订单判断原始订单]
        List<GoodsInfoDto> goodsInfoDtoList = this.toGoodsInfoDtoList(goodsList, sourceModel.isHKMO());
        if (null != goodsInfoDtoList) {
            return goodsInfoDtoList;
        }

        // 修改单无商品信息，用原单的商品信息
        return this.toGoodsInfoDtoList(sourceGoodsList, sourceModel.isHKMO());
    }


    /**
     * 修改场景产品中心校验-LAS 商品信息转换，需要将货品信息的增值服务放入到商品信息这边
     *
     * @param sourceGoodsList
     * @param goodsList
     * @return
     */
    private List<GoodsInfoDto> toGoodsInfoModifyDtoListIncludeCargoInfo(List<Goods> sourceGoodsList, List<Goods> goodsList, List<Cargo> sourceCargoList, List<Cargo> cargoList) {

        // 找到待使用的cargoList
        List<Cargo> tempCargoList;
        if (CollectionUtils.isNotEmpty(cargoList)) {
            tempCargoList = cargoList;
        } else {
            tempCargoList = sourceCargoList;
        }

        // 找到待使用的goodsList
        List<Goods> tempGoodsList;
        if (CollectionUtils.isNotEmpty(goodsList)) {
            tempGoodsList = goodsList;
        } else {
            tempGoodsList = sourceGoodsList;
        }

        if (CollectionUtils.isEmpty(tempGoodsList) && CollectionUtils.isEmpty(tempCargoList)) {
            return null;
        }
        int goodsSize = CollectionUtils.isEmpty(tempGoodsList) ? 0 : tempGoodsList.size();
        int cargoSize = CollectionUtils.isEmpty(tempCargoList) ? 0 : tempCargoList.size();
        List<GoodsInfoDto> goodsInfoDtoList = new ArrayList<>(goodsSize + cargoSize);
        // 商品信息的处理
        if(goodsSize != 0) {
            tempGoodsList.forEach(good -> {
                GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
                if(StringUtils.isNotBlank(good.getGoodsUniqueCode())){
                    goodsInfoDto.setGoodsUniqueCode(good.getGoodsUniqueCode());
                }
                if (CollectionUtils.isNotEmpty(good.getGoodsProductInfos())) {
                    List<ProductInfoDto> productInfoDtos = good.getGoodsProductInfos().stream().map(goodsProduct -> {
                        ProductInfoDto productInfoDto = new ProductInfoDto();
                        productInfoDto.setProductNo(goodsProduct.getProductNo());
                        productInfoDto.setProductName(goodsProduct.getProductName());
                        productInfoDto.setParentNo(goodsProduct.getParentNo());
                        productInfoDto.setProductType(goodsProduct.getProductType());
                        productInfoDto.setExtendProps(goodsProduct.getExtendProps());
                        productInfoDto.setProductAttrs(goodsProduct.getProductAttrs());
                        return productInfoDto;
                    }).collect(Collectors.toList());
                    goodsInfoDto.setGoodsProductInfos(productInfoDtos);
                }
                goodsInfoDtoList.add(goodsInfoDto);
            });
        }


        // 货品信息的处理
        if(cargoSize != 0) {
            tempCargoList.forEach(cargo -> {
                GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
                goodsInfoDto.setGoodsUniqueCode(cargo.getCargoNo());
                if (CollectionUtils.isNotEmpty(cargo.getCargoProductInfos())) {
                    List<ProductInfoDto> productInfoDtos = cargo.getCargoProductInfos().stream().map(cargoProduct -> {
                        ProductInfoDto productInfoDto = new ProductInfoDto();
                        productInfoDto.setProductNo(cargoProduct.getProductNo());
                        productInfoDto.setProductName(cargoProduct.getProductName());
                        productInfoDto.setParentNo(cargoProduct.getParentNo());
                        productInfoDto.setProductType(cargoProduct.getProductType());
                        productInfoDto.setExtendProps(cargoProduct.getExtendProps());
                        productInfoDto.setProductAttrs(cargoProduct.getProductAttrs());
                        return productInfoDto;
                    }).collect(Collectors.toList());
                    goodsInfoDto.setGoodsProductInfos(productInfoDtos);
                }
                goodsInfoDtoList.add(goodsInfoDto);
            });
        }

        List<GoodsInfoDto> collect = goodsInfoDtoList
            .stream()
            .filter(goodsInfoDto -> CollectionUtils.isNotEmpty(goodsInfoDto.getGoodsProductInfos()))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        return goodsInfoDtoList;
    }

    /**
     * 解决方案
     *
     * @param solution
     * @return
     */
    public BusinessSolutionInfoDto toBusinessSolutionInfoDto(BusinessSolution solution) {
        if (solution == null) {
            return null;
        }
        BusinessSolutionInfoDto infoDto = new BusinessSolutionInfoDto();
        infoDto.setBusinessSolutionNo(solution.getBusinessSolutionNo());
        infoDto.setBusinessSolutionName(solution.getBusinessSolutionName());
        infoDto.setProductAttrs(solution.getProductAttrs());
        return infoDto;
    }

    /**
     * 补全解决方案信息
     *
     * @param orderModel
     * @param businessSolutionFacade
     */
    public void complementBusinessSolution(ExpressOrderModel orderModel, ProductFacadeResponse.BusinessSolutionFacade businessSolutionFacade) {
        orderModel.getComplementModel().complementBusinessSolutionName(this, businessSolutionFacade.getBusinessSolutionName());
    }

    /**
     * 补齐商品信息
     * @param orderModel
     * @param goodsResult
     */
    public void complementGoodsProduct(ExpressOrderModel orderModel, GoodsResult goodsResult) {
        if(null == goodsResult){
            return;
        }
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        List<GoodsInfoDto> goodsInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsResult.getProducts())) {
            GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
            goodsInfoDto.setGoodsUniqueCode(goodsResult.getGoodsUniqueCode());
            if(CollectionUtils.isNotEmpty(goodsResult.getProducts())){
                List<ProductInfoDto> productInfoDtoList = new ArrayList<>();
                goodsResult.getProducts().forEach(product -> {
                    ProductInfoDto productInfoDto = new ProductInfoDto();
                    productInfoDto.setProductNo(product.getProductNo());
                    productInfoDto.setProductName(product.getProductName());
                    productInfoDto.setProductType(product.getProductType());
                    productInfoDto.setProductAttrs(product.getProductAttrs());
                    productInfoDto.setExtendProps(product.getExtendProps());
                    productInfoDto.setParentNo(product.getParentNo());
                    productInfoDtoList.add(productInfoDto);
                });
                goodsInfoDto.setGoodsProductInfos(productInfoDtoList);
            }
            goodsInfos.add(goodsInfoDto);
        }
        modelCreator.setGoodsInfos(goodsInfos);
        orderModel.getComplementModel().complementGoodsProduct(this, modelCreator);
    }

    /**
     * 围栏信息
     * @copyright    &copy;2023 JDL.CN All Right Reserved
     * <AUTHOR>
     * @date         2023/5/16
     * @version      1.0
     * @since        1.8
     */
    @Data
    @AllArgsConstructor
    static class Fence implements Serializable {
        /**
         * 围栏ID
         */
        private String fenceId;
        /**
         * 围栏类型
         */
        private String businessType;
    }


    /**
     * las 产品校验
     * 货品纬度的产品信息校验--将货品信息和商品信息一起放到goodsList里面进行校验
     * @param context
     * @return
     */
    public ProductFacadeRequest toProductFacadeRequestPutCargoIntoGoods(ExpressOrderContext context) {
        ExpressOrderModel expressOrderModel = context.getOrderModel();
        ProductFacadeRequest productFacadeRequest = new ProductFacadeRequest();
        productFacadeRequest.setRequestProfile(expressOrderModel.requestProfile());
        OrderBusinessIdentity requestIdentity = expressOrderModel.getOrderBusinessIdentity();
        BusinessIdentity identity = new BusinessIdentity(requestIdentity.getBusinessUnit(), requestIdentity.getBusinessType(), requestIdentity.getBusinessScene(), requestIdentity.getBusinessStrategy());
        identity.setFulfillmentUnit(requestIdentity.getFulfillmentUnit());
        productFacadeRequest.setBusinessIdentity(identity);
        productFacadeRequest.setCustomerInfoDto(toCustomerInfoDto(expressOrderModel.getCustomer()));
        List<Cargo> cargoList = (List<Cargo>) expressOrderModel.getCargoDelegate().getCargoList();
        productFacadeRequest.setCargoInfoDtoList(toCargoInfoDtoList(cargoList));
        productFacadeRequest.setChannelInfoDto(toChannelInfoDto(expressOrderModel.getChannel()));
        productFacadeRequest.setShipmentInfoDto(toShipmentInfoDto(expressOrderModel.getShipment()));
        productFacadeRequest.setFinanceInfoDto(toFinanceInfoDto(expressOrderModel.getFinance()));

        // 货品信息的增值服务和商品信息的都放在商品信息列表里
        productFacadeRequest.setGoodsInfoDtoList(toGoodsInfoDtoListIncludeCargoInfo((List<Goods>)expressOrderModel.getGoodsDelegate().getGoodsList(), cargoList));
        productFacadeRequest.setProductInfoDtoList(toProductInfoDtoList((List<Product>) expressOrderModel.getProductDelegate().getProducts(), expressOrderModel.getShipment().getServiceRequirements(), context.isNeedDeleteOperationMode()));
        productFacadeRequest.setConsigneeInfoDto(toConsigneeInfoDto(expressOrderModel.getConsignee()));
        productFacadeRequest.setConsignorInfoDto(toConsignorInfoDto(expressOrderModel.getConsignor()));
        productFacadeRequest.setOperateTime(expressOrderModel.getOperateTime());
        // 解决方案
        productFacadeRequest.setBusinessSolutionInfoDto(toBusinessSolutionInfoDto(expressOrderModel.getBusinessSolution()));
        // 扩展字段
        productFacadeRequest.setExtendProps(this.toExtendProps(context));
        return productFacadeRequest;
    }

    /**
     * 功能：LAS构建商品信息，需要将货品信息的增值服务放入到商品信息中
     * @param goodsList
     * @return
     */
    private List<GoodsInfoDto> toGoodsInfoDtoListIncludeCargoInfo(List<Goods> goodsList, List<Cargo> cargoList) {
        if (CollectionUtils.isEmpty(goodsList) && CollectionUtils.isEmpty(cargoList)) {
            return null;
        }
        int goodsSize = CollectionUtils.isEmpty(goodsList) ? 0 : goodsList.size();
        int cargoSize = CollectionUtils.isEmpty(cargoList) ? 0 : cargoList.size();
        List<GoodsInfoDto> goodsInfoDtoList = new ArrayList<>(goodsSize + cargoSize);
        // 商品信息的处理
        if(goodsSize != 0) {
            goodsList.forEach(good -> {
                GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
                if(StringUtils.isNotBlank(good.getGoodsUniqueCode())){
                    goodsInfoDto.setGoodsUniqueCode(good.getGoodsUniqueCode());
                }
                if (CollectionUtils.isNotEmpty(good.getGoodsProductInfos())) {
                    List<ProductInfoDto> productInfoDtos = good.getGoodsProductInfos().stream().map(goodsProduct -> {
                        ProductInfoDto productInfoDto = new ProductInfoDto();
                        productInfoDto.setProductNo(goodsProduct.getProductNo());
                        productInfoDto.setProductName(goodsProduct.getProductName());
                        productInfoDto.setParentNo(goodsProduct.getParentNo());
                        productInfoDto.setProductType(goodsProduct.getProductType());
                        productInfoDto.setExtendProps(goodsProduct.getExtendProps());
                        productInfoDto.setProductAttrs(goodsProduct.getProductAttrs());
                        return productInfoDto;
                    }).collect(Collectors.toList());
                    goodsInfoDto.setGoodsProductInfos(productInfoDtos);
                }
                goodsInfoDtoList.add(goodsInfoDto);
            });
        }


        // 货品信息的处理
        if(cargoSize != 0) {
            cargoList.forEach(cargo -> {
                GoodsInfoDto goodsInfoDto = new GoodsInfoDto();
                goodsInfoDto.setGoodsUniqueCode(cargo.getCargoNo());
                if (CollectionUtils.isNotEmpty(cargo.getCargoProductInfos())) {
                    List<ProductInfoDto> productInfoDtos = cargo.getCargoProductInfos().stream().map(cargoProduct -> {
                        ProductInfoDto productInfoDto = new ProductInfoDto();
                        productInfoDto.setProductNo(cargoProduct.getProductNo());
                        productInfoDto.setProductName(cargoProduct.getProductName());
                        productInfoDto.setParentNo(cargoProduct.getParentNo());
                        productInfoDto.setProductType(cargoProduct.getProductType());
                        productInfoDto.setExtendProps(cargoProduct.getExtendProps());
                        productInfoDto.setProductAttrs(cargoProduct.getProductAttrs());
                        return productInfoDto;
                    }).collect(Collectors.toList());
                    goodsInfoDto.setGoodsProductInfos(productInfoDtos);
                }
                goodsInfoDtoList.add(goodsInfoDto);
            });
        }

        List<GoodsInfoDto> collect = goodsInfoDtoList
            .stream()
            .filter(goodsInfoDto -> CollectionUtils.isNotEmpty(goodsInfoDto.getGoodsProductInfos()))
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        return goodsInfoDtoList;
    }


    /**
     * 功能：大件修改产品转换，需要将货品信息里的增值服务放到商品信息的增值服务里面
     *
     * @param context 1
     * @return cn.jdl.oms.express.domain.infrs.acl.pl.product.ProductFacadeRequest
     * @version 0.0.1
     * <AUTHOR>
     * @date 2021/3/31 14:51
     */
    public ProductFacadeRequest toProductModifyFacadeRequestPutCargoIntoGoods(ExpressOrderContext context) {
        //源订单
        ExpressOrderModel sourceModel = context.getOrderModel().getOrderSnapshot();
        //现订单
        ExpressOrderModel model = context.getOrderModel();
        ProductFacadeRequest productFacadeRequest = new ProductFacadeRequest();
        /*业务信息*/
        productFacadeRequest.setRequestProfile(model.requestProfile());
        OrderBusinessIdentity requestIdentity = model.getOrderBusinessIdentity();
        BusinessIdentity identity = new BusinessIdentity();
        identity.setBusinessUnit(requestIdentity.getBusinessUnit());
        identity.setBusinessType(requestIdentity.getBusinessType());
        identity.setBusinessScene(requestIdentity.getBusinessScene());
        identity.setBusinessStrategy(StringUtils.isNotBlank(requestIdentity.getBusinessStrategy())
            ? requestIdentity.getBusinessStrategy() : model.getOrderSnapshot().getOrderBusinessIdentity().getBusinessStrategy());
        identity.setFulfillmentUnit(requestIdentity.getFulfillmentUnit());

        productFacadeRequest.setBusinessIdentity(identity);
        /*客户信息*/
        productFacadeRequest.setCustomerInfoDto(toCustomerModifyInfoDto(sourceModel.getCustomer(), model.getCustomer()));
        /*货品信息*/
        List<Cargo> cargoList = (List<Cargo>) model.getCargoDelegate().getCargoList();
        List<Cargo> sourceCargoList = (List<Cargo>) sourceModel.getCargoDelegate().getCargoList();
        productFacadeRequest.setCargoInfoDtoList(toCargoInfoModifyDtoList(sourceCargoList, cargoList, sourceModel, model));
        /*商品信息*/
        List<Goods> goodsList = (List<Goods>) model.getGoodsDelegate().getGoodsList();
        List<Goods> sourceGoodsList = (List<Goods>) sourceModel.getGoodsDelegate().getGoodsList();
        productFacadeRequest.setGoodsInfoDtoList(this.toGoodsInfoModifyDtoListIncludeCargoInfo(sourceGoodsList, goodsList, sourceCargoList, cargoList));
        /*渠道信息*/
        productFacadeRequest.setChannelInfoDto(toChannelModifyInfoDto(sourceModel.getChannel(), model.getChannel()));
        /*配送信息*/
        productFacadeRequest.setShipmentInfoDto(toShipmentModifyInfoDto(sourceModel, model));
        /*财务信息*/
        productFacadeRequest.setFinanceInfoDto(toFinanceModifyInfoDto(sourceModel.getFinance(), model.getFinance()));
        /*产品信息*/
        List<Product> sourceProducts = (List<Product>) sourceModel.getProductDelegate().getProducts();
        List<Product> products = (List<Product>) model.getProductDelegate().getProducts();
        productFacadeRequest.setProductInfoDtoList(toProductModifyInfoDtoList(
            sourceProducts.stream().filter(product -> !OperateTypeEnum.DELETE.equals(product.getOperateType())).collect(Collectors.toList()),
            products == null ? null : products.stream().filter(product -> !OperateTypeEnum.DELETE.equals(product.getOperateType())).collect(Collectors.toList()),
            model.getShipment().getServiceRequirements(), context.isNeedDeleteOperationMode()));
        productFacadeRequest.setConsigneeInfoDto(toConsigneeModifyInfoInfoDto(sourceModel.getConsignee(), model.getConsignee()));
        productFacadeRequest.setConsignorInfoDto(toConsignorModifyInfoDto(sourceModel.getConsignor(), model.getConsignor()));
        productFacadeRequest.setOperateTime(model.getOperateTime());
        // 扩展字段
        productFacadeRequest.setExtendProps(this.toModifyExtendProps(context));
        return productFacadeRequest;
    }

    /**
     * 是否需要过滤产品信息
     */
    private boolean isSkipProduct(Product product) {

        // 快运需要剔除高峰期附加费、燃油附加费。原因：产品中心校验不通过但是计费需要
        if (AddOnProductEnum.FUEL_SURCHARGE.getCode().equals(product.getProductNo()) ||
                AddOnProductEnum.PEAK_SEASON_SURCHARGE.getCode().equals(product.getProductNo())) {
            return true;
        }

        return false;
    }

    /**
     * 产品中心校验通过之后，补全跳过校验的产品信息
     */
    private void addSkipProducts(ExpressOrderModel orderModel, String mainProductNo, List<ProductInfoDto> productInfoDtoList) {
        if (BusinessUnitEnum.CN_JDL_FREIGHT_SERVICE.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessUnit())
                || BusinessUnitEnum.CN_JDL_FREIGHT_CONSUMER.getCode().equals(orderModel.getOrderBusinessIdentity().getBusinessUnit()) || UnitedB2CUtil.isUnitedFreightB2C(orderModel)) {
            // 燃油附加费
            if (orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.FUEL_SURCHARGE.getCode()) != null) {
                Product product = orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.FUEL_SURCHARGE.getCode());
                ProductInfoDto productInfoDto = new ProductInfoDto();
                productInfoDto.setProductNo(product.getProductNo());
                productInfoDto.setProductName(AddOnProductEnum.FUEL_SURCHARGE.getDesc());
                productInfoDto.setProductType(product.getProductType());
                productInfoDto.setParentNo(mainProductNo);
                productInfoDto.setProductAttrs(product.getProductAttrs());
                productInfoDto.setExtendProps(product.getExtendProps());
                productInfoDtoList.add(productInfoDto);
            }
            // 高峰期附加费
            if (orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.PEAK_SEASON_SURCHARGE.getCode()) != null) {
                Product product = orderModel.getProductDelegate().ofProductNo(AddOnProductEnum.PEAK_SEASON_SURCHARGE.getCode());
                ProductInfoDto productInfoDto = new ProductInfoDto();
                productInfoDto.setProductNo(product.getProductNo());
                productInfoDto.setProductName(AddOnProductEnum.PEAK_SEASON_SURCHARGE.getDesc());
                productInfoDto.setProductType(product.getProductType());
                productInfoDto.setParentNo(mainProductNo);
                productInfoDto.setProductAttrs(product.getProductAttrs());
                productInfoDto.setExtendProps(product.getExtendProps());
                productInfoDtoList.add(productInfoDto);
            }
        }
    }

    /**
     * 产品中心校验通过之后的后置处理
     */
    public void postProcess(ExpressOrderContext context, ProductFacadeResponse productFacadeResponse) {
        if (context == null || context.getOrderModel() == null || productFacadeResponse == null) {
            return;
        }

        // 按业务身份处理
        if (context.getOrderModel().isFreight()
                || UnitedB2CUtil.isUnitedFreightB2C(context.getOrderModel())) {
            // 快运
            postProcessFreight(context);
        }
    }

    /**
     * 产品中心校验通过之后的后置处理：快运
     */
    private void postProcessFreight(ExpressOrderContext context) {
        // fr-a-0044补全actualReceiptDate：取主产品的预计送达时间
        ExpressOrderModel orderModel = context.getOrderModel();
        if ((orderModel.getOrderStatus() !=null && orderModel.getOrderStatus().isAfterPickedUp())
                || (orderModel.getOrderSnapshot() != null && orderModel.getOrderSnapshot().getOrderStatus() != null && orderModel.getOrderSnapshot().getOrderStatus().isAfterPickedUp())) {
            // 揽收后修改，不补全
            return;
        }

        ProductDelegate productDelegate = orderModel.getProductDelegate();
        if (productDelegate == null || productDelegate.isEmpty()) {
            return;
        }
        Product mainProduct = (Product) productDelegate.getMainProduct();
        if (mainProduct == null || mainProduct.getPlanDeliveryTime() == null) {
            return;
        }

        Product product = productDelegate.ofProductNo(AddOnProductEnum.WAIT_NOTICE_DELIVERY.getCode());
        if (product == null || OperateTypeEnum.DELETE == product.getOperateType()) {
            return;
        }
        Map<String, String> productAttrs = product.getProductAttrs();
        if (productAttrs == null) {
            productAttrs = new HashMap<>();
            product.setProductAttrs(productAttrs);
        }
        productAttrs.put(ACTUAL_RECEIPT_DATE, DateUtils.formatDate(mainProduct.getPlanDeliveryTime()));
    }


    /**
     * 获取操作人或者订单所属人
     */
    private String getPin(ExpressOrderContext context) {
        if (context.getOrderModel().isFreight() || UnitedB2CUtil.isUnitedFreightB2C(context.getOrderModel())) {
            // 快运存在小哥代下单，优先取订单所属人
            return getOperatorOrOrderOwner(context);
        }
        return context.getOrderModel().getOperator();
    }

    /**
     * 获取操作人或者订单所属人
     */
    private String getOperatorOrOrderOwner(ExpressOrderContext context) {
        // 快运存在小哥代下单，需要优先取订单所属人
        String orderOwner = getOrderOwner(context);
        if (StringUtils.isNotBlank(orderOwner)) {
            return orderOwner;
        }
        // 修改
        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot != null && StringUtils.isNotBlank(orderSnapshot.getOperator())) {
            return orderSnapshot.getOperator();
        }
        // 接单
        return orderModel.getOperator();
    }

    /**
     * 获取订单所属人
     */
    private String getOrderOwner(ExpressOrderContext orderContext) {
        if (orderContext == null) {
            return null;
        }
        ExpressOrderModel orderModel = orderContext.getOrderModel();
        if (orderModel == null) {
            return null;
        }

        // 快照不为空则先从快照取（修改等场景）
        ExpressOrderModel orderSnapshot = orderModel.getOrderSnapshot();
        if (orderSnapshot != null) {
            String orderOwner = getOrderOwner(orderSnapshot);
            if (StringUtils.isNotBlank(orderOwner)) {
                return orderOwner;
            }
        }

        // 再从当前单取
        return getOrderOwner(orderModel);
    }

    /**
     * 获取订单所属人
     */
    private String getOrderOwner(ExpressOrderModel orderModel) {
        if (orderModel == null) {
            return null;
        }
        Map<String, String> extendProps = orderModel.getExtendProps();
        if (MapUtils.isEmpty(extendProps)) {
            return null;
        }
        return extendProps.get(ORDER_OWNER);
    }

    /**
     * 构建跨境报关信息
     * @param customs
     * @return
     */
    private CustomsInfoDto toCustomsInfoDto(Customs customs) {
        if (customs == null) {
            return null;
        }
        CustomsInfoDto customsInfoDto = new CustomsInfoDto();
        customsInfoDto.setStartFlowDirection(customs.getStartFlowDirection());
        customsInfoDto.setEndFlowDirection(customs.getEndFlowDirection());
        return customsInfoDto;
    }

    /**
     * 构建产品映射入参
     * @param orderModel
     * @return
     */
    public ProductMappingFacadeRequest toProductMappingFacadeRequest(ExpressOrderModel orderModel, List<String> productNos, String productLine) {
        ProductMappingFacadeRequest mappingFacadeRequest = new ProductMappingFacadeRequest();
        mappingFacadeRequest.setRequestProfile(orderModel.requestProfile());
        mappingFacadeRequest.setProductNos(productNos);
        mappingFacadeRequest.setProductLine(productLine);
        return mappingFacadeRequest;
    }

    /**
     * 补全新产品信息
     *
     * @param orderModel
     * @param mappingFacadeResponse
     */
    public void complementNewProduct(ExpressOrderModel orderModel, ProductMappingFacadeResponse mappingFacadeResponse) {
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        modelCreator.setProducts(mappingFacadeResponse.getProductInfoDtoList());
        orderModel.complement().complementNewProduct(this, modelCreator, orderModel);
    }

    /**
     * 补全新产品信息-使用原单
     *
     * @param orderModel
     */
    public void complementNewProductWithSnapshot(ExpressOrderModel orderModel) {
        if (orderModel.getOrderSnapshot() == null
                || orderModel.getOrderSnapshot().getProductDelegate() == null
                || CollectionUtils.isEmpty(orderModel.getOrderSnapshot().getProductDelegate().getNewProducts())) {
            LOGGER.error("快照新产品为空，无法补全新产品信息");
            return;
        }
        ExpressOrderModelCreator modelCreator = new ExpressOrderModelCreator();
        // 使用快照的新产品
        modelCreator.setProducts(toNewProductInfoDtos(orderModel.getOrderSnapshot().getProductDelegate().getNewProducts()));
        orderModel.complement().complementNewProduct(this, modelCreator, orderModel);
    }

    /**
     * 转换新产品信息
     *
     * @param newProducts
     * @return
     */
    private List<ProductInfoDto> toNewProductInfoDtos(List<Product> newProducts) {
        List<ProductInfoDto> productInfoDtoList = new ArrayList<>(newProducts.size());
        for (Product product : newProducts) {
            ProductInfoDto infoDto = new ProductInfoDto();
            infoDto.setProductNo(product.getProductNo());
            infoDto.setProductName(product.getProductName());
            infoDto.setProductType(product.getProductType());
            productInfoDtoList.add(infoDto);
        }
        return productInfoDtoList;
    }

    /**
     * 防腐层请求转换：查询产品的销售时间信息
     */
    public ProductTimeConfigFacadeRequest toProductTimeConfigFacadeRequest(Date date, RequestProfile requestProfile, List<String> productNos) {
        ProductTimeConfigFacadeRequest facadeRequest = new ProductTimeConfigFacadeRequest();
        facadeRequest.setQueryTime(date);
        facadeRequest.setRequestProfile(requestProfile);
        facadeRequest.setProductNos(productNos);
        return facadeRequest;
    }
}
