package cn.jdl.oms.express.domain.infrs.acl.pl.product;

import cn.jdl.batrix.sdk.base.BusinessIdentity;
import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.dto.AgreementInfoDto;
import cn.jdl.oms.express.domain.dto.BusinessSolutionInfoDto;
import cn.jdl.oms.express.domain.dto.CargoInfoDto;
import cn.jdl.oms.express.domain.dto.ChannelInfoDto;
import cn.jdl.oms.express.domain.dto.ConsigneeInfoDto;
import cn.jdl.oms.express.domain.dto.ConsignorInfoDto;
import cn.jdl.oms.express.domain.dto.CustomerInfoDto;
import cn.jdl.oms.express.domain.dto.CustomsInfoDto;
import cn.jdl.oms.express.domain.dto.DimensionInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.dto.GoodsInfoDto;
import cn.jdl.oms.express.domain.dto.ProductInfoDto;
import cn.jdl.oms.express.domain.dto.QuantityInfoDto;
import cn.jdl.oms.express.domain.dto.ShipmentInfoDto;
import cn.jdl.oms.express.domain.dto.VolumeInfoDto;
import cn.jdl.oms.express.domain.dto.WeightInfoDto;
import cn.jdl.oms.express.domain.spec.dict.ServiceProductTypeEnum;
import cn.jdl.oms.express.domain.spec.dict.SystemCallerEnum;
import cn.jdl.oms.express.shared.common.dict.BusinessUnitEnum;
import cn.jdl.pms.api.dto.AddressInfo;
import cn.jdl.pms.api.dto.AgreementInfo;
import cn.jdl.pms.api.dto.AreaInfo;
import cn.jdl.pms.api.dto.BusinessSolution;
import cn.jdl.pms.api.dto.BusinessSolutionInfo;
import cn.jdl.pms.api.dto.CargoInfo;
import cn.jdl.pms.api.dto.ChannelInfo;
import cn.jdl.pms.api.dto.ConsigneeInfo;
import cn.jdl.pms.api.dto.ConsignorInfo;
import cn.jdl.pms.api.dto.CustomerInfo;
import cn.jdl.pms.api.dto.CustomsInfo;
import cn.jdl.pms.api.dto.DimensionInfo;
import cn.jdl.pms.api.dto.FinanceInfo;
import cn.jdl.pms.api.dto.Goods;
import cn.jdl.pms.api.dto.GoodsInfo;
import cn.jdl.pms.api.dto.Product;
import cn.jdl.pms.api.dto.ProductInfo;
import cn.jdl.pms.api.dto.QuantityInfo;
import cn.jdl.pms.api.dto.ShipmentInfo;
import cn.jdl.pms.api.dto.VolumeInfo;
import cn.jdl.pms.api.dto.WeightInfo;
import cn.jdl.pms.api.request.CheckProductRequest;
import cn.jdl.pms.api.response.CheckProductResponse;
import cn.jdl.pms.api.response.ProductResult;
import cn.jdl.pms.basic.api.request.producttimeconfig.ProductTimeConfigRequest;
import cn.jdl.pms.basic.api.request.producttimeconfig.TimeInfo;
import cn.jdl.pms.basic.api.response.producttimeconfig.ProductTimeConfigResponse;
import cn.jdl.pms.basic.api.response.producttimeconfig.ServiceTimeConfig;
import com.jdl.product.api.request.productmapping.ProductLineInfo;
import com.jdl.product.api.request.productmapping.ProductMappingQueryRequest;
import com.jdl.product.api.response.productmapping.ProductMappingResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ProjectName：cn.jdl.oms.express.domain.infrs.acl.pl.product
 * @Package： cn.jdl.oms.express.domain.infrs.acl.pl.product
 * @ClassName: ProductRpcTranslator
 * @Description: 产品Rpc参数转换器
 * @Author： zhangqi1026
 * @CreateDate 2020/3/18
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
@Translator
public class ProductRpcTranslator {

    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(ProductRpcTranslator.class);

    /**
     * 产品中心校验需要传入的业务身份
     */
    @Value("${pms.business.type:pms_service}")
    private String PMS_BUSINESS_TYPE;

    /**
     * 产品中心校验需要传入的业务类型
     */
    @Value("${pms.business.scene:pmscheck}")
    private String PMS_BUSINESS_SCENE;

    /**
     * 业务查询场景：1-查询产品高峰期时间段
     */
    private static final Integer PRODUCT_TIME_CONFIG_PEAK = 1;

    /**
     * 构造器
     *
     * @param productFacadeRequest
     * @return
     */
    public RequestProfile toRequestProfile(ProductFacadeRequest productFacadeRequest) {
        RequestProfile requestProfile = new RequestProfile();
        requestProfile.setLocale(productFacadeRequest.getRequestProfile().getLocale());
        requestProfile.setTenantId(productFacadeRequest.getRequestProfile().getTenantId());
        requestProfile.setTimeZone(productFacadeRequest.getRequestProfile().getTimeZone());
        requestProfile.setTraceId(productFacadeRequest.getRequestProfile().getTraceId());
        return requestProfile;
    }

    /**
     * 构造器
     *
     * @param productFacadeRequest
     * @return
     */
    public CheckProductRequest toCheckProductRequest(ProductFacadeRequest productFacadeRequest) {
        CheckProductRequest checkProductRequest = new CheckProductRequest();

        checkProductRequest.setBusinessIdentity(toBusinessIdentity(productFacadeRequest));
        checkProductRequest.setChannelInfo(toChannelInfo(productFacadeRequest));
        checkProductRequest.setCustomerInfo(toCustomerInfo(productFacadeRequest));
        checkProductRequest.setConsignorInfo(toConsignorInfo(productFacadeRequest));
        checkProductRequest.setConsigneeInfo(toConsigneeInfo(productFacadeRequest));
        checkProductRequest.setCargoInfos(toCargoInfos(productFacadeRequest));
        checkProductRequest.setProductInfos(toProductInfos(productFacadeRequest));
        checkProductRequest.setFinanceInfo(toFinanceInfo(productFacadeRequest));
        checkProductRequest.setShipmentInfo(toShipmentInfo(productFacadeRequest));
        //商品
        checkProductRequest.setGoodsInfos(toGoodsInfo(productFacadeRequest));
        //解决方案
        checkProductRequest.setBusinessSolutionInfo(toBusinessSolutionInfo(productFacadeRequest.getBusinessSolutionInfoDto()));
        // 扩展字段
        checkProductRequest.setExtendProps(productFacadeRequest.getExtendProps());
        // 跨境报关流向
        checkProductRequest.setCustomsInfo(toCustomsInfo(productFacadeRequest));
        return checkProductRequest;
    }

    /**
     * LAS大件构造器
     *
     * @param productFacadeRequest
     * @return
     */
    public CheckProductRequest toCheckLASProductRequest(ProductFacadeRequest productFacadeRequest) {
        CheckProductRequest checkProductRequest = new CheckProductRequest();

        checkProductRequest.setBusinessIdentity(toBusinessIdentity(productFacadeRequest));
        checkProductRequest.setChannelInfo(toChannelInfo(productFacadeRequest));
        checkProductRequest.setCustomerInfo(toCustomerInfo(productFacadeRequest));
        checkProductRequest.setConsignorInfo(toConsignorInfo(productFacadeRequest));
        checkProductRequest.setConsigneeInfo(toConsigneeInfo(productFacadeRequest));
        checkProductRequest.setCargoInfos(toLASCargoInfos(productFacadeRequest));
        checkProductRequest.setProductInfos(toProductInfos(productFacadeRequest));
        checkProductRequest.setFinanceInfo(toFinanceInfo(productFacadeRequest));
        checkProductRequest.setShipmentInfo(toShipmentInfo(productFacadeRequest));
        //商品
        checkProductRequest.setGoodsInfos(toGoodsInfo(productFacadeRequest));
        //解决方案
        checkProductRequest.setBusinessSolutionInfo(toBusinessSolutionInfo(productFacadeRequest.getBusinessSolutionInfoDto()));
        // 扩展字段
        checkProductRequest.setExtendProps(productFacadeRequest.getExtendProps());
        return checkProductRequest;
    }

    /**
     * 商品信息转换
     * @param productFacadeRequest
     * @return
     */
    private List<GoodsInfo> toGoodsInfo(ProductFacadeRequest productFacadeRequest) {
        List<GoodsInfo> goodsInfos = new ArrayList<>();
        List<GoodsInfoDto> goodsInfoDtoList = productFacadeRequest.getGoodsInfoDtoList();
        if (CollectionUtils.isNotEmpty(goodsInfoDtoList)) {
            for (GoodsInfoDto goodsInfoDto : goodsInfoDtoList) {
                GoodsInfo goodsInfo = new GoodsInfo();
                // 扩展字段
                goodsInfo.setExtendProps(goodsInfoDto.getExtendProps());
                //商品唯一编码
                goodsInfo.setGoodsUniqueCode(goodsInfoDto.getGoodsUniqueCode());
                //商品增值服务
                if(CollectionUtils.isNotEmpty(goodsInfoDto.getGoodsProductInfos())){
                    List<ProductInfo> productInfos = goodsInfoDto.getGoodsProductInfos().stream().map(goodInfoDto ->{
                        ProductInfo productInfo = new ProductInfo();
                        productInfo.setProductNo(goodInfoDto.getProductNo());
                        productInfo.setParentNo(goodInfoDto.getParentNo());
                        productInfo.setProductType(goodInfoDto.getProductType());
                        productInfo.setExtendProps(goodInfoDto.getExtendProps());
                        productInfo.setProductAttrs(goodInfoDto.getProductAttrs());
                        return productInfo;
                    }).collect(Collectors.toList());
                    goodsInfo.setProductInfos(productInfos);
                }
                //托寄物品类编码
                if (null != goodsInfoDto.getGoodsCategoryCode()) {
                    goodsInfo.setGoodsCategoryCode(goodsInfoDto.getGoodsCategoryCode());
                }
                goodsInfos.add(goodsInfo);
            }
        }
        return goodsInfos;
    }


    /**
     * 货品信息
     *
     * @param productFacadeRequest
     * @return
     */
    private List<CargoInfo> toCargoInfos(ProductFacadeRequest productFacadeRequest) {
        List<CargoInfo> cargoInfos = new ArrayList<>();
        List<CargoInfoDto> cargoInfoDtos = productFacadeRequest.getCargoInfoDtoList();
        if (CollectionUtils.isNotEmpty(cargoInfoDtos)) {
            for (CargoInfoDto cargoInfoDto : cargoInfoDtos) {
                CargoInfo cargoInfo = new CargoInfo();
                cargoInfo.setDimensionInfo(toDimensionInfo(cargoInfoDto.getDimensionInfo()));
                cargoInfo.setWeight(toWeightInfo(cargoInfoDto.getCargoWeight()));
                cargoInfo.setVolume(toVolumeInfo(cargoInfoDto.getCargoVolume()));
                cargoInfo.setQuantityInfo(toQuantityInfo(cargoInfoDto.getCargoQuantityInfo()));
                cargoInfo.setCargoName(cargoInfoDto.getCargoName());
                cargoInfos.add(cargoInfo);
            }
        }
        return cargoInfos;
    }

    /**
     * LAS大件货品信息不传体积
     *
     * @param productFacadeRequest
     * @return
     */
    private List<CargoInfo> toLASCargoInfos(ProductFacadeRequest productFacadeRequest) {
        List<CargoInfo> cargoInfos = new ArrayList<>();
        List<CargoInfoDto> cargoInfoDtos = productFacadeRequest.getCargoInfoDtoList();
        if (CollectionUtils.isNotEmpty(cargoInfoDtos)) {
            for (CargoInfoDto cargoInfoDto : cargoInfoDtos) {
                CargoInfo cargoInfo = new CargoInfo();
                cargoInfo.setDimensionInfo(toDimensionInfo(cargoInfoDto.getDimensionInfo()));
                cargoInfo.setWeight(toWeightInfo(cargoInfoDto.getCargoWeight()));
                // 大件业务线不校验体积
//                cargoInfo.setVolume(toVolumeInfo(cargoInfoDto.getCargoVolume()));
                cargoInfo.setQuantityInfo(toQuantityInfo(cargoInfoDto.getCargoQuantityInfo()));
                cargoInfos.add(cargoInfo);
            }
        }
        return cargoInfos;
    }

    /**
     * 数量
     *
     * @param cargoQuantityInfo
     * @return
     */
    private QuantityInfo toQuantityInfo(QuantityInfoDto cargoQuantityInfo) {
        QuantityInfo quantityInfo = null;
        if (cargoQuantityInfo != null) {
            quantityInfo = new QuantityInfo();
            quantityInfo.setValue(cargoQuantityInfo.getValue());
            quantityInfo.setUnit(cargoQuantityInfo.getUnit());
        }
        return quantityInfo;
    }

    /**
     * 体积
     *
     * @param volumeInfoDto
     * @return
     */
    private VolumeInfo toVolumeInfo(VolumeInfoDto volumeInfoDto) {
        VolumeInfo volumeInfo = null;
        if (volumeInfoDto != null) {
            volumeInfo = new VolumeInfo();
            volumeInfo.setVolume(volumeInfoDto.getValue());
            if (volumeInfoDto.getUnit() != null) {
                volumeInfo.setUnit(volumeInfoDto.getUnit().getCode());
            }
        }
        return volumeInfo;
    }


    /**
     * 重量
     *
     * @param weightInfoDto
     * @return
     */
    private WeightInfo toWeightInfo(WeightInfoDto weightInfoDto) {
        WeightInfo weightInfo = null;
        if (weightInfoDto != null) {
            weightInfo = new WeightInfo();
            weightInfo.setWeight(weightInfoDto.getValue());
            if (weightInfoDto.getUnit() != null) {
                weightInfo.setUnit(weightInfoDto.getUnit().getCode());
            }
        }
        return weightInfo;
    }

    /**
     * 体积
     *
     * @param dimensionInfoDto
     * @return
     */
    private DimensionInfo toDimensionInfo(DimensionInfoDto dimensionInfoDto) {
        DimensionInfo dimensionInfo = null;
        if (dimensionInfoDto != null) {
            dimensionInfo = new DimensionInfo();
            dimensionInfo.setLength(dimensionInfoDto.getLength());
            dimensionInfo.setWidth(dimensionInfoDto.getWidth());
            dimensionInfo.setHeight(dimensionInfoDto.getHeight());
            if (dimensionInfoDto.getUnit() != null) {
                dimensionInfo.setUnit(dimensionInfoDto.getUnit().getCode());
            }
        }
        return dimensionInfo;
    }

    /**
     * 配送诉求信息
     *
     * @param productFacadeRequest
     * @return
     */
    private ShipmentInfo toShipmentInfo(ProductFacadeRequest productFacadeRequest) {
        ShipmentInfo shipmentInfo = null;
        ShipmentInfoDto shipmentInfoDto = productFacadeRequest.getShipmentInfoDto();
        if (shipmentInfoDto != null) {
            shipmentInfo = new ShipmentInfo();

            //上门揽收时间
            // todo 终端揽收前修改，这个时间应该为当前时间【港澳引入】
            // fixme 1、路由静态妥投时间：是在揽收完成之后，路由监听运单妥投的消息更新，用于对内监管；（在路由系统展示）
            //  2、承诺妥投时间：是在揽收前修改流程里进行的修改，落到运单，路由监听运单消息，更新承诺妥投时间；揽收完成之后不会再变更；（在路由系统、运单详情中展示,客户是看不到的）
            //  a)港澳的系统链路：终端-接单层-订单中心-产品中心-路由，订单中心将路由返回的agingDateTime透传下去；路由监听运单的消息，更新对外承诺时间；
            //  b)大网流程里:是终端->产品中心->路由，路由返回的agingDateTime，终端->外单揽收前修改，落到运单
            //  3、预计送达时间：是路由监听全程跟踪的节点（封车、解封车），实时动态更新预计送达时间，小程序对外展示的是预计送达时间；
            if(null != shipmentInfoDto.getPickupTime()){
                shipmentInfo.setPickupTime(shipmentInfoDto.getPickupTime());
            } else if (shipmentInfoDto.getExpectPickupEndTime() != null) {
                shipmentInfo.setPickupTime(shipmentInfoDto.getExpectPickupEndTime());
            }

            shipmentInfo.setPickupStartTime(shipmentInfoDto.getExpectPickupStartTime());

            //下单时间
            if (productFacadeRequest.getOperateTime() != null) {
                shipmentInfo.setOrderTime(productFacadeRequest.getOperateTime());
            }

            //揽收方式
            if (shipmentInfoDto.getPickupType() != null) {
                shipmentInfo.setPickupType(shipmentInfoDto.getPickupType().getCode());
            }

            //派送方式
            if (shipmentInfoDto.getDeliveryType() != null) {
                shipmentInfo.setDeliveryType(shipmentInfoDto.getDeliveryType().getCode());
            }

            //始发站编码，名称，目的站编码，名称
            shipmentInfo.setStartStationNo(shipmentInfoDto.getStartStationNo());
            shipmentInfo.setStartStationName(shipmentInfoDto.getStartStationName());
            shipmentInfo.setEndStationNo(shipmentInfoDto.getEndStationNo());
            shipmentInfo.setEndStationName(shipmentInfoDto.getEndStationName());
        }
        return shipmentInfo;
    }

    /**
     * 财务信息
     *
     * @param productFacadeRequest
     * @return
     */
    private FinanceInfo toFinanceInfo(ProductFacadeRequest productFacadeRequest) {
        FinanceInfo financeInfo = null;
        FinanceInfoDto financeInfoDto = productFacadeRequest.getFinanceInfoDto();
        if (financeInfoDto != null) {
            financeInfo = new FinanceInfo();
            financeInfo.setSettlementType(String.valueOf(financeInfoDto.getSettlementType().getCode()));
        }
        return financeInfo;
    }

    /**
     * 产品列表
     *
     * @param productFacadeRequest
     * @return
     */
    private List<ProductInfo> toProductInfos(ProductFacadeRequest productFacadeRequest) {
        List<ProductInfo> productInfoList = null;
        List<ProductInfoDto> productInfoDtoList = productFacadeRequest.getProductInfoDtoList();
        if (CollectionUtils.isNotEmpty(productInfoDtoList)) {
            productInfoList = new ArrayList<>(productInfoDtoList.size());
            for (ProductInfoDto productInfoDto : productInfoDtoList) {
                ProductInfo productInfo = new ProductInfo();
                productInfo.setProductNo(productInfoDto.getProductNo());
                //将温层赋值给主产品
                if (ServiceProductTypeEnum.MAIN_PRODUCT.getCode().equals(productInfoDto.getProductType())) {
                    ShipmentInfoDto shipment = productFacadeRequest.getShipmentInfoDto();
                    if (shipment != null && shipment.getWarmLayer() != null) {
                        productInfo.setWarmLayer(shipment.getWarmLayer().getCode());
                    }
                }
                productInfo.setProductAttrs(productInfoDto.getProductAttrs());
                productInfo.setExtendProps(productInfoDto.getExtendProps());
                productInfo.setProductType(productInfoDto.getProductType());
                productInfo.setParentNo(productInfoDto.getParentNo());
                productInfoList.add(productInfo);
            }
        }
        return productInfoList;
    }


    /**
     * 收货信息
     *
     * @param productFacadeRequest
     * @return
     */
    private ConsigneeInfo toConsigneeInfo(ProductFacadeRequest productFacadeRequest) {
        ConsigneeInfo consigneeInfo = null;
        ConsigneeInfoDto consigneeInfoDto = productFacadeRequest.getConsigneeInfoDto();
        if (consigneeInfoDto == null) {
            return null;
        }
        consigneeInfo = new ConsigneeInfo();
        consigneeInfo.setConsigneeName(consigneeInfoDto.getConsigneeName());
        consigneeInfo.setConsigneePhone(consigneeInfoDto.getConsigneePhone());
        consigneeInfo.setConsigneeMobile(consigneeInfoDto.getConsigneeMobile());

        if (consigneeInfoDto != null && consigneeInfoDto.getAddressInfoDto() != null) {
            //B2C和C2C处理逻辑不一样
            if (BusinessUnitEnum.CN_JDL_C2C.getCode().equals(productFacadeRequest.getBusinessIdentity().getBusinessUnit())
                    || BusinessUnitEnum.CN_JDL_O2O.getCode().equals(productFacadeRequest.getBusinessIdentity().getBusinessUnit())) {
                //如果解析前地址无1、2、3、4级则传解析后地址
                //港澳引入 终端揽收前修改切订单中心 终端修改使用gis解析后的
                if (StringUtils.isAllBlank(consigneeInfoDto.getAddressInfoDto().getProvinceNo(), consigneeInfoDto.getAddressInfoDto().getCityNo(),
                        consigneeInfoDto.getAddressInfoDto().getCountyNo(), consigneeInfoDto.getAddressInfoDto().getTownNo())
                        ||(null != productFacadeRequest.getChannelInfoDto() && SystemCallerEnum.PDA == productFacadeRequest.getChannelInfoDto().getSystemCaller())) {
                    consigneeInfo.setAddressInfo(toAddressInfoGis(consigneeInfoDto.getAddressInfoDto()));
                } else {
                    consigneeInfo.setAddressInfo(toAddressInfoOrigin(consigneeInfoDto.getAddressInfoDto()));
                }
            } else {
                consigneeInfo.setAddressInfo(toAddressInfoGis(consigneeInfoDto.getAddressInfoDto()));
            }
        }
        return consigneeInfo;
    }

    /**
     * 地址转换
     *
     * @param addressInfoDto
     * @return
     */
    private AddressInfo toAddressInfoGis(AddressInfoDto addressInfoDto) {
        if (addressInfoDto == null) {
            return null;
        }
        AddressInfo addressInfo = new AddressInfo();
        if (addressInfoDto.getCoordinateType() != null) {
            addressInfo.setCoordinateType(addressInfoDto.getCoordinateType().getCode());
        }
        if (StringUtils.isNotBlank(addressInfoDto.getLongitude())) {
            addressInfo.setLongitude(new BigDecimal(addressInfoDto.getLongitude()));
        }
        if (StringUtils.isNotBlank(addressInfoDto.getLatitude())) {
            addressInfo.setLatitude(new BigDecimal(addressInfoDto.getLatitude()));
        }
        if(StringUtils.isNotBlank(addressInfoDto.getRegionNo())) {
            addressInfo.setRegionNo(addressInfoDto.getRegionNo());
        }
        if(StringUtils.isNotBlank(addressInfoDto.getRegionName())) {
            addressInfo.setRegionName(addressInfoDto.getRegionName());
        }
        //Gis解析后地址
        if (StringUtils.isNotBlank(addressInfoDto.getProvinceNoGis())
                && StringUtils.isNotBlank(addressInfoDto.getCityNoGis())
                && StringUtils.isNotBlank(addressInfoDto.getCountyNoGis())) {
            addressInfo.setProvinceNo(StringUtils.isNotBlank(addressInfoDto.getProvinceNoGis()) ? addressInfoDto.getProvinceNoGis() : "0");
            addressInfo.setProvinceName(addressInfoDto.getProvinceNameGis());
            addressInfo.setCityNo(StringUtils.isNotBlank(addressInfoDto.getCityNoGis()) ? addressInfoDto.getCityNoGis() : "0");
            addressInfo.setCityName(addressInfoDto.getCityNameGis());
            addressInfo.setCountyNo(StringUtils.isNotBlank(addressInfoDto.getCountyNoGis()) ? addressInfoDto.getCountyNoGis() : "0");
            addressInfo.setCountyName(addressInfoDto.getCountyNameGis());
            addressInfo.setTownNo(StringUtils.isNotBlank(addressInfoDto.getTownNoGis()) ? addressInfoDto.getTownNoGis() : "0");
            addressInfo.setTownName(addressInfoDto.getTownNameGis());
            //原用户下单详细地址
            addressInfo.setDetailAddress(addressInfoDto.getAddress());
            //addressInfo.setIsParsed(true);
            //地址状态码：
            //1：未通过GIS解析
            //2：GIS解析成功
            //3：GIS解析失败
            addressInfo.setStatusCode(2);
            addressInfo.setPrecise(addressInfoDto.getPreciseGis());
        } else {
            //原地址
            addressInfo.setProvinceNo(StringUtils.isNotBlank(addressInfoDto.getProvinceNo())
                    ? addressInfoDto.getProvinceNo() : "0");
            addressInfo.setProvinceName(addressInfoDto.getProvinceName());
            addressInfo.setCityNo(StringUtils.isNotBlank(addressInfoDto.getCityNo()) ? addressInfoDto.getCityNo() : "0");
            addressInfo.setCityName(addressInfoDto.getCityName());
            addressInfo.setCountyNo(StringUtils.isNotBlank(addressInfoDto.getCountyNo()) ? addressInfoDto.getCountyNo() : "0");
            addressInfo.setCountyName(addressInfoDto.getCountyName());
            addressInfo.setTownNo(StringUtils.isNotBlank(addressInfoDto.getTownNo()) ? addressInfoDto.getTownNo() : "0");
            addressInfo.setTownName(addressInfoDto.getTownName());
            addressInfo.setDetailAddress(addressInfoDto.getAddress());
            //addressInfo.setIsParsed(false);
            //地址状态码：
            //1：未通过GIS解析
            //2：GIS解析成功
            //3：GIS解析失败
            addressInfo.setStatusCode(3);
        }
        return addressInfo;
    }

    /**
     * 赋客户原地址
     *
     * @param addressInfoDto
     * @return
     */
    private AddressInfo toAddressInfoOrigin(AddressInfoDto addressInfoDto) {
        if (addressInfoDto == null) {
            return null;
        }
        AddressInfo addressInfo = new AddressInfo();
        if (addressInfoDto.getCoordinateType() != null) {
            addressInfo.setCoordinateType(addressInfoDto.getCoordinateType().getCode());
        }
        if (StringUtils.isNotBlank(addressInfoDto.getLongitude())) {
            addressInfo.setLongitude(new BigDecimal(addressInfoDto.getLongitude()));
        }
        if (StringUtils.isNotBlank(addressInfoDto.getLatitude())) {
            addressInfo.setLatitude(new BigDecimal(addressInfoDto.getLatitude()));
        }

        if(StringUtils.isNotBlank(addressInfoDto.getRegionNo())) {
            addressInfo.setRegionNo(addressInfoDto.getRegionNo());
        }
        if(StringUtils.isNotBlank(addressInfoDto.getRegionName())) {
            addressInfo.setRegionName(addressInfoDto.getRegionName());
        }

        //原地址
        addressInfo.setProvinceNo(addressInfoDto.getProvinceNo());
        addressInfo.setProvinceName(addressInfoDto.getProvinceName());
        addressInfo.setCityNo(addressInfoDto.getCityNo());
        addressInfo.setCityName(addressInfoDto.getCityName());
        addressInfo.setCountyNo(addressInfoDto.getCountyNo());
        addressInfo.setCountyName(addressInfoDto.getCountyName());
        addressInfo.setTownNo(StringUtils.isNotBlank(addressInfoDto.getTownNo()) ? addressInfoDto.getTownNo() : "0");
        addressInfo.setTownName(addressInfoDto.getTownName());
        addressInfo.setDetailAddress(addressInfoDto.getAddress());

        addressInfo.setStatusCode(2);

        return addressInfo;
    }


    /**
     * 发货信息
     *
     * @param productFacadeRequest
     * @return
     */
    private ConsignorInfo toConsignorInfo(ProductFacadeRequest productFacadeRequest) {
        ConsignorInfo consignorInfo = null;
        ConsignorInfoDto consignor = productFacadeRequest.getConsignorInfoDto();
        if (consignor == null) {
            return null;
        }
        consignorInfo = new ConsignorInfo();
        consignorInfo.setConsignorName(consignor.getConsignorName());
        consignorInfo.setConsignorPhone(consignor.getConsignorPhone());
        consignorInfo.setConsignorMobile(consignor.getConsignorMobile());

        if (consignor != null && consignor.getAddressInfoDto() != null) {
            //B2C和C2C处理逻辑不一样
            if (BusinessUnitEnum.CN_JDL_C2C.getCode().equals(productFacadeRequest.getBusinessIdentity().getBusinessUnit())
                    || BusinessUnitEnum.CN_JDL_O2O.getCode().equals(productFacadeRequest.getBusinessIdentity().getBusinessUnit())) {
                //如果解析前地址无1、2、3、4级则传解析后地址
                //港澳引入 终端揽收前修改切订单中心 终端修改使用gis解析后的
                if (StringUtils.isAllBlank(consignor.getAddressInfoDto().getProvinceNo(), consignor.getAddressInfoDto().getCityNo(),
                        consignor.getAddressInfoDto().getCountyNo(), consignor.getAddressInfoDto().getTownNo())
                        ||(null != productFacadeRequest.getChannelInfoDto() && SystemCallerEnum.PDA == productFacadeRequest.getChannelInfoDto().getSystemCaller())) {
                    consignorInfo.setAddressInfo(toAddressInfoGis(consignor.getAddressInfoDto()));
                } else {
                    consignorInfo.setAddressInfo(toAddressInfoOrigin(consignor.getAddressInfoDto()));
                }
            } else {
                consignorInfo.setAddressInfo(toAddressInfoGis(consignor.getAddressInfoDto()));
            }
        }
        return consignorInfo;
    }

    /**
     * 客户信息
     *
     * @param productFacadeRequest
     * @return
     */
    private CustomerInfo toCustomerInfo(ProductFacadeRequest productFacadeRequest) {
        CustomerInfo customerInfo = new CustomerInfo();
        CustomerInfoDto customerInfoDto = productFacadeRequest.getCustomerInfoDto();
        if (customerInfoDto != null) {
            customerInfo.setAccountNo(customerInfoDto.getAccountNo());
            customerInfo.setAccountNo2(customerInfoDto.getAccount2No());
            customerInfo.setAccountNo3(customerInfoDto.getAccount3No());
        }
        customerInfo.setAgreementInfos(this.toAgreementInfoList(productFacadeRequest.getAgreementInfoList()));

        return customerInfo;
    }

    /**
     * 协议信息列表转换
     * @param agreementInfoDtos
     * @return
     */
    private List<AgreementInfo> toAgreementInfoList(List<AgreementInfoDto> agreementInfoDtos) {
        if (CollectionUtils.isEmpty(agreementInfoDtos)) {
            return null;
        }

        return agreementInfoDtos.stream()
                .map(this::toAgreementInfo)
                .collect(Collectors.toList());
    }

    /**
     * 协议信息转换
     * @param agreementInfoDto
     * @return
     */
    private AgreementInfo toAgreementInfo(AgreementInfoDto agreementInfoDto) {
        AgreementInfo agreementInfo = new AgreementInfo();
        agreementInfo.setAgreementType(agreementInfoDto.getAgreementType());
        return agreementInfo;
    }

    /**
     * 渠道信息
     *
     * @param productFacadeRequest
     * @return
     */
    private ChannelInfo toChannelInfo(ProductFacadeRequest productFacadeRequest) {
        ChannelInfo channelInfo = null;
        ChannelInfoDto channel = productFacadeRequest.getChannelInfoDto();
        if (channel != null) {
            channelInfo = new ChannelInfo();
            channelInfo.setCustomerOrderNo(channel.getCustomerOrderNo());
            channelInfo.setChannelNo(channel.getChannelNo());
            channelInfo.setChannelOrderNo(channel.getChannelOrderNo());
            channelInfo.setChannelCustomerNo(channel.getChannelCustomerNo());
            channelInfo.setChannelOperateTime(channel.getChannelOperateTime());
            channelInfo.setTwoLevelChannel(channel.getSecondLevelChannel());
            channelInfo.setTwoChannelOrderNo(channel.getSecondLevelChannelOrderNo());
            channelInfo.setTwoChannelCustomerNo(channel.getSecondLevelChannelCustomerNo());
            channelInfo.setSystemCaller(channel.getSystemCaller().getCode());
            channelInfo.setSystemSubCaller(channel.getSystemSubCaller());
        }
        return channelInfo;
    }


    /**
     * 交易业务身份
     *
     * @param productFacadeRequest
     * @return
     */
    private BusinessIdentity toBusinessIdentity(ProductFacadeRequest productFacadeRequest) {
        BusinessIdentity businessIdentity = null;
        BusinessIdentity expressBusinessIdentity = productFacadeRequest.getBusinessIdentity();
        if (expressBusinessIdentity != null) {
            businessIdentity = new BusinessIdentity();
            businessIdentity.setBusinessUnit(expressBusinessIdentity.getBusinessUnit());
            if (StringUtils.isNotBlank(expressBusinessIdentity.getBusinessType())) {
                businessIdentity.setBusinessType(expressBusinessIdentity.getBusinessType());
            } else {
                //默认兜底
                businessIdentity.setBusinessType(PMS_BUSINESS_TYPE);
            }
            businessIdentity.setBusinessStrategy(expressBusinessIdentity.getBusinessStrategy());
            businessIdentity.setFulfillmentUnit(expressBusinessIdentity.getFulfillmentUnit());

            if (StringUtils.isNotBlank(expressBusinessIdentity.getBusinessScene())) {
                businessIdentity.setBusinessScene(expressBusinessIdentity.getBusinessScene());
            } else {
                //默认兜底
                businessIdentity.setBusinessScene(PMS_BUSINESS_SCENE);
            }
        }
        return businessIdentity;
    }

    /**
     * 构建
     *
     * @param checkProductResponse
     * @return
     */
    public ProductFacadeResponse toProductFacadeResponse(CheckProductResponse checkProductResponse) {
        ProductFacadeResponse productFacadeResponse = new ProductFacadeResponse();
        productFacadeResponse.setProductResults(toProductFacadeResultList(checkProductResponse.getProductResults()));
        productFacadeResponse.setDegradeProductResults(toProductFacadeResultList(checkProductResponse.getDegradeProductResults()));
        productFacadeResponse.setBusinessSolutionFacade(toBusinessSolutionFacade(checkProductResponse.getBusinessSolution()));
        productFacadeResponse.setGoodsResults(toGoodsResultFacade(checkProductResponse.getGoods()));
        return productFacadeResponse;
    }

    /**
     * 商品校验结果转换
     * @param goods
     * @return
     */
    private List<GoodsResult> toGoodsResultFacade(List<Goods> goods) {
        List<GoodsResult> goodsResultList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(goods)){
            goods.forEach(good ->{
                GoodsResult goodsResult = new GoodsResult();
                goodsResult.setGoodsUniqueCode(good.getGoodsUniqueCode());
                if(CollectionUtils.isNotEmpty(good.getProducts())){
                    List<ProductCheckResult> productCheckResultList = new ArrayList<>(good.getProducts().size());
                    good.getProducts().forEach(product -> {
                        ProductCheckResult productCheckResult = new ProductCheckResult();
                        productCheckResult.setProductNo(product.getProductNo());
                        productCheckResult.setProductName(product.getProductName());
                        productCheckResult.setProductType(product.getProductType());
                        productCheckResult.setProductAttrs(product.getProductAttrs());
                        productCheckResult.setExtendProps(product.getExtendProps());
                        productCheckResult.setResultCode(product.getResultCode());
                        productCheckResult.setResultMessage(product.getResultMessage());
                        productCheckResult.setRefDegradedNo(product.getRefDegradedNo());
                        //产品类型
                        productCheckResult.setProductType(ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode());
                        productCheckResultList.add(productCheckResult);
                    });
                    goodsResult.setProducts(productCheckResultList);
                }
                goodsResultList.add(goodsResult);
            });
        }
        return goodsResultList;
    }

    /**
     * 构建产品校验结果
     *
     * @param productResultList
     * @return
     */
    private List<ProductFacadeResponse.ProductFacadeResult> toProductFacadeResultList(List<ProductResult> productResultList) {
        List<ProductFacadeResponse.ProductFacadeResult> productResults = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productResultList)) {
            for (ProductResult productResult : productResultList) {
                //主产品
                Product mainProduct = productResult.getProduct();
                ProductFacadeResponse.ProductFacadeResult productFacadeResult = new ProductFacadeResponse.ProductFacadeResult();
                productFacadeResult.setMainProductCheckResult(toProductCheckResult(mainProduct, ServiceProductTypeEnum.MAIN_PRODUCT.getCode(), null));
                //增值产品
                List<Product> valueAddedProducts = productResult.getValueAddedProducts();
                if (CollectionUtils.isNotEmpty(valueAddedProducts)) {
                    List<ProductCheckResult> valueAddedProductCheckResultList = new LinkedList<>();
                    for (Product valueAddedProduct : valueAddedProducts) {
                        valueAddedProductCheckResultList.add(toProductCheckResult(valueAddedProduct,
                                ServiceProductTypeEnum.VALUE_ADDED_PRODUCT.getCode(), mainProduct.getProductNo()));
                    }
                    //增值产品
                    productFacadeResult.setValueAddedProductCheckResultList(valueAddedProductCheckResultList);
                }
                productResults.add(productFacadeResult);
            }
        }
        return productResults;
    }


    /**
     * 构建产品校验结果
     *
     * @param product     产品信息
     * @param productType 产品类型
     * @param parentNo    父产品编码
     * @return
     */
    private ProductCheckResult toProductCheckResult(Product product, Integer productType, String parentNo) {
        ProductCheckResult productCheckResult = null;
        if (product != null) {
            productCheckResult = new ProductCheckResult();
            productCheckResult.setProductNo(product.getProductNo());
            productCheckResult.setProductName(product.getProductName());
            productCheckResult.setProductType(product.getProductType());
            productCheckResult.setProductAttrs(product.getProductAttrs());
            productCheckResult.setExtendProps(product.getExtendProps());
            productCheckResult.setResultCode(product.getResultCode());
            productCheckResult.setResultMessage(product.getResultMessage());
            productCheckResult.setRefDegradedNo(product.getRefDegradedNo());
            //产品类型
            productCheckResult.setProductType(productType);
            //所属产品
            productCheckResult.setParentNo(parentNo);
            productCheckResult.setPlanDeliveryTime(product.getPlanDeliveryTime());
        }
        return productCheckResult;
    }

    /**
     * 解决方案
     *
     * @param infoDto
     * @return
     */
    public BusinessSolutionInfo toBusinessSolutionInfo(BusinessSolutionInfoDto infoDto) {
        if (infoDto == null || StringUtils.isBlank(infoDto.getBusinessSolutionNo())) {
            return null;
        }
        BusinessSolutionInfo solutionInfo = new BusinessSolutionInfo();
        solutionInfo.setBusinessSolutionNo(infoDto.getBusinessSolutionNo());
        solutionInfo.setBusinessSolutionAttrs(infoDto.getProductAttrs());
        return solutionInfo;
    }

    /**
     * 产品中心返回解决方案信息转换
     * @param businessSolution
     * @return
     */
    public ProductFacadeResponse.BusinessSolutionFacade toBusinessSolutionFacade(BusinessSolution businessSolution) {
        if (businessSolution == null) {
            return null;
        }
        ProductFacadeResponse.BusinessSolutionFacade facade = new ProductFacadeResponse.BusinessSolutionFacade();
        facade.setResultCode(businessSolution.getResultCode());
        facade.setResultMessage(businessSolution.getResultMessage());
        facade.setBusinessSolutionNo(businessSolution.getBusinessSolutionNo());
        facade.setBusinessSolutionName(businessSolution.getBusinessSolutionName());
        facade.setProductAttrs(businessSolution.getBusinessSolutionAttrs());
        return facade;
    }

    /**
     * 跨境报关信息
     * @param productFacadeRequest
     * @return
     */
    private CustomsInfo toCustomsInfo(ProductFacadeRequest productFacadeRequest) {
        CustomsInfo customsInfo = new CustomsInfo();
        CustomsInfoDto customsInfoDto = productFacadeRequest.getCustomsInfoDto();
        if (null != customsInfoDto) {
            if (null != customsInfoDto.getStartFlowDirection()){
                //始发流向
                AreaInfo startAreaInfo = new AreaInfo();
                startAreaInfo.setArea(customsInfoDto.getStartFlowDirection().name());
                customsInfo.setStartAreaInfo(startAreaInfo);
            }
            if (null != customsInfoDto.getEndFlowDirection()){
                //目的流向
                AreaInfo endAreaInfo = new AreaInfo();
                endAreaInfo.setArea(customsInfoDto.getEndFlowDirection().name());
                customsInfo.setEndAreaInfo(endAreaInfo);
            }
        }
        return customsInfo;
    }

    /**
     * 产品映射
     *
     * @param mappingFacadeRequest
     * @return
     */
    public ProductMappingQueryRequest toProductMappingQueryRequest(ProductMappingFacadeRequest mappingFacadeRequest) {
        ProductMappingQueryRequest mappingQueryRequest = new ProductMappingQueryRequest();
        List<com.jdl.product.api.request.productmapping.ProductInfo> productInfos = new ArrayList<>(mappingFacadeRequest.getProductNos().size());
        for (String productNo : mappingFacadeRequest.getProductNos()) {
            com.jdl.product.api.request.productmapping.ProductInfo productInfo = new com.jdl.product.api.request.productmapping.ProductInfo();
            productInfo.setProductNo(productNo);
            productInfos.add(productInfo);
        }
        if (StringUtils.isNotBlank(mappingFacadeRequest.getProductLine())) {
            ProductLineInfo productLineInfo = new ProductLineInfo();
            productLineInfo.setProductLineNo(mappingFacadeRequest.getProductLine());
            mappingQueryRequest.setProductLineInfo(productLineInfo);
        }
        mappingQueryRequest.setProductInfoList(productInfos);
        return mappingQueryRequest;
    }

    /**
     * 产品中心Profile构造器
     *
     * @param mappingFacadeRequest
     * @return
     */
    public com.jdl.product.api.core.bean.RequestProfile toProductRequestProfile(ProductMappingFacadeRequest mappingFacadeRequest) {
        com.jdl.product.api.core.bean.RequestProfile requestProfile = new com.jdl.product.api.core.bean.RequestProfile();
        requestProfile.setLocale(mappingFacadeRequest.getRequestProfile().getLocale());
        requestProfile.setTenantId(mappingFacadeRequest.getRequestProfile().getTenantId());
        requestProfile.setTimeZone(mappingFacadeRequest.getRequestProfile().getTimeZone());
        requestProfile.setTraceId(mappingFacadeRequest.getRequestProfile().getTraceId());
        return requestProfile;
    }

    /**
     * 产品映射出参
     *
     * @param productMappingResult
     * @return
     */
    public ProductMappingFacadeResponse toProductMappingFacadeResponse(ProductMappingResult productMappingResult) {
        ProductMappingFacadeResponse mappingFacadeResponse = new ProductMappingFacadeResponse();
        List<ProductInfoDto> productInfos = new ArrayList<>(productMappingResult.getProductResultList().size());
        for (com.jdl.product.api.response.productmapping.ProductResult productResult : productMappingResult.getProductResultList()) {
            ProductInfoDto productInfoDto = new ProductInfoDto();
            productInfoDto.setProductNo(productResult.getProductNo());
            productInfoDto.setProductName(productResult.getProductName());
            productInfoDto.setProductType(productResult.getProductType());
            productInfos.add(productInfoDto);
        }
        mappingFacadeResponse.setProductInfoDtoList(productInfos);
        return mappingFacadeResponse;
    }

    /**
     * 产品中心Profile构造器
     *
     * @param requestProfile
     * @return
     */
    public com.jdl.product.api.core.bean.RequestProfile toProductRequestProfile(RequestProfile requestProfile) {
        com.jdl.product.api.core.bean.RequestProfile productRequestProfile = new com.jdl.product.api.core.bean.RequestProfile();
        productRequestProfile.setLocale(requestProfile.getLocale());
        productRequestProfile.setTenantId(requestProfile.getTenantId());
        productRequestProfile.setTimeZone(requestProfile.getTimeZone());
        productRequestProfile.setTraceId(requestProfile.getTraceId());
        return productRequestProfile;
    }

    /**
     * RPC入参转换：查询产品的销售时间信息
     */
    public ProductTimeConfigRequest toProductTimeConfigRequest(ProductTimeConfigFacadeRequest productTimeConfigFacadeRequest) {
        ProductTimeConfigRequest productTimeConfigRequest = new ProductTimeConfigRequest();

        // 产品编码
        List<cn.jdl.pms.basic.api.request.producttimeconfig.ProductInfo> productInfoList = new ArrayList<>();
        List<String> productNos = productTimeConfigFacadeRequest.getProductNos();
        for (String productNo : productNos) {
            cn.jdl.pms.basic.api.request.producttimeconfig.ProductInfo productInfo = new cn.jdl.pms.basic.api.request.producttimeconfig.ProductInfo();
            productInfo.setProductNo(productNo);
            productInfoList.add(productInfo);
        }
        productTimeConfigRequest.setProductInfoList(productInfoList);

        // 时间
        TimeInfo timeInfo = new TimeInfo();
        timeInfo.setQueryTime(productTimeConfigFacadeRequest.getQueryTime());
        productTimeConfigRequest.setTimeInfo(timeInfo);

        // 场景：当前固定1-查询产品高峰期时间段
        productTimeConfigRequest.setBusinessScene(PRODUCT_TIME_CONFIG_PEAK);

        return productTimeConfigRequest;
    }

    /**
     * RPC出参转换：查询产品的销售时间信息
     */
    public ProductTimeConfigFacadeResponse toProductTimeConfigFacadeResponse(ProductTimeConfigResponse productTimeConfigResponse) {
        ProductTimeConfigFacadeResponse facadeResponse = new ProductTimeConfigFacadeResponse();
        Map<String, List<ProductTimeConfigFacadeResponse.ProductServiceTimeRange>> timeRangeMap = new HashMap<>();
        if (productTimeConfigResponse != null && CollectionUtils.isNotEmpty(productTimeConfigResponse.getServiceTimeConfigList())) {
            List<ServiceTimeConfig> serviceTimeConfigList = productTimeConfigResponse.getServiceTimeConfigList();
            for (ServiceTimeConfig serviceTimeConfig : serviceTimeConfigList) {
                List<ProductTimeConfigFacadeResponse.ProductServiceTimeRange> list = timeRangeMap.computeIfAbsent(serviceTimeConfig.getProductNo(), k -> new ArrayList<>());
                ProductTimeConfigFacadeResponse.ProductServiceTimeRange productServiceTimeRange = new ProductTimeConfigFacadeResponse.ProductServiceTimeRange();
                productServiceTimeRange.setProductNo(serviceTimeConfig.getProductNo());
                productServiceTimeRange.setStartDate(serviceTimeConfig.getStartDate());
                productServiceTimeRange.setEndDate(serviceTimeConfig.getEndDate());
                list.add(productServiceTimeRange);
            }
        }
        facadeResponse.setTimeRangeMap(timeRangeMap);
        return facadeResponse;
    }
}
