# JDL-OMS-Express 项目信息文档

## 📋 项目概述

**项目名称**: JDL-OMS-Express  
**项目类型**: 传统Spring框架微服务应用  
**主要功能**: 京东物流快递订单管理系统(OMS)  
**技术架构**: 基于Spring Framework的传统微服务架构  
**项目版本**: 1.0.0-SNAPSHOT  
**生成时间**: 2025-09-03  
**分析工具**: Init-Project工作流完整指令  

## 🏗️ 项目白皮书

### 目录结构
```
jdl-oms-express/
├── jdl-oms-express-all/                    # 父POM模块
├── jdl-oms-express-application/            # 应用服务层,对外提供API服务实现类
├── jdl-oms-express-client/                 # 客户端模块,对外提供API服务包
├── jdl-oms-express-domain/                 # 领域层
│   ├── jdl-oms-express-domain-model/       # 领域模型层 - 核心业务实体和值对象
│   ├── jdl-oms-express-domain-service/     # 领域服务层 - 业务逻辑和规则实现
│   ├── jdl-oms-express-domain-infrastructure/ # 基础设施层 - 外部系统适配和数据访问
│   ├── jdl-oms-express-domain-extension/   # 扩展点层 - 业务扩展和插件机制
│   ├── jdl-oms-express-domain-adapter/     # 适配器层 - 协议转换和接口适配
│   └── jdl-oms-express-domain-spec/       # 规范定义层 - 领域规范和约束
├── jdl-oms-express-horizontal/             # 横向通用模块
│   ├── jdl-oms-express-horz-common/        # 通用组件层 - 公共工具和配置
│   ├── jdl-oms-express-horz-extension/     # 扩展点层 - 横向业务扩展实现
│   ├── jdl-oms-express-horz-model/         # 模型层 - 横向业务模型定义
│   └── jdl-oms-express-horz-infrastructure/ # 基础设施层 - 横向业务基础设施
├── jdl-oms-express-shared-common/          # 共享公共模块
├── jdl-oms-express-test/                   # 测试模块
├── jdl-oms-express-vertical-*/             # 垂直业务模块
└── jdl-oms-express-web/                    # Web应用层
    ├── jdl-oms-express-main/               # 主应用
    ├── jdl-oms-express-worker/             # 工作节点
    └── jdl-oms-express-monitor/            # 监控应用
```

### 🛠️ 技术栈信息

#### 基础技术栈
- **Java版本**: 1.8 (maven.compiler.source/target: 1.8)
- **Spring版本**: 5.2.9.RELEASE
- **Maven版本**: 3.8.0
- **编码**: UTF-8
- **打包方式**: WAR (传统Web应用)

#### 数据库技术
- **ORM框架**: MyBatis 3.5.6
- **MyBatis集成**: mybatis-spring 1.3.1
- **数据库驱动**: MySQL Connector Java 5.1.48
- **连接池**: Apache Commons DBCP2 2.4.0 + Commons Pool2 2.6.2

#### Web技术
- **Web框架**: Spring MVC (传统Spring Web)
- **Web服务器**: Tomcat (通过web.xml配置)
- **Servlet版本**: 2.5
- **JSON处理**: Gson 2.8.6, Jackson 1.9.2, FastJSON 1.2.83-jdsec.rc1

#### 测试技术
- **测试框架**: JUnit 4.13.2
- **Spring测试**: spring-test 5.2.9.RELEASE
- **测试跳过**: 配置为跳过测试 (skipTests=true)

#### 构建工具
- **Maven插件**: 
  - maven-compiler-plugin 3.8.0
  - maven-resources-plugin 3.0.2
  - maven-surefire-plugin 2.9

### 🔌 核心业务服务接口
#### 对外提供服务
##### JSF Provider服务列表
基于XML配置的JSF服务，按业务垂直模块组织：

**B2C业务服务**:
- `b2cCreateExpressOrderService` - B2C创建快递订单服务
- `b2cModifyExpressOrderService` - B2C修改快递订单服务
- `b2cCallBackExpressOrderService` - B2C回传快递订单服务
- `b2cDeleteExpressOrderService` - B2C删除快递订单服务
- `b2cCancelExpressOrderService` - B2C取消快递订单服务
- `b2cInterceptExpressOrderService` - B2C拦截快递订单服务
- `b2cEnquiryExpressOrderService` - B2C询价快递订单服务
- `b2cModifyExpressOrderFinanceService` - B2C修改财务信息服务
- `b2cPrecheckExpressOrderService` - B2C前置校验服务
- `b2cSimpleCreateExpressOrderService` - B2C简单创建服务
- `b2cSimpleModifyExpressOrderService` - B2C简单修改服务

**C2C业务服务**:
- 通过 `applicationContext-c2c-provider.xml` 配置

**C2B业务服务**:
- 通过 `applicationContext-c2b-provider.xml` 配置

**O2O业务服务**:
- 通过 `applicationContext-o2o-provider.xml` 配置

**其他垂直业务服务**:
- 冷链业务 (CC-B2B/CC-B2C)
- 快运业务 (Freight-B2C/Freight-C2C)
- 落地配业务 (LM-B2C)
- 合同物流业务
- 国际业务 (Intl-B2C/Intl-C2C)
- TMS业务
- UEP业务

### DDD分层实现

#### 领域层详细结构
```
jdl-oms-express-domain/
├── jdl-oms-express-domain-model/           # 领域模型层
│   └── src/main/java/cn/jdl/oms/express/domain/
│       ├── model/                           # 核心领域模型
│       │   ├── ExpressOrderModel.java       # 快递订单领域模型
│       │   ├── ComplementDomainModel.java   # 输入领域模型
│       │   ├── ExportDomainModel.java       # 输出领域模型
│       │   └── AbstractDomainModel.java     # 抽象领域模型基类
│       ├── vo/                              # 值对象(Value Object)
│       ├── dto/                             # 数据传输对象
│       ├── bo/                              # 业务对象(Business Object)
│       ├── bc/                              # 业务上下文(Business Context)
│       ├── po/                              # 持久化对象(Persistent Object)
│       ├── converter/                       # 数据转换器
│       ├── config/                          # 领域配置
│       ├── utils/                           # 领域工具类
│       ├── facade/                          # 外观模式接口
│       ├── lock/                            # 分布式锁
│       ├── annotation/                      # 领域注解
│       └── statemachine/                    # 状态机
│
├── jdl-oms-express-domain-service/          # 领域服务层
│   └── src/main/java/cn/jdl/oms/express/domain/
│       ├── service/                         # 领域服务接口
│       │   ├── ICreateExpressOrderDomainService.java      # 创建订单服务
│       │   ├── IModifyExpressOrderDomainService.java      # 修改订单服务
│       │   ├── ICancelExpressOrderDomainService.java      # 取消订单服务
│       │   ├── IDeleteExpressOrderDomainService.java      # 删除订单服务
│       │   ├── ICallBackExpressOrderDomainService.java    # 回传订单服务
│       │   ├── IPayExpressOrderDomainService.java         # 支付订单服务
│       │   ├── IRefundExpressOrderDomainService.java      # 退款订单服务
│       │   ├── IInterceptExpressOrderDomainService.java   # 拦截订单服务
│       │   ├── IEnquiryExpressOrderDomainService.java     # 询价订单服务
│       │   ├── IPrecheckExpressOrderDomainService.java    # 前置校验服务
│       │   ├── IUpdateExpressOrderDomainService.java      # 更新订单服务
│       │   ├── IRecoverExpressOrderDomainService.java     # 恢复订单服务
│       │   ├── IReacceptExpressOrderDomainService.java    # 重受理订单服务
│       │   └── IModifyExpressOrderFinanceDomainService.java # 修改财务服务
│       ├── service/impl/                    # 领域服务实现
│       ├── flow/                            # 业务流程编排
│       ├── aop/                             # 面向切面编程
│       ├── ability/                         # 业务能力抽象
│       └── ohs/                             # 开放主机服务
│
├── jdl-oms-express-domain-infrastructure/   # 基础设施层
│   └── src/main/java/cn/jdl/oms/express/domain/infrs/
│       ├── acl/                             # 防腐层(Anti-Corruption Layer)
│       ├── ohs/                             # 开放主机服务(Open Host Service)
│       │   ├── locals/                      # 本地服务实现
│       │   │   ├── repository/              # 仓储实现
│       │   │   ├── repo/                    # 数据访问对象
│       │   │   ├── cache/                   # 缓存服务
│       │   │   ├── redis/                   # Redis缓存
│       │   │   ├── message/                 # 消息服务
│       │   │   ├── scheduler/               # 任务调度
│       │   │   ├── listener/                # 事件监听器
│       │   │   ├── filter/                  # 过滤器
│       │   │   ├── thread/                  # 线程管理
│       │   │   ├── ump/                     # 监控服务
│       │   │   ├── ducc/                    # 配置中心
│       │   │   ├── security/                # 安全服务
│       │   │   ├── es/                      # 搜索引擎
│       │   │   ├── event/                   # 事件处理
│       │   │   ├── drools/                  # 规则引擎
│       │   │   ├── environment/             # 环境配置
│       │   │   └── promise/                 # 承诺服务
│       │   └── remote/                      # 远程服务调用
│       ├── aspect/                          # 切面编程
│       └── annotation/                      # 基础设施注解
│
├── jdl-oms-express-domain-extension/        # 扩展点层
│   └── src/main/java/cn/jdl/oms/express/domain/extension/
│       ├── order/                           # 订单扩展点
│       ├── orderbank/                       # 台账扩展点
│       ├── customer/                        # 客户扩展点
│       ├── product/                         # 产品扩展点
│       ├── address/                         # 地址扩展点
│       ├── status/                          # 状态扩展点
│       ├── fee/                             # 费用扩展点
│       ├── pay/                             # 支付扩展点
│       ├── security/                        # 安全扩展点
│       ├── repository/                      # 仓储扩展点
│       ├── serviceOrder/                    # 服务订单扩展点
│       ├── serviceplus/                     # 增值服务扩展点
│       ├── tms/                             # 运输管理系统扩展点
│       ├── tos/                             # 运输订单系统扩展点
│       ├── resource/                        # 资源扩展点
│       ├── right/                           # 权限扩展点
│       ├── region/                          # 区域扩展点
│       ├── relation/                        # 关系扩展点
│       ├── orderquantity/                   # 订单数量扩展点
│       ├── invoice/                         # 发票扩展点
│       ├── jdorder/                         # 京东订单扩展点
│       ├── merge/                           # 合并扩展点
│       ├── notice/                          # 通知扩展点
│       ├── exception/                       # 异常扩展点
│       ├── hold/                            # 保留扩展点
│       ├── insurance/                       # 保险扩展点
│       ├── boxcode/                         # 箱码扩展点
│       ├── ebs/                             # 企业业务系统扩展点
│       ├── enquiry/                         # 询价扩展点
│       ├── antirepeat/                      # 防重复扩展点
│       ├── basic/                           # 基础扩展点
│       ├── aging/                           # 时效扩展点
│       ├── issue/                           # 问题扩展点
│       ├── wechatpayment/                   # 微信支付扩展点
│       ├── white/                           # 白名单扩展点
│       ├── snapshoot/                       # 快照扩展点
│       ├── scheduler/                       # 调度器扩展点
│       ├── pickupcode/                      # 取件码扩展点
│       ├── presort/                         # 预分拣扩展点
│       ├── ordertrack/                      # 订单轨迹扩展点
│       ├── orderResult/                     # 订单结果扩展点
│       ├── integral/                        # 积分扩展点
│       ├── iot/                             # 物联网扩展点
│       ├── ious/                            # 欠条扩展点
│       ├── init/                            # 初始化扩展点
│       ├── black/                           # 黑名单扩展点
│       ├── compare/                         # 比较扩展点
│       ├── coupon/                          # 优惠券扩展点
│       ├── effective/                       # 生效扩展点
│       ├── antirelock/                      # 防重复锁定扩展点
│       ├── bindOriginalOrder/               # 绑定原单扩展点
│       └── unicode/                         # 统一码扩展点
│
├── jdl-oms-express-domain-adapter/          # 适配器层
│   └── src/main/java/cn/jdl/oms/express/domain/
│       └── adapter/                         # 协议适配器
│
└── jdl-oms-express-domain-spec/             # 规范定义层
    └── src/main/java/cn/jdl/oms/express/domain/
        └── spec/                            # 领域规范定义
```
### 订单模型ExpressOrderModel解析

#### ExpressOrderModel类属性说明
ExpressOrderModel是系统的核心领域订单模型，代表了快递订单的完整业务实体，是DDD架构中的聚合根。主要属性如下：

| 属性名称 | 类型 | 说明 |
|---------|------|------|
| orderBusinessIdentity | OrderBusinessIdentity | 订单业务身份，区分batrix业务身份独立环境执行 |
| businessScene | String | 业务场景，如创建、修改、取消等 |
| customOrderNo | String | 业务单号（运单号） |
| parentOrderNo | String | 父订单号 |
| orderType | OrderTypeEnum | 订单类型，如B2C、C2C、B2B等 |
| orderSubType | OrderSubTypeEnum | 订单子类型 |
| orderUsage | Integer | 订单用途 |
| orderStatus | OrderStatus | 订单状态，包含主状态和子状态 |
| logisticsCloudPin | String | 物流云登录pin |
| customer | Customer | 客户信息，包含履约账号等 |
| channel | Channel | 渠道信息 |
| productDelegate | ProductDelegate | 产品及增值服务信息 |
| consignee | Consignee | 收货人信息 |
| consignor | Consignor | 发货人信息 |
| shipment | Shipment | 配送要求 |
| finance | Finance | 财务信息，包含结算方式、费用明细等 |
| promotion | Promotion | 营销信息 |
| cargoDelegate | CargoDelegate | 货品管理 |
| goodsDelegate | GoodsDelegate | 商品管理 |
| refOrderInfoDelegate | RefOrderDelegate | 关联单信息 |
| customStatus | Integer | 订单的前台个性化状态 |
| executedStatus | String | 履约执行状态 |
| executedStatusDesc | String | 扩展状态描述 |
| cancelStatus | CancelStatusEnum | 取消状态 |
| orderSnapshot | ExpressOrderModel | 订单快照，用于修改、取消、查询场景 |
| extendProps | Map<String, String> | 订单扩展信息 |
| initiatorType | InitiatorTypeEnum | 发起人类型 |
| operator | String | 操作人 |
| operateTime | Date | 操作时间 |
| remark | String | 备注 |
| exportModel | ExportDomainModel | 信息输出模型 |
| complementModel | ComplementDomainModel | 信息补全模型 |
| cancelInterceptType | CancelInterceptTypeEnum | 取消拦截类型 |
| cancelReasonCode | String | 取消原因编码 |
| cancelReason | String | 取消原因 |
| clearFields | List<String> | 修改场景-清空的字段 |
| modifiedFields | Map<String, String> | 修改场景-集合字段操作模式 |
| interceptType | InterceptTypeEnum | 拦截类型 |
| hiddenMark | String | 隐藏标 |
| businessSolution | BusinessSolution | 解决方案 |
| enquiry | Enquiry | 询价信息 |
| agreementDelegate | AgreementDelegate | 协议管理 |
| orderSign | Map<String, String> | 订单标识 |
| returnInfoVo | ReturnInfoVo | 退货信息 |
| intercept | Intercept | 拦截信息 |
| readdressStatus | ReaddressStatusEnum | 改址状态 |
| fulfillment | Fulfillment | 履约信息 |
| recheckVolume | Volume | 复核体积 |
| recheckWeight | Weight | 复核重量 |
| customs | Customs | 跨境报关信息 |
| attachments | List<Attachment> | 附件列表 |
| orderNetWeight | Weight | 订单总净重 |
| orderWeight | Weight | 订单总毛重 |
| orderVolume | Volume | 订单总体积 |
| refundInfoVo | RefundInfoVo | 退款信息 |


#### 横向模块详细结构
```
jdl-oms-express-horizontal/
├── jdl-oms-express-horz-common/             # 通用组件层
│   └── pom.xml                              # 公共依赖管理
│
├── jdl-oms-express-horz-extension/          # 扩展点层
│   └── src/main/java/cn/jdl/oms/express/horz/ext/
│       ├── order/                            # 订单相关扩展点
│       │   ├── ModifyBuildEnquiryOrderExtension.java      # 修改构建询价订单扩展
│       │   ├── OrderNoExtension.java                      # 订单号扩展
│       │   ├── CreateOrderFieldDataNotifyExtension.java   # 创建订单字段数据通知扩展
│       │   ├── EnquiryOrderFieldDataNotifyExtension.java  # 询价订单字段数据通知扩展
│       │   ├── ModifyOrderFieldDataNotifyExtension.java   # 修改订单字段数据通知扩展
│       │   ├── CallBackOrderFieldDataNotifyExtension.java # 回传订单字段数据通知扩展
│       │   ├── CancelOrInterceptOrderFieldDataNotifyExtension.java # 取消/拦截订单字段数据通知扩展
│       │   └── OrderRelationRepositoryExtension.java      # 订单关系仓储扩展
│       ├── status/                           # 状态相关扩展点
│       │   ├── CallBackOrderStatusExtension.java          # 回传订单状态扩展
│       │   ├── CancelOrderStatusExtension.java            # 取消订单状态扩展
│       │   ├── InterceptOrderStatusExtension.java         # 拦截订单状态扩展
│       │   ├── CancelOrderStatusNotifyExtension.java      # 取消订单状态通知扩展
│       │   ├── OrderStatusNotifyExtension.java            # 订单状态通知扩展
│       │   ├── OrderStatusNotifyRetryExtension.java       # 订单状态通知重试扩展
│       │   └── DeleteOrderStatusExtension.java            # 删除订单状态扩展
│       ├── customer/                         # 客户相关扩展点
│       │   ├── PrecheckCustomerConfigExtension.java      # 前置校验客户配置扩展
│       │   └── PrechargeOccupyExtension.java             # 预充值占用扩展
│       ├── security/                         # 安全相关扩展点
│       │   └── AntiDesensitizationExtension.java         # 反脱敏处理扩展
│       ├── aging/                            # 时效相关扩展点
│       │   └── AgingExtension.java                       # 时效揽收校验扩展
│       ├── ebs/                              # EBS系统扩展点
│       │   └── PushEBSExtension.java                     # 推送EBS扩展
│       ├── address/                          # 地址相关扩展点
│       ├── product/                          # 产品相关扩展点
│       ├── pay/                              # 支付相关扩展点
│       ├── orderbank/                        # 台账相关扩展点
│       ├── repository/                       # 仓储相关扩展点
│       ├── serviceplus/                      # 增值服务扩展点
│       ├── shippingLine/                     # 航线扩展点
│       ├── tms/                              # 运输管理系统扩展点
│       ├── tos/                              # 运输订单系统扩展点
│       ├── refund/                           # 退款扩展点
│       ├── orderquantity/                    # 订单数量扩展点
│       ├── boxcode/                          # 箱码扩展点
│       ├── bindOriginalOrder/                # 绑定原单扩展点
│       ├── scheduler/                        # 调度器扩展点
│       ├── iot/                              # 物联网扩展点
│       ├── integral/                         # 积分扩展点
│       ├── effective/                        # 生效扩展点
│       ├── antirelock/                       # 防重复锁定扩展点
│       ├── antirepeat/                       # 防重复扩展点
│       ├── enquiry/                          # 询价扩展点
│       ├── relation/                         # 关系扩展点
│       ├── white/                            # 白名单扩展点
│       ├── black/                            # 黑名单扩展点
│       ├── right/                            # 权限扩展点
│       ├── notice/                           # 通知扩展点
│       ├── bind/                             # 绑定扩展点
│       ├── issue/                            # 问题扩展点
│       ├── insurance/                        # 保险扩展点
│       ├── coupon/                           # 优惠券扩展点
│       ├── basic/                            # 基础扩展点
│       ├── compare/                          # 比较扩展点
│       ├── init/                             # 初始化扩展点
│       ├── invoice/                          # 发票扩展点
│       ├── jdorder/                          # 京东订单扩展点
│       ├── merge/                            # 合并扩展点
│       ├── ordertrack/                       # 订单轨迹扩展点
│       ├── orderResult/                      # 订单结果扩展点
│       ├── presort/                          # 预分拣扩展点
│       ├── pickupcode/                       # 取件码扩展点
│       ├── snapshoot/                        # 快照扩展点
│       └── unicode/                          # 统一码扩展点
│
├── jdl-oms-express-horz-model/               # 模型层
│   └── src/main/java/cn/jdl/oms/express/horz/
│       └── App.java                          # 横向模块主类
│
└── jdl-oms-express-horz-infrastructure/      # 基础设施层
    └── src/main/java/cn/jdl/oms/express/horz/infrs/
        ├── acl/                               # 防腐层(Anti-Corruption Layer)
        ├── ohs/                               # 开放主机服务(Open Host Service)
        │   ├── locals/                        # 本地服务实现
        │   │   ├── redis/                     # Redis缓存服务
        │   │   ├── message/                   # 消息服务
        │   │   └── es/                        # 搜索引擎服务
        │   └── remote/                        # 远程服务调用
        ├── repo/                              # 数据访问层
        └── sharddatasource/                   # 分库分表数据源
```

#### 横向模块架构特点
- **通用性设计**: 提供跨垂直业务的通用扩展点和服务
- **扩展点机制**: 通过50+个扩展点支持横向业务功能的灵活扩展
- **分层架构**: 遵循DDD分层架构，职责清晰明确
- **防腐层设计**: 通过ACL层隔离外部系统依赖
- **开放主机服务**: 通过OHS层对外提供服务接口
- **缓存策略**: 集成Redis缓存提升性能
- **消息服务**: 支持异步消息处理
- **搜索引擎**: 集成ES支持复杂查询
- **分库分表**: 支持大数据量场景的数据分片

#### 应用层组织
- **包结构**: `jdl-oms-express-application/`
- **用例编排**: 应用服务实现业务用例
- **服务调用**: 协调领域服务和基础设施服务

#### 基础设施层
- **包结构**: `jdl-oms-express-domain/jdl-oms-express-domain-infrastructure/`
- **外部适配**: 数据库访问、外部服务调用
- **数据访问**: MyBatis Repository实现

### 扩展点机制

#### 扩展接口定义
- **扩展接口**: 通过Extension接口定义扩展点
- **位置**: `jdl-oms-express-domain/jdl-oms-express-domain-extension/`

#### 扩展实现模式
- **垂直业务扩展**: 各垂直业务模块的扩展实现
- **通用扩展**: 横向通用模块的扩展实现

### 外部服务依赖

#### JSF Consumer服务
- 通过 `applicationContext-consumer.xml` 配置
- 支持多种业务场景的服务调用

#### HTTP客户端服务
- 通过 `applicationContext-http.xml` 配置
- 支持外部HTTP服务调用

#### 数据库访问
- **Repository模式**: 通过MyBatis实现数据访问
- **表访问**: 支持多种业务表的数据操作
- **查询模式**: 支持复杂SQL查询和动态SQL

### 应用服务层 API 服务实现类分析

jdl-oms-express-application 应用服务层提供了一系列面向物流业务的 API 服务实现类，这些服务共同构成了完整的快递订单生命周期管理体系。根据物流业务背景，各服务实现类的具体用途如下：

#### 订单创建相关服务
- **CreateExpressOrderServiceImpl**: 快递订单创建服务，负责处理快递订单的创建请求，支持单个订单创建和批量订单创建。实现了参数校验、订单记录创建、运单号分配、费用计算等核心功能，支持同步和异步处理模式，配置有专用线程池以提高并发处理能力。
- **SimpleCreateExpressOrderServiceImpl**: 简化版快递订单创建服务，提供简化的参数要求和流程，适用于快速下单场景，减少必填字段，提高下单效率。
- **PrecheckExpressOrderServiceImpl**: 订单前置校验服务，在正式创建订单前进行地址有效性、服务可用性、客户权限等前置校验，避免无效订单创建，提高系统效率。

##### 快递订单创建服务入参分析
CreateExpressOrderServiceImpl 类中的 createOrder 方法是快递订单创建的核心入口，该方法接收两个关键参数：

###### RequestProfile profile
请求上下文信息对象，来自 cn.jdl.batrix.spec 包，包含以下关键信息：
- traceId：链路追踪ID，用于全链路监控
- tenantId：租户ID，用于多租户隔离
- locale：地区/语言设置，默认为"zh_CN"
- 其他上下文信息，用于处理国际化多语言、多租户、业务身份以及未来扩展的需求

###### CreateExpressOrderRequest request
创建订单的核心请求参数对象，包含以下主要字段：

**业务标识信息**
- businessIdentity：业务身份，标识业务单元和业务类型（如B2C、C2C等）
- orderType：订单类型，如普通订单、退货单等
- orderSubType：订单子类型，进一步细分订单类型
- orderUsage：订单用途，标识订单的使用场景
- orderSign：订单标识，包含额外的订单标记信息

**参与方信息**
- customerInfo：交易客户信息，包含客户账号、履约账号等
- channelInfo：渠道信息，包含系统调用者、渠道来源等
- consignorInfo：发货人信息，包含发货地址、联系人等
- consigneeInfo：收货人信息，包含收货地址、联系人等

**货物信息**
- productInfos：产品服务信息列表，包含所选物流产品和增值服务
- cargoInfos：货品信息列表，包含货物的名称、数量、重量等
- goodsInfos：商品信息列表，包含商品的SKU、价格等
- orderWeightInfo：订单总重量
- orderVolumeInfo：订单总体积

**配送与财务信息**
- shipmentInfo：配送信息，包含期望取件时间、配送要求等
- financeInfo：财务信息，包含支付方式、结算方式、费用明细等
- promotionInfo：营销信息，包含优惠券、活动等
- fulfillmentInfo：履约信息，包含履约相关的特殊要求

**关联信息**
- refOrderInfo：交易关联单，关联的订单信息
- parentOrderNo：父订单号，用于子母单关系
- orderNo：订单号，可能在某些场景下预先生成
- businessSolutionInfo：解决方案信息
- agreementInfos：协议信息列表
- returnInfo：退货信息，用于退货场景

**其他信息**
- initiatorType：下单人类型
- operator：下单人唯一标识，最大长度50
- remark：下单备注，最大长度500
- extendProps：扩展字段，用于存储额外信息

###### 处理流程
1. 参数校验：使用JSR303规范进行基本信息校验
2. 领域模型转换：通过expressOrderModelOf方法将请求参数转换为领域模型
3. 领域服务调用：调用receiveExpressOrderDomainService.createOrder执行实际的订单创建逻辑
4. 结果转换：将领域服务返回的结果转换为API响应对象
5. 异常处理：统一处理各类异常，包括业务异常、系统异常等
6. 监控埋点：集成UMP和PFinder进行性能监控和业务监控

整个入参设计体现了物流业务的复杂性，涵盖了寄收双方信息、货物信息、配送要求、财务信息等多个维度，支持多种业务场景的订单创建需求。

#### 订单修改相关服务
- **ModifyExpressOrderServiceImpl**: 快递订单修改服务，处理对已创建订单的修改请求，支持修改收发地址、联系人、货物信息等，会触发费用重新计算，支持批量修改操作。
- **SimpleModifyExpressOrderServiceImpl**: 简化版订单修改服务，提供简化的修改流程，适用于常见修改场景，减少修改复杂度。
- **ModifyExpressOrderFinanceServiceImpl**: 订单财务信息修改服务，专门处理订单支付方式、结算方式、费用等财务相关信息的修改，与普通修改服务分离以确保财务数据安全性。
- **ModifyExternalExpressOrderServiceImpl**: 外部订单修改服务，处理来自外部系统的订单修改请求，进行必要的协议转换和权限校验。

##### 快递订单修改服务入参分析
ModifyExpressOrderServiceImpl 类中的 modifyOrder 方法是快递订单修改的核心入口，该方法接收两个关键参数：

###### RequestProfile profile
请求上下文信息对象，来自 cn.jdl.batrix.spec 包，包含以下关键信息：
- traceId：链路追踪ID，用于全链路监控
- tenantId：租户ID，用于多租户隔离
- locale：地区/语言设置，默认为"zh_CN"
- 其他上下文信息，用于处理国际化多语言、多租户、业务身份以及未来扩展的需求

###### ModifyExpressOrderRequest request
修改订单的核心请求参数对象，包含以下主要字段：

**必填标识信息**
- businessIdentity：业务身份，标识业务单元和业务类型（如B2C、C2C等）
- orderNo：订单号，用于唯一标识需要修改的订单

**可修改信息**
- consignorInfo：发货人信息，包含发货地址、联系人等
- consigneeInfo：收货人信息，包含收货地址、联系人等
- cargoInfos：货品信息列表，包含货物的名称、数量、重量等
- goodsInfos：商品信息列表，包含商品的SKU、价格等
- productInfos：产品服务信息列表，包含所选物流产品和增值服务
- shipmentInfo：配送信息，包含期望取件时间、配送要求等
- financeInfo：财务信息，包含支付方式、结算方式、费用明细等
- customerInfo：交易客户信息，包含客户账号、履约账号等
- channelInfo：渠道信息，包含系统调用者、渠道来源等

**修改控制信息**
- initiatorType：修改发起人类型，如寄件人(4)、收件人(5)、客服(6)等
- operator：修改人唯一标识，最大长度50
- remark：修改备注，最大长度500
- modifiedFields：对订单主档的集合字段标记操作类型，用于控制增量更新或全量覆盖
- extendProps：扩展字段，用于存储额外信息

**其他可选信息**
- orderWeight/orderNetWeight：订单总毛重/净重
- orderVolume：订单总体积
- recheckWeight/recheckVolume：复核重量/体积
- customOrderNo：自定义单号
- businessSolutionInfo：解决方案信息
- attachmentInfos：附件列表，如报关单、装箱单等文档
- customsInfo：跨境报关信息

###### 处理流程
1. 参数校验：使用JSR303规范进行基本信息校验
2. 领域模型转换：通过expressOrderModelOf方法将请求参数转换为领域模型
3. 领域服务调用：调用modifyExpressOrderDomainService.modifyOrder执行实际的订单修改逻辑
4. 结果转换：将领域服务返回的结果转换为API响应对象
5. 异常处理：统一处理各类异常，包括业务异常、系统异常等
6. 监控埋点：集成UMP和PFinder进行性能监控和业务监控
7. 消息发送：通过expressOrderFlowService.sendModifyOrderRecordMq发送订单修改记录消息

修改服务的入参设计特点是支持增量修改，通过modifiedFields字段控制对集合类字段（如productInfos、cargoInfos）的操作类型，可以实现全量覆盖或全量删除。同时，修改服务会触发费用重新计算、预分拣结果更新等后续处理流程，确保订单数据的一致性和完整性。

#### 订单状态管理服务
- **CancelExpressOrderServiceImpl**: 订单取消服务，处理订单取消请求，执行取消流程，处理退款逻辑，更新订单状态，支持前置校验和批量取消。
- **DeleteExpressOrderServiceImpl**: 订单删除服务，处理订单删除请求，执行逻辑删除操作，保留历史记录但标记为已删除状态。
- **InterceptExpressOrderServiceImpl**: 订单拦截服务，在物流配送过程中处理拦截请求，可修改配送路径或取消配送，是物流配送环节的紧急干预机制。
- **RecoverExpressOrderServiceImpl**: 订单恢复服务，将已删除或已取消的订单恢复为有效状态，支持特定业务场景下的订单状态回滚。
- **ReacceptExpressOrderServiceImpl**: 订单重新接收服务，对已取消或异常的订单进行重新接收处理，生成新的订单记录但保留原订单关联关系。

#### 订单处理相关服务
- **CallBackExpressOrderServiceImpl**: 订单回传服务，接收并处理物流节点信息，更新订单状态，触发后续业务流程，是订单状态实时更新的关键服务。
- **PayExpressOrderServiceImpl**: 订单支付服务，处理订单支付请求，更新订单支付状态，触发后续配送流程。
- **RefundExpressOrderServiceImpl**: 订单退款服务，处理订单退款请求，计算退款金额，发起退款流程，更新订单财务状态。
- **UpdateExpressOrderServiceImpl**: 订单更新服务，处理订单状态更新请求，是订单生命周期管理的通用服务。

#### 特殊功能服务
- **EnquiryExpressOrderServiceImpl**: 订单询价服务，提供快递费用查询功能，根据寄收地址、货物信息等计算快递费用，与计费系统对接，提供实时询价能力。
- **CheckBoxCodeExpressOrderServiceImpl**: 箱码校验服务，验证包装箱码的有效性和合法性，支持物流包装标准化和可追溯性。

这些服务实现类通过调用领域层服务完成具体业务逻辑，遵循DDD架构设计原则，实现了应用层与领域层的解耦。每个服务都配置有专用的线程池，支持高并发处理，并集成了完善的监控、日志和异常处理机制。

## 🔧 中间件配置详情

### JSF配置

#### 使用模式
- **集成方式**: 原生Spring集成 (非DongBoot)
- **配置模式**: XML配置方式
- **依赖版本**: 通过com.jd:jsf依赖引入

#### 配置文件
- **主配置**: `spring/rpc/applicationContext-provider.xml`
- **业务配置**: 各垂直业务的独立provider配置文件
- **消费者配置**: `spring/rpc/applicationContext-consumer.xml`
- **过滤器配置**: `spring/rpc/applicationContext-filer.xml`

#### JSF配置样例 (供AI开发参考)

##### Provider配置样例
```xml
<!-- B2C订单接单服务 -->
<jsf:provider id="b2cCreateExpressOrderService"
              interface="cn.jdl.oms.express.service.CreateExpressOrderService"
              alias="${jdl.oms.express.b2c.receive.provider.alias}"
              ref="createExpressOrderServiceImpl"
              timeout="${jdl.oms.express.b2c.receive.provider.timeout}"
              concurrents="${jsf.provider.create.threads}"
              filter="providerChainFilter"
              server="jsf">
    <jsf:parameter key="token" value="${jdl.oms.express.b2c.receive.provider.token}"/>
</jsf:provider>
```

##### Consumer配置样例
```xml
<!-- 持久化接单 -->
<jsf:consumer id="persistOrderApi"
              interface="com.jdl.cp.op.client.api.PersistOrderApi"
              alias="${jsf.persistOrderApi.alias}"
              protocol="jsf"
              serialization="hessian"
              filter="consumerChainFilter"
              timeout="${jsf.persistOrderApi.timeout}"
              retries="${jsf.persistOrderApi.retries}">
    <jsf:parameter key="token" value="${jsf.persistOrderApi.token}" hide="true"/>
</jsf:consumer>
```

##### 核心配置元素说明
- **interface**: 服务接口全限定名
- **alias**: 服务别名(环境变量配置)
- **ref**: 服务实现类Bean引用(仅Provider)
- **timeout/retries**: 超时时间和重试次数
- **filter**: 过滤器链
- **token**: 认证令牌

#### 集成约束
- **新服务必须遵循**: XML配置模式
- **服务命名规范**: 按业务类型前缀命名
- **线程池配置**: 必须配置对应的线程池参数
- **过滤器配置**: 必须配置providerChainFilter等过滤器
- **超时重试**: 必须配置超时和重试次数

### JMQ配置

#### 使用模式
- **集成方式**: 原生JMQ集成 (非DongBoot)
- **配置模式**: XML配置方式
- **依赖版本**: 通过com.jd.jmq:jmq-client-core依赖引入

#### 配置文件
- **生产者配置**: `spring/jmq/applicationContext-jmq-producer.xml`
- **JMQ4配置**: `spring/jmq/applicationContext-jmq4-producer.xml`

#### 连接配置
- **应用标识**: `jmq.app=JDLOMSEXPRESS`
- **用户名**: `jmq.userName=JDLOMSEXPRESS`
- **密码**: `jmq.password=8039AD67`
- **服务地址**: `jmq.address=jmq-testcluster.jd.local:50088`

#### Topic列表
- **订单相关**:
  - `cancel_pay_timeOut_order` - 支付超时取消订单
  - `callback_order_record` - 订单回传记录
  - `modify_order_record` - 订单修改记录
  - `cancel_order_record` - 订单取消记录
  - `delete_order_record` - 订单删除记录
  - `order_snapshot_msg` - 订单快照
  - `orderBank_record` - 台账流水记录
  - `create_order_record` - 接单记录
- **业务相关**:
  - `retry_order_pdq` - PDQ异常重试
  - `init_order_bank_c2c` - C2C台账初始化
  - `init_order_bank_b2c` - B2C台账初始化
  - `init_order_bank_c2b` - C2B台账初始化
- **冷链业务**:
  - `lbs2eclp_fee_infos_result` - 冷链计费结果
  - `coldchain_waybill_unpaid` - 冷链运单待支付
  - `eclp_to_lbs_enquiry_fee` - 冷链整车询价推计费

#### 环境变量配置
- **JMQ环境**: `jmq.environment=TEST`
- **应用配置**: `jmq.app`, `jmq.userName`, `jmq.password`, `jmq.address`
- **降级配置**: `jmq.downgrade`, `jmq.downgradeTime`, `jmq.errorCount`

#### 集成约束
- **新消息处理必须遵循**: XML配置模式
- **Topic命名规范**: 使用下划线分隔的业务描述
- **生产者配置**: 必须配置对应的JMQMessageProducer Bean
- **环境配置**: 必须配置对应环境的JMQ连接参数

### DUCC配置

#### 使用状态
- **使用状态**: 已集成DUCC配置中心
- **依赖版本**: `com.jd.laf.config:laf-config-client-jd-spring:1.4.3`

#### 配置文件
- **主配置**: `spring/ucc/applicationContext-ducc.xml`
- **配置监听器**: `ExpressDUCConfigCenter`

#### 配置项列表
- **业务开关**:
  - `expressUccConfigCenter.sensitiveWordsSwitch` - 敏感词校验开关
  - `expressUccConfigCenter.antiConcurrentSwitch` - 防并发能力降级开关
  - `expressUccConfigCenter.sendMessageSwitch` - JMQ消息发送开关
  - `expressUccConfigCenter.orderBankMergeSwitch` - 台账合并开关
- **缓存配置**:
  - `expressUccConfigCenter.addressOrgCacheSwitch` - 地址机构缓存开关
  - `expressUccConfigCenter.addressOrgCacheExpireSeconds` - 缓存过期时间
- **业务配置**:
  - `expressUccConfigCenter.cancelPayTimeOutOrderBuffer` - 支付超时取消缓冲
  - `expressUccConfigCenter.orderRelationCustomerMinLength` - 关联关系最小长度
  - `expressUccConfigCenter.cargoQuantitySystemCallers` - 货品数量系统调用者
  - `expressUccConfigCenter.cargoWeightSystemCallers` - 货品重量系统调用者

### UMP配置

#### 使用状态
- **使用状态**: 已集成UMP监控
- **依赖版本**: `com.jd.ump:profiler:********`
#### 命名规范
- 公司主体-域.类名.方法名.类型.自定义信息(业务身份)
- 系统监控Key：jdl-trade.order.CreateExpressOrderServiceImpl.createOrder.biz.cn_jdl_c2c
#### 使用方式
- 核心方法要同时增加系统监控，捕获系统异常时进行系统异常打点，示例如下
```java
    public static String UMP_KEY_PREFIX = "jdl-trade.order.";

    CallerInfo callerInfo = Profiler.registerInfo(UMP_KEY_PREFIX + this.getClass().getSimpleName() + ".execute.tech."+"request.getBusinessIdentity().getBusinessUnit()"
            , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE  
            , UmpKeyConstants.METHOD_ENABLE_HEART  
            , UmpKeyConstants.METHOD_ENABLE_TP);
            
    try {
        //业务逻辑...;
    } catch (Exception e){
        LOGGER.error("XX系统可用率异常", e);
        Profiler.functionError(callerInfo);
    } finally {  
        Profiler.registerInfoEnd(callerInfo);  
    }
```

## 🏛️ 架构实现细节

#### 领域层组织
- **包结构**: `jdl-oms-express-domain/`
- **实体定义**: `domain-model/` - 领域模型和值对象
- **业务规则**: `domain-service/` - 领域服务实现
- **领域扩展**: `domain-extension/` - 业务扩展点
- **领域规范**: `domain-spec/` - 领域规范定义

#### 领域层架构特点
- **分层设计**: 严格按照DDD分层架构设计，每层职责明确
- **扩展机制**: 通过扩展点机制支持业务功能的灵活扩展
- **防腐层**: 通过ACL层隔离外部系统，保持领域模型的纯净性
- **开放主机服务**: 通过OHS层对外提供服务接口
- **状态机**: 支持复杂业务流程的状态管理
- **规则引擎**: 集成Drools规则引擎支持业务规则配置
- **事件驱动**: 支持领域事件的发布和订阅
- **缓存策略**: 多级缓存策略提升性能
- **监控集成**: 集成UMP监控和DUCC配置中心

## 💻 技术实现模式

### 监控埋点模式
- **UMP使用模式**: 通过InitHeartBeats初始化监控
- **代码示例**: 
```java
@Bean
public InitHeartBeats initHeartBeats() {
    InitHeartBeats heartBeats = new InitHeartBeats();
    heartBeats.setJvmKey("${ump.monitor.jvmKey}");
    heartBeats.setHeartBeatKey("${ump.monitor.heartBeatKey}");
    return heartBeats;
}
```

### 异常处理模式
- **统一异常处理**: 通过Spring AOP实现
- **异常类型**: 业务异常、系统异常、参数异常
- **处理方式**: 统一异常拦截和日志记录

### 配置管理模式
- **DUCC使用模式**: 通过DUCC配置中心管理配置
- **配置监听**: 支持配置变更实时监听
- **配置注入**: 通过Spring容器注入配置

## 📝 代码约定

### 包结构约定
- **Controller包**: `cn.jdl.oms.express.*.controller`
- **Service包**: `cn.jdl.oms.express.*.service`
- **Repository包**: `cn.jdl.oms.express.*.repository`
- **Entity包**: `cn.jdl.oms.express.*.entity`
- **DTO包**: `cn.jdl.oms.express.*.dto`

### 命名约定
- **类命名**: 使用驼峰命名法，如`CreateExpressOrderService`
- **方法命名**: 动词+名词，如`createOrder`、`modifyOrder`
- **变量命名**: 使用驼峰命名法
- **常量命名**: 使用大写字母和下划线

### 开发约定
- **新增字段**: 在原来的对象基础上新增字段,不要重新创建对象


## ⚠️ 集成约束

### 技术栈约束
- **必须使用的Java版本**: Java 1.8
- **必须使用的Spring版本**: Spring 5.2.9.RELEASE
- **必须使用的Maven版本**: Maven 3.8.0+
- **必须使用的编码**: UTF-8

### 中间件约束
- **JSF配置模式**: 必须使用XML配置模式
- **JMQ配置模式**: 必须使用XML配置模式
- **DUCC配置模式**: 必须使用DUCC配置中心
- **UMP监控模式**: 必须使用UMP监控规范

## 🚀 开发约束和规范

### 新功能开发约束
- **基于现有架构**: 必须遵循现有的DDD分层架构
- **扩展点使用**: 优先使用现有的扩展点机制
- **配置管理**: 必须使用DUCC配置中心管理配置

### 外部集成约束
- **新增外部服务集成的约束**: 必须实现对应的适配器
- **协议支持**: 支持HTTP、JSF、JMQ等协议
- **错误处理**: 必须实现统一的错误处理机制

### 领域层开发约束
- **领域模型约束**: 新增领域模型必须继承AbstractDomainModel基类
- **领域服务约束**: 新增领域服务必须使用@DomainService注解标注
- **扩展点约束**: 新增业务扩展必须在对应的extension包下实现
- **防腐层约束**: 外部系统集成必须通过ACL层实现
- **状态机约束**: 复杂业务流程必须通过状态机管理
- **规则引擎约束**: 业务规则变更必须通过Drools规则引擎配置
- **缓存约束**: 数据访问必须遵循缓存策略和Key命名规范
- **监控约束**: 关键业务操作必须集成UMP监控埋点

### 模型转换开发约束

#### 接单请求入参CreateExpressOrderRequest 到 ExpressModel 转换规则

- **转换流程约束**: 必须通过 `CreateExpressOrderTranslator.translator` 方法进行转换，禁止直接构造 `ExpressModel`
- **创建器模式约束**: 必须使用 `ExpressOrderModelCreator` 作为中间构建对象
- **空值处理约束**: 必须使用 `Optional.ofNullable().ifPresent()` 模式处理可能为空的字段
- **枚举转换约束**: 必须使用枚举类的 `of` 方法将字符串或整数转换为对应的枚举值
- **嵌套对象转换约束**: 对于嵌套对象，必须递归调用相应的转换方法
- **集合转换约束**: 对于集合类型，必须使用 `stream().map().collect()` 或循环遍历进行转换
- **默认值设置约束**: 对于必要字段，在为空时必须设置合理的默认值
- **命名规范约束**: 转换方法必须遵循 `xxxOf` 的命名格式
- **单一职责约束**: 每个转换方法只负责一种类型的转换
- **数据完整性约束**: 转换过程中不得丢失原始请求中的任何信息
- **业务规则约束**: 必须确保转换后的领域模型满足业务规则（如必填字段、字段格式等）

#### 接单服务入参订单模型转换映射关系

| CreateExpressOrderRequest 字段 | ExpressModel 字段 | 转换方法 |
|-------------------------------|------------------|---------|
| businessIdentity | orderBusinessIdentity | businessIdentityOf |
| orderType | orderType | OrderTypeEnum.of |
| orderSubType | orderSubType | OrderSubTypeEnum.of |
| orderUsage | orderUsage | 直接赋值 |
| customerInfo | customer | customerOf |
| channelInfo | channel | channelOf |
| consignorInfo | consignor | consignorOf |
| consigneeInfo | consignee | consigneeOf |
| productInfos | productDelegate | productInfosOf |
| cargoInfos | cargoDelegate | cargoInfosOf |
| goodsInfos | goodsDelegate | goodsInfosOf |
| shipmentInfo | shipment | shipmentInfoOf |
| financeInfo | finance | financeInfoOf |
| promotionInfo | promotion | promotionInfoOf |
| refOrderInfo | refOrderInfoDelegate | refOrderOf |
| extendProps | extendProps | extendPropsOf |
| initiatorType | initiatorType | initiatorTypeOf |
| operator | operator | 直接赋值 |
| remark | remark | 直接赋值 |
| orderSign | orderSign | 直接赋值 |
| businessSolutionInfo | businessSolution | businessSolutionInfoOf |
| agreementInfos | agreementDelegate | agreementInfosOf |
| returnInfo | returnInfoVo | returnInfoOf |
| fulfillmentInfo | fulfillment | fulfillmentInfoOf |
| customsInfo | customs | customsInfoOf |
| attachmentInfos | attachments | attachmentInfosOf |
| orderWeightInfo | orderWeight | orderWeightInfoOf |
| orderVolumeInfo | orderVolume | orderVolumeInfoOf |
| orderNetWeightInfo | orderNetWeight | orderNetWeightInfoOf |

#### 维护建议

- **字段新增约束**: 新增字段时，必须同时修改 `ExpressOrderModel`、`ExpressOrderModelCreator` 和 `CreateExpressOrderTranslator`
- **字段类型变更约束**: 修改字段类型时，必须确保转换逻辑兼容新的类型
- **字段删除约束**: 删除字段时，必须确保不影响现有业务逻辑
- **转换逻辑变更约束**: 转换逻辑变更时，必须进行充分的单元测试和集成测试
- **同步更新约束**: 必须保持转换逻辑与领域模型的同步更新

### ExpressModel 到持久化对象转换规则

#### 转换流程约束

- **转换流程约束**: 必须通过 `CreateRepositoryAbility` 和 `IRepositoryExtension` 实现类进行持久化处理
- **防腐层约束**: 必须使用 `CreateOrderFacadeTranslator.toCreateOrderFacadeRequest` 方法将 `ExpressOrderModel` 转换为持久化请求对象
- **扩展点约束**: 必须通过扩展点机制实现不同业务场景下的持久化逻辑
- **事务约束**: 持久化操作必须在事务中执行，确保数据一致性
- **异常处理约束**: 必须捕获并处理持久化过程中的异常，并进行重试或补偿
- **监控约束**: 必须对持久化操作进行监控埋点，记录关键指标
- **幂等性约束**: 必须确保持久化操作的幂等性，防止重复提交
- **数据校验约束**: 持久化前必须进行数据完整性和有效性校验
- **日志记录约束**: 必须记录持久化操作的关键日志，便于问题排查

#### 持久化转换映射关系

| ExpressModel 字段 | 持久化对象字段 | 转换方法 |
|------------------|--------------|---------|
| orderBusinessIdentity | businessIdentity | toOrderBusinessIdentity |
| orderNo | orderNo | 直接赋值 |
| customOrderNo | customOrderNo | 直接赋值 |
| parentOrderNo | parentOrderNo | 直接赋值 |
| orderType | orderType | getCode() |
| orderSubType | orderSubType | getCode() |
| orderUsage | orderUsage | 直接赋值 |
| orderStatus | orderStatus | getOrderStatus().getCode() |
| customStatus | orderStatusCustom | 直接赋值 |
| executedStatus | orderExtendStatus | 直接赋值 |
| initiatorType | initiatorType | getCode() |
| operator | operator | 直接赋值 |
| operateTime | operateTime | 直接赋值 |
| remark | remark | 直接赋值 |
| extendProps | extendProps | 直接赋值 |
| customer | customer | toCustomerFacade |
| channel | channel | toChannelFacade |
| productDelegate.products | products | toProductFacades |
| consignor | consignor | toConsignorFacade |
| consignee | consignee | toConsigneeFacade |
| cargoDelegate.cargoList | cargos | toCargoFacades |
| goodsDelegate.goodsList | goodsList | toGoodsFacades |
| shipment | shipment | toShipmentFacade |
| finance | finance | toFinanceFacade |
| promotion | promotion | toPromotionFacade |
| refOrderInfoDelegate | refOrders | toRefOrderFacades |
| refOrderInfoDelegate.originalNo | originalNo | 直接赋值 |
| hiddenMark | hiddenMark | 直接赋值 |
| businessSolution | businessSolutionFacade | toBusinessSolutionFacade |
| agreementDelegate | agreementFacades | toAgreementFacades |
| orderSign | orderSign | 直接赋值 |
| refOrderInfoDelegate.extendProps | extendRefOrder | 直接赋值 |
| returnInfoVo | returnInfoFacade | toReturnInfoFacade |
| fulfillment | fulfillmentFacade | toFulfillmentFacade |
| customs | customsFacade | toCustomsFacade |
| attachments | attachmentFacades | toAttachmentFacades |
| orderWeight | orderWeight | toWeightInfo |
| orderNetWeight | orderNetWeight | toWeightInfo |
| orderVolume | orderVolume | toVolumeInfo |
| discardStatus | discardStatus | getCode() |

#### 持久化处理流程

1. **前置处理**:
   - 商家自计费场景处理
   - 产品映射信息补齐
   - 产品合并处理

2. **扩展点调用**:
   - 根据业务场景选择对应的 `IRepositoryExtension` 实现类
   - 调用扩展点的 `execute` 方法执行持久化逻辑

3. **异常处理**:
   - 捕获持久化异常并进行重试
   - 根据业务场景选择对应的重试策略

4. **后置处理**:
   - 产品拆分处理
   - 防重复锁设置
   - 监控埋点记录

#### 维护建议

- **字段新增约束**: 新增字段时，必须同时修改 `ExpressOrderModel`、`CreateOrderFacadeRequest` 和 `CreateOrderFacadeTranslator`
- **字段类型变更约束**: 修改字段类型时，必须确保转换逻辑兼容新的类型
- **字段删除约束**: 删除字段时，必须确保不影响现有业务逻辑
- **转换逻辑变更约束**: 转换逻辑变更时，必须进行充分的单元测试和集成测试
- **同步更新约束**: 必须保持转换逻辑与领域模型的同步更新
- **扩展点实现约束**: 新增业务场景时，必须实现对应的 `IRepositoryExtension` 扩展点

### 横向模块开发约束
- **扩展点约束**: 新增横向扩展必须在horz-extension包下实现
- **通用性约束**: 横向扩展必须支持跨垂直业务场景
- **接口约束**: 必须实现对应的扩展接口
- **注解约束**: 必须使用@Extension注解标注扩展点
- **防腐层约束**: 外部系统集成必须通过ACL层实现
- **缓存约束**: 横向扩展必须遵循统一的缓存策略
- **监控约束**: 横向扩展必须集成UMP监控埋点
- **分库分表约束**: 数据访问必须支持分库分表场景

## 📁 文件路径映射

### 源码文件路径
- **Controller**: `jdl-oms-express-web/*/src/main/java/cn/jdl/oms/express/*/controller/`
- **Service**: `jdl-oms-express-application/src/main/java/cn/jdl/oms/express/application/service/`
- **Repository**: `jdl-oms-express-domain/jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/repo/`
- **Entity**: `jdl-oms-express-domain/jdl-oms-express-domain-model/src/main/java/cn/jdl/oms/express/domain/model/`

### 配置文件路径
- **Spring配置**: `jdl-oms-express-web/*/src/main/resources/spring/`
- **属性配置**: `jdl-oms-express-web/*/src/main/resources/properties/`
- **Web配置**: `jdl-oms-express-web/*/src/main/webapp/WEB-INF/`

## 📋 配置文件清单

### 应用配置
- **主配置**: `applicationContext.xml`
- **Web配置**: `web.xml`
- **属性配置**: `properties/*/application.properties`

### 中间件配置
- **JSF配置**: `spring/rpc/applicationContext-*.xml`
- **JMQ配置**: `spring/jmq/applicationContext-*.xml`
- **DUCC配置**: `spring/ucc/applicationContext-ducc.xml`
- **UMP配置**: `spring/ump/applicationContext-ump.xml`

### 环境配置
- **开发环境**: `properties/dev/`
- **测试环境**: `properties/test/`
- **预发环境**: `properties/pre/`
- **生产环境**: `properties/online/`
- **AB环境**: `properties/ab/`
- **YC环境**: `properties/yc/`

## 🌍 环境变量清单

### JSF环境变量
- **JSF_REGISTRY_INDEX**: JSF注册中心地址 (如: test.i.jsf.jd.local)
- **JSF_SERVER_PROTOCOL**: JSF服务协议 (默认: jsf)
- **JSF_SERVER_THREADS**: JSF服务线程数 (默认: 1200)
- **JSF_SERVER_CONCURRENTS**: JSF服务并发数 (默认: 200)

### JMQ环境变量
- **JMQ_ADDRESS**: JMQ服务地址 (如: jmq-testcluster.jd.local:50088)
- **JMQ_PASSWORD**: JMQ密码 (如: 8039AD67)
- **JMQ_PRODUCER_APP**: JMQ应用标识 (如: JDLOMSEXPRESS)
- **JMQ_USERNAME**: JMQ用户名 (如: JDLOMSEXPRESS)
- **JMQ_ENVIRONMENT**: JMQ环境标识 (如: TEST)

### DUCC环境变量
- **DUCC_APPLICATION**: DUCC应用名称
- **DUCC_TOKEN**: DUCC认证Token
- **DUCC_URI**: DUCC服务地址
- **DUCC_NAMESPACE**: DUCC命名空间

### UMP环境变量
- **UMP_MONITOR_JVM_KEY**: UMP JVM监控Key
- **UMP_MONITOR_HEART_BEAT_KEY**: UMP心跳监控Key
- **MONITOR_DOMAIN**: 监控业务域 (如: express)
- **MONITOR_ENV**: 监控环境 (如: test)

### 应用环境变量
- **APP_NAME**: 应用名称 (如: express.oms.jdl.cn)
- **APP_ID**: 应用ID (如: express.oms.jdl.cn)
- **PROFILES_ACTIVE**: 激活的环境配置 (如: dev, test, pre, online)

### 知识库说明
- **扩展信息**: 对象的扩展字段(扩展信息),统一命名为extendProps,结构为Map<String, String>,具体实现为HashMap<String, String>
- **扩展信息字段**:扩展信息里的字段不需要定义为明字段,只需要在扩展字段常量类里维护常量说明
- **接单服务新增字段**:读取接单服务入参CreateExpressOrderRequest,并将新增字段通过接单服务入参映射关系,转换到订单模型
- **接单数据存储**:将订单模型的里的字段数据,通过接单持久化数据对象转换,调用持久化服务进行存储
- **修改数据存储**:将订单模型的里的字段数据,通过修改持久化数据对象转换,调用持久化服务进行存储

## 任务指南

### 新增字段开发指南 TODO 

#### 接单服务新增字段开发指南
- **接单服务数据传输流**:接单服务到数据持久化,对象数据传输流 CreateExpressOrderRequest->ExpressModel->CreateOrderFacadeRequest->PersistOrderRequest
- **分析**: 分析当前项目结构信息,接单服务数据传输流,查找相关的模型类
- **dto新增字段**: 在cn.jdl.oms.express.domain.dto.*中新增字段,新增字段定义需要和接单服务请求入参的里字段保持一致
- **vo新增字段**: 参考现有的dto与vo对象转换映射关系,在cn.jdl.oms.express.domain.vo.*中新增字段,新增字段定义需要和接单服务请求入参的里字段保持一致
- **接单持久化防腐层dto新增字段**: 在cn.jdl.oms.express.domain.infrs.acl.pl.order.*中新增字段,新增字段定义需要和接单服务请求入参的里字段保持一致
- **新增字段约束**: 新增字段定义需要和接单服务请求入参的里字段保持一致
- **新增字段转换**: 分析CreateExpressOrderTranslator类中的转换逻辑,将dto与vo新增的字段参进行字段转换映射
- **新增字段转换约束**: 接单服务请求入参不能直接转换为ExpressModel，必须通过CreateExpressOrderTranslator.translator方法转换,若新增字段是在值对象里,需要在原有的转换方法里增加对应字段转换逻辑
- **接单持久化防腐层新增字段转换**: 分析CreateOrderFacadeTranslator类中的转换逻辑,将vo与dto新增的字段参进行字段转换映射
- **新增字段转换约束**: 订单模型ExpressModel转换为接单持久化防腐层对象CreateOrderFacadeRequest，必须通过CreateOrderFacadeTranslator方法转换,若新增字段是在值对象里,需要在原有的转换方法里增加对应字段转换逻辑
- **接单持久化服务新增字段转换**: 分析CreateOrderRpcTranslator类中的转换逻辑,将接单持久化防腐层对象CreateOrderFacadeRequest转换为接单持久化服务请求入参对象
- **新增字段转换约束**: 新增字段时，可以参考现有的转换映射关系，了解不同类型字段的转换方法。例如：简单字段：直接赋值;复杂对象：使用专门的转换方法（如customerOf、consignorOf等）;集合对象：使用专门的转换方法（如productInfosOf、cargoInfosOf等）
  对于枚举类型，使用枚举类的of方法进行转换
  对于枚举类型，使用枚举类的of方法进行转换
  对于集合类型，使用stream().map().collect()或循环遍历进行转换
  对于嵌套对象，递归调用相应的转换方法

### 新增JSF接口开发指南

#### 1. 接口信息配置

##### 基本信息 📋
```yaml
接口全限定名: {interfaceClass}
方法名: {methodName}
入参类型: {requestType}
出参类型: {responseType}
外部系统: {externalSystem}
业务域: {businessDomain}
```

##### 自动生成配置 🔧
- **JSF Consumer ID**: `{serviceId}` (接口类名转小写驼峰)
- **配置前缀**: `{configPrefix}` (接口类名转小写点分隔)
- **包路径**: `cn.jdl.oms.express.domain.infrs.acl.rpc.{businessDomain}`

##### 类存在性检查 ⚠️
检查以下类是否存在，如存在则在现有类最后添加新方法：
- `I{功能名}Service` - 防腐层接口
- `{功能名}Service` - 防腐层实现
- `{功能名}Facade` - 业务防腐层
- `{功能名}Translator` - 数据转换器

#### 2. 快速检查清单

##### 配置检查
- [ ] 类存在性检查
- [ ] 参数转换逻辑设计（固定值/环境固定值/领域模型取值）
- [ ] Maven依赖
- [ ] JSF配置（main/worker/monitor）
- [ ] 环境属性配置（main: test/pre/ab/yc/online, worker/monitor: test/pre/online）

#### 3. Maven依赖管理

##### 父POM版本管理
```xml
<!-- {外部系统}接口 -->
<{外部系统}.{功能}.version>0.3.1-SNAPSHOT</{外部系统}.{功能}.version>
```

##### 子模块依赖配置
**domain/pom.xml (dependencyManagement):**
```xml
<dependency>
    <groupId>{groupId}</groupId>
    <artifactId>{artifactId}</artifactId>
    <version>${外部系统.功能.version}</version>
</dependency>
```

**domain-infrastructure/pom.xml:**
```xml
<dependency>
    <groupId>{groupId}</groupId>
    <artifactId>{artifactId}</artifactId>
</dependency>
```

#### 4. JSF配置

##### applicationContext-consumer.xml
```xml
<!-- {接口功能描述} -->
<jsf:consumer id="{serviceId}" 
              interface="{interfaceClass}"
              protocol="jsf" 
              serialization="hessian" 
              filter="consumerChainFilter"
              alias="${jsf.{configPrefix}.alias}" 
              timeout="${jsf.{configPrefix}.timeout}"
              retries="${jsf.{configPrefix}.retries}">
    <jsf:parameter key="token" value="${jsf.{configPrefix}.token}"/>
</jsf:consumer>
```

#### 5. 环境属性配置

##### 环境配置清单
| 环境 | alias | 模块支持 |
|------|-------|----------|
| 测试 | m-ztcs | main/worker/monitor |
| 预发 | LF_JPOS_CLOUD | main/worker/monitor |
| AB | LF_JPOS_CLOUD | main |
| 压测 | LF_JPOS_CLOUD | main |
| 生产 | LF_JPOS_CLOUD | main/worker/monitor |

##### 配置格式
```properties
# {接口功能描述}
jsf.{configPrefix}.alias={alias}
jsf.{configPrefix}.timeout=1000
jsf.{configPrefix}.retries=0
jsf.{configPrefix}.token={token}

# 环境相关固定值（可选）
jsf.{configPrefix}.appId={appId}
jsf.{configPrefix}.appSecret={appSecret}
jsf.{configPrefix}.env={env}
jsf.{configPrefix}.bizType={bizType}
jsf.{configPrefix}.remark={remark}
```

#### 6. 代码实现

##### 6.1 防腐层接口
```java
// cn.jdl.oms.express.domain.infrs.acl.rpc.{businessDomain}.I{功能名}Service
public interface I{功能名}Service {
    /**
     * {接口功能描述}
     * @param request {requestType} 请求参数
     * @return RpcResult<{responseType}> 响应结果
     */
    RpcResult<{responseType}> {methodName}({requestType} request);
}
```

##### 6.2 防腐层实现
```java
// cn.jdl.oms.express.horz.infrs.acl.rpc.{businessDomain}.{功能名}Service
@Service
public class {功能名}Service implements I{功能名}Service {
    @Resource
    private {interfaceClass} {serviceId};
    
    @Override
    public RpcResult<{responseType}> {methodName}({requestType} request) {
        CallerInfo callerInfo = Profiler.registerInfo("cn.jdl.oms.express.horz.infrs.acl.rpc.{businessDomain}.{功能名}Service.{methodName}");
        try {
            LOGGER.info("{接口功能描述}入参:{}", JSONUtils.beanToJSONDefaultLazy(request));
            {responseType} response = {serviceId}.{methodName}(request);
            LOGGER.info("{接口功能描述}结果:{}", JSONUtils.beanToJSONDefaultLazy(response));
            return RpcResult.ofSuccess(response);
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.info("{接口功能描述}异常", e);
            throw e;
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }
}
```

##### 6.3 业务防腐层
```java
// cn.jdl.oms.express.domain.infrs.acl.facade.{businessDomain}.{功能名}Facade
@Component
public class {功能名}Facade {
    @Resource
    private I{功能名}Service {功能名}Service;
    @Resource
    private {功能名}Translator {功能名}Translator;
    
    public {返回类型} {业务方法名}({业务参数类型} {业务参数}) {
        try {
            {requestType} request = {功能名}Translator.to{requestType}({业务参数});
            RpcResult<{responseType}> rpcResult = {功能名}Service.{methodName}(request);
            
            if (!rpcResult.isSuccess()) {
                LOGGER.warn("{接口功能描述}调用失败");
                return {失败返回值};
            }
            
            {responseType} response = rpcResult.getResult();
            if (response != null && response.isSuccess() && {数据有效性检查}) {
                return {成功返回值};
            } else {
                LOGGER.warn("{接口功能描述}返回数据为空或失败");
                return {失败返回值};
            }
        } catch (Exception e) {
            LOGGER.error("{接口功能描述}异常", e);
            throw new InfrastructureException({错误码}, e);
        }
    }
}
```

##### 6.4 数据转换器
```java
// cn.jdl.oms.express.domain.infrs.acl.pl.{businessDomain}.{功能名}Translator
@Component
public class {功能名}Translator {
    
    // 区分环境固定值配置（可选）
    @Value("${jsf.{configPrefix}.appId:}")
    private String APP_ID;
    @Value("${jsf.{configPrefix}.appSecret}")
    private String APP_SECRET;
    
    // 固定值常量
    private static final String VERSION = "1.0";
    private static final String CURRENCY = "CNY";
    
    public {requestType} to{requestType}({业务参数类型} {业务参数}) {
        {requestType} rpcRequest = new {requestType}();
        
        // 1. 区分环境固定值
        if (StringUtils.hasText(APP_ID)) {
            rpcRequest.setAppId(APP_ID);
        }
        rpcRequest.setAppSecret(APP_SECRET);
        
        // 2. 固定值
        rpcRequest.setVersion(VERSION);
        rpcRequest.setCurrency(CURRENCY);
        
        // 3. 从订单领域模型取值
        rpcRequest.setPin({业务参数}.getOperator());
        if ({业务参数}.getCustomer() != null) {
            rpcRequest.setCustomerId({业务参数}.getCustomer().getCustomerId());
        }
        
        return rpcRequest;
    }
}
```

#### 7. 参数转换逻辑

##### 7.1 三种参数类型
1. **固定值**: 使用`static final`常量，不随环境变化
2. **区分环境固定值**: 通过`@Value`注入，配置在properties中
3. **从订单领域模型取值**: 从ExpressOrderModel中动态获取，需null检查

##### 7.2 配置映射规范
- 环境相关配置: `jsf.{configPrefix}.{fieldName}`
- null值处理: 从订单领域模型取值时必须进行null检查
- 环境隔离: 确保不同环境使用不同的配置值

#### 8. 使用步骤

1. **填写接口基本信息** - 确定接口全限定名、方法名、入参出参类型等
2. **生成配置信息** - 生成serviceId、configPrefix等配置信息
3. **检查类是否存在** - 执行类存在性检查
4. **设计参数转换逻辑** - 确定三种参数类型的映射关系
5. **Maven依赖管理** - 添加版本号定义和依赖配置
6. **JSF配置** - 在main/worker/monitor模块中添加JSF消费者配置
7. **环境属性配置** - 配置各环境属性
8. **代码实现** - 根据类存在情况选择添加方式
9. **根据业务需求调整具体实现**

#### 9. 检查命令

##### 类存在性检查
```bash
find . -name "I{功能名}Service.java" -path "*/domain-infrastructure/*"
find . -name "{功能名}Service.java" -path "*/horz-infrastructure/*"
find . -name "{功能名}Facade.java" -path "*/domain-infrastructure/*"
find . -name "{功能名}Translator.java" -path "*/domain-infrastructure/*"
```

#### 10. 命名规范

| 类型 | 命名规则 | 示例 |
|------|----------|------|
| 接口 | `I{功能名}Service` | `IAccountBookService` |
| 实现类 | `{功能名}Service` | `AccountBookService` |
| Facade | `{功能名}Facade` | `AccountBookFacade` |
| 转换器 | `{功能名}Translator` | `AccountBookTranslator` |
| 配置前缀 | `{外部系统}.{功能名}` | `bms.account.risk.facade.service` |

#### 11. 注意事项

1. **Maven依赖管理**: 版本号必须在父POM中统一管理，domain-infrastructure模块无需维护版本号
2. **环境配置完整性**: main模块支持test/pre/ab/yc/online，worker/monitor模块支持test/pre/online
3. **类存在性检查**: 添加新接口前必须检查相关类是否已存在
4. **方法添加位置**: 如果类已存在，新方法必须添加到现有类的最后
5. **参数转换逻辑**: 明确区分三种参数类型，环境相关配置通过@Value注入
6. **环境相关配置**: 只有当需求明确指定某参数需要区分环境时，才需要配置环境相关固定值
7. **AB和压测环境处理**: 如果需求中未提及，则与预发环境保持一致

### 新增Batrix能力指南（FlowNode- Ability- Extension） TODO
- 同步/异步能力
### 新增产品（主增/是否支持修改） 

#### 1. 新主产品支持与数据同步

##### 功能描述
为OMS系统添加新的主产品类型支持，包括产品枚举定义、外单标识映射和数据同步机制。

##### 实现要点
- **产品枚举扩展**: 在`ProductEnum`中添加新产品类型
- **外单映射配置**: 在`WaybillInfoMappingUtil`中添加产品标识映射逻辑
- **数据同步机制**: 确保新产品在各个数据流转环节的正确识别

##### 关键代码模式
```java
// 1. 产品枚举定义
NEW_PRODUCT("ed-m-xxxx", "产品名称", 标识位置, "标识值"),

// 2. 外单映射逻辑
if (ProductEnum.NEW_PRODUCT.getSign().equals(String.valueOf(markUtil.charAt(31)))) {
    mainProduct.setProductName(ProductEnum.NEW_PRODUCT.getDesc());
    mainProduct.setProductNo(ProductEnum.NEW_PRODUCT.getCode());
}

// 3. 静态注册表更新（自动处理）
private static final Map<String, ProductEnum> registry = new HashMap();
static {
    Iterator iterator = EnumSet.allOf(ProductEnum.class).iterator();
    while (iterator.hasNext()) {
        ProductEnum typeEnum = (ProductEnum) iterator.next();
        registry.put(typeEnum.getCode(), typeEnum);
    }
}
```

##### 涉及文件
- `jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java`
- `jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java`
#### 3. 新增增值服务支持

##### 功能描述
为OMS系统添加新的增值服务类型支持，包括增值服务枚举定义、修改权限控制和业务逻辑集成。

##### 实现要点
- **增值服务枚举扩展**: 在[`AddOnProductEnum`](jdl-oms-express-domain/jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/AddOnProductEnum.java:236)中添加新增值服务类型
- **修改权限配置**: 通过[`getAllowedModifiedProduct()`](jdl-oms-express-domain/jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/AddOnProductEnum.java:388)方法控制是否支持揽收后修改
- **产品编码规范**: 快运产品使用fr-a-xxxx格式，快递产品使用ed-a-xxxx格式

##### 关键代码模式
```java
// 1. 增值服务枚举定义
FR_WAIT_SELLER_VERIFICATION("fr-a-0089", "等待商家验货-快运", null),
EXPRESS__WAIT_SELLER_VERIFICATION("ed-a-0107", "等待商家验货-快递", null),

// 2. 修改权限控制（如需支持修改，需在此方法中添加）
public static List<AddOnProductEnum> getAllowedModifiedProduct(){
    return Arrays.asList(JDL_COD_TOC, LL_ZZ_DSHK, READDRESS, BOOK_DELIVERY, 
                        CC_MD_COD_TOC, EXPRESS_NIGHT_COLLECTION,
                        CARBON_EMISSION_CALCULATION_FREIGHT, CARBON_EMISSION_CALCULATION_EXPRESS, 
                        ACTIVATION_CHECK, EXPRESS_CONTACT_DIRECTLY);
}

// 3. 产品编码获取
public static Set<String> getAllowedModifiedProductCode() {
    return getAllowedModifiedProduct().stream()
           .map(AddOnProductEnum::getCode)
           .collect(Collectors.toSet());
}
```

##### 涉及文件
- [`jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/AddOnProductEnum.java`](jdl-oms-express-domain/jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/AddOnProductEnum.java)

##### 示例：等待商家验货增值服务
基于commit 5fe075c9738933a2727c00ded14c4962a4644002新增的增值服务：
- **快运版本**: `FR_WAIT_SELLER_VERIFICATION("fr-a-0089", "等待商家验货-快运", null)`
- **快递版本**: `EXPRESS__WAIT_SELLER_VERIFICATION("ed-a-0107", "等待商家验货-快递", null)`
- **修改支持**: 当前这两个服务不在[`getAllowedModifiedProduct()`](jdl-oms-express-domain/jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/AddOnProductEnum.java:388)列表中，不支持揽收后修改

#### 2. 修改项识别方法

##### 功能描述
实现订单修改项的精确识别。代码里所有需求项都需要使用changedPropertyDelegate的字段

##### 实现要点
- **变更检测方法**: 在`ChangedPropertyDelegate`中实现各种变更检测方法
- **字段级变更**: 提供细粒度的字段变更检测能力

##### 关键代码模式
```java

// 通用变更检测模式
public boolean specificFieldHaveChange() {
    if (changedPropertyMap == null) {
        return false;
    }
    return changedPropertyMap.get(ModifyItemConfigEnum.SPECIFIC_FIELD.getCode()) != null;
}
```

##### 涉及文件
- `jdl-oms-express-domain-model/src/main/java/cn/jdl/oms/express/domain/vo/modify/ChangedPropertyDelegate.java`

#### 3. 修改规则守则

##### 功能描述
基于产品类型和订单状态实现修改权限控制，确保业务规则的正确执行。

##### 实现要点
- **状态检查**: 基于订单状态（如揽收前后）进行修改权限判断
- **产品类型限制**: 针对特定产品类型实施修改限制
- **异常处理**: 提供清晰的业务异常信息和错误提示

##### 关键代码模式
```java

// 通用业务规则检查模式
private void validateBusinessRule(ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
    // 1. 状态检查
    if (!isValidStatus(orderModel.getOrderSnapshot().getOrderStatus())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前状态不允许修改");
    }
    
    // 2. 产品类型检查
    if (isRestrictedProduct(orderModel.getProductDelegate().getMajorProductNo())) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("当前产品类型不允许修改");
    }
    
    // 3. 字段变更检查
    if (hasRestrictedFieldChange(changedPropertyDelegate)) {
        throw new BusinessDomainException(ERROR_CODE).withCustom("不允许修改限制字段");
    }
}
```

##### 涉及文件
- `jdl-oms-express-horz-extension/src/main/java/cn/jdl/oms/express/horz/ext/white/ModifyWhiteExtension.java`

#### 4. 不允许使用e卡

##### 功能描述
基于产品类型实现支付方式的限制，如禁用E卡支付等。

##### 实现要点
- **支付限制枚举**: 在`ECardDisableReasonEnum`中定义支付限制原因
- **支付校验逻辑**: 在询价和台账写入pos台账过程中对waybillsign的第一位赋值为0不支持e卡
- **错误信息提示**: 提供明确的支付限制原因说明

##### 关键代码模式
1. e卡不支持原因
```java
PRODUCT_PAYMENT_DISABLE("编码", "特定产品不支持特定支付方式"),
```

2. 不允许e卡支付参考实现代码
```
 if (valOrderModel.getPromotion() != null && !CollectionUtils.isEmpty(valOrderModel.getPromotion().getActivities())) {
            for (Activity activity : valOrderModel.getPromotion().getActivities()) {
                if ("GRADUATION_SEND".equals(activity.getActivityNo())) {
                    LOGGER.info("waybillSignZeroBit,不支持E卡，活动 ");
                    return ECardDisableReasonEnum.ACTIVITY_GRADUATION_SEND;
                }
            }
        }
```

##### 涉及文件
- `jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java`
- 各种OrderBankFacadeTranslator类

#### 5. 消息类字段扩展

##### 功能描述
在对外消息JMQ中增加新的字段信息，支持附件信息等扩展数据的传递。

##### 实现要点
- **消息结构扩展**: 在接单消息类DTO中添加新字段
- **数据转换逻辑**: 在消息转换器中处理新字段的映射
- **向下兼容**: 确保新字段的添加不影响现有消息处理逻辑

##### 关键代码模式和步骤
1. 查找消息类

   | MQ Topic | 消息类 | 转换类 | 说明 |
   |----------|--------|--------|------|
   | `EXPRESS_ORDER_CREATE_NOTICE` | `CreateOrderNotice` | `CreateOrderNoticeTranslator` | 订单接单成功通知 |
   | `EXPRESS_ORDER_STATUS` | `OrderStatusNotifyMessageDto` | `OrderStatusNotifyDataDtoTranslator` | 订单状态变更通知 |
   | `EXPRESS_ORDER_DATA` | `OrderDataFlowDto` | `OrderDataNotifyTranslator` | 订单数据流水记录 |
   | `EXPRESS_ORDER_DATA_UPDATE_NOTICE` | `OrderDataUpdateDto` | `OrderDataNotifyTranslator` | 订单数据变更通知 |

- 用消息mq从上面表格中获取对应的消息发送方法，获取消息类
- 在消息类中新增对应的字段
- 在转换类里新增字段转换规则

##### 涉及文件
- 各种Translator类
- 消息类


##### 3. 数据转换模式
```java
// 使用Optional处理空值
Optional.ofNullable(source.getField()).ifPresent(field -> {
    target.setField(convertField(field));
});

// 集合转换
List<TargetDto> targetList = sourceList.stream()
    .map(this::convertItem)
    .collect(Collectors.toList());
```

##### 4. 业务校验模式
```java
// 统一异常处理
if (!isValid(condition)) {
    throw new BusinessDomainException(UnifiedErrorSpec.Category.ERROR_TYPE)
        .withCustom("具体错误信息");
}

// 日志记录
LOGGER.error("业务校验失败, orderNo: {}, reason: {}", orderNo, reason);

```

### 新增揽收方式流程

**基本功能**

1. **新增枚举值**
   - 在 `cn.jdl.oms.express.domain.spec.dict.PickupTypeEnum` 枚举类中新增枚举值

2. **处理数据同步**
   - 参考代码位置：`cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil#buildShipmentInfo`
   ```java
   if ('1' == markUtil.charAt(71)) {
       shipmentInfoDto.setPickupType(PickupTypeEnum.ON_SITE_PICK);
   } else if ('2' == markUtil.charAt(71)) {
       shipmentInfoDto.setPickupType(PickupTypeEnum.SELF_DELIVERY);
   } else if ('3' == markUtil.charAt(71)) {
       shipmentInfoDto.setPickupType(PickupTypeEnum.ON_SITE_PICK_CREATE_ORDER);
   } else if ('5' == markUtil.charAt(71)) {
       shipmentInfoDto.setPickupType(PickupTypeEnum.SELF_DELIVERY_DMS);
   } else if ('0' == markUtil.charAt(71)) {
       // TODO 目前只有冷链用，字段为标准字段，后续快快有诉求可以放开
       if (snapShotModel.isCCB2B()) {
           shipmentInfoDto.setPickupType(PickupTypeEnum.NO_PICKUP);
       }
   }
   ```

**可选功能**

1. **接单流程卡控**
   - 接单流程基本信息校验：如果揽收方式是新增枚举值，始发站点编码不能为空
   - 参考代码位置：`cn.jdl.oms.express.domain.ability.basic.CreateBasicInfoAbility#shipmentValid(ExpressOrderModel model)`
   ```java
   if (PickupTypeEnum.SELF_DELIVERY_DMS == shipment.getPickupType() && StringUtils.isBlank(shipment.getStartCenterNo())){
       LOGGER.error("接单基本信息校验活动能力开始执行,当取件揽收方式为直送分拣中心时，始发分拣中心id字段不能为空");
       throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
               .withCustom("取件揽收方式为直送分拣中心时，始发分拣中心id字段不能为空");
   }
   ```

   **注意事项：**
   - 始发分拣中心id `shipment.getStartCenterNo()` 需要替换成始发站点编码 `shipment.getStartStationNo()`
   - 可能需要import枚举类 `PickupTypeEnum`
   - 如果参考代码块未出现在 `shipmentValid` 方法中，则增加到方法末尾，否则在参考代码块后面增加

2. **修改流程卡控**
   - 修改流程基本信息校验：如果揽收方式是新增枚举值，始发站点编码不能为空
   - 参考代码位置：`cn.jdl.oms.express.domain.ability.basic.ModifyBasicInfoAbility#shipmentValid(ExpressOrderModel model)`
   ```java
   if (shipment.getPickupType() != null
           && PickupTypeEnum.SELF_DELIVERY.getCode().equals(shipment.getPickupType().getCode())
           && StringUtils.isBlank(shipment.getStartStationNo())) {
       LOGGER.error("订单修改信息校验活动能力开始执行,揽派方式是自送则揽件站点不能为空");
       throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
               .withCustom("揽派方式是自送则揽件站点不能为空");
   }
   ```

   **注意事项：**
   - 可能需要import枚举类 `PickupTypeEnum`
   - 如果参考代码块未出现在 `shipmentValid` 方法中，则增加到方法末尾，否则在参考代码块后面增加

### 新增派送方式流程

**基本功能**

1. **新增枚举值**
   - 在 `cn.jdl.oms.express.domain.spec.dict.DeliveryTypeEnum` 枚举类中新增枚举值

2. **处理数据同步**
   - 参考代码位置：`cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.waybill.WaybillInfoMappingUtil#buildShipmentInfo`
   ```java
   if ("1".equals(String.valueOf(markUtil.charAt(79)))) {
       shipmentInfoDto.setDeliveryType(DeliveryTypeEnum.TO_DOOR);
   } else if ("2".equals(String.valueOf(markUtil.charAt(79)))) {
       shipmentInfoDto.setDeliveryType(DeliveryTypeEnum.SELF_PICKUP);
   }
   ```

**可选功能**

1. **接单流程卡控**
   - 接单流程基本信息校验：如果派送方式是新增枚举值，目的站点编码不能为空
   - 参考代码位置：`cn.jdl.oms.express.domain.ability.basic.CreateBasicInfoAbility#shipmentValid(ExpressOrderModel model)`
   ```java
   if (DeliveryTypeEnum.DMS_DELIVERY == shipment.getDeliveryType() && StringUtils.isBlank(shipment.getEndStationNo())){
       LOGGER.error("接单基本信息校验活动能力开始执行,当派送方式为分拣自提时，目的站点必填");
       throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
               .withCustom("接单基本信息校验活动能力开始执行,当派送方式为分拣自提时，目的站点必填");
   }
   ```

   **注意事项：**
   - 可能需要import枚举类 `DeliveryTypeEnum`
   - 如果参考代码块未出现在 `shipmentValid` 方法中，则增加到方法末尾，否则在参考代码块后面增加

2. **修改流程卡控**
   - 修改流程基本信息校验：如果派送方式是新增枚举值，目的站点编码不能为空
   - 参考代码位置：`cn.jdl.oms.express.domain.ability.basic.ModifyBasicInfoAbility#shipmentValid(ExpressOrderModel model)`
   ```java
   if (DeliveryTypeEnum.DMS_DELIVERY == shipment.getDeliveryType() && StringUtils.isBlank(shipment.getEndStationNo())){
       LOGGER.error("修改基本信息校验活动能力开始执行,当派送方式为分拣自提时，目的站点必填");
       throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.BASIC_INFO_VALIDATE_FAIL)
               .withCustom("修改基本信息校验活动能力开始执行,当派送方式为分拣自提时，目的站点必填");
   }
   ```

   **注意事项：**
   - 可能需要import枚举类 `DeliveryTypeEnum`
   - 如果参考代码块未出现在 `shipmentValid` 方法中，则增加到方法末尾，否则在参考代码块后面增加

## 📊 生成信息

- **文档生成时间**: 2025-09-03
- **项目版本**: 1.0.0-SNAPSHOT
- **分析工具**: Init-Project工作流完整指令
- **文档状态**: 完整项目信息文档
- **需要人工补充的信息点**: 
  - 具体的业务流程图
  - 详细的API接口文档
  - 性能指标和监控数据
  - 部署和运维相关信息

---

**注意**: 本文档基于代码分析生成，建议结合业务实际情况进行验证和补充。
