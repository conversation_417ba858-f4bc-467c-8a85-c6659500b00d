package cn.jdl.oms.express.uep.infrs.acl.pl.enquiry;

import cn.jdl.oms.express.domain.annotation.Translator;
import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.dto.FinanceDetailInfoDto;
import cn.jdl.oms.express.domain.dto.FinanceInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.EnquiryMapper;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingEnquiryFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.enquiry.billing.BillingSplitFacadeRequest;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.AddOnProductEnum;
import cn.jdl.oms.express.domain.spec.dict.ChargingSourceEnum;
import cn.jdl.oms.express.domain.spec.dict.ContextInfoEnum;
import cn.jdl.oms.express.domain.spec.model.IDeduction;
import cn.jdl.oms.express.domain.vo.Address;
import cn.jdl.oms.express.domain.vo.Consignee;
import cn.jdl.oms.express.domain.vo.Consignor;
import cn.jdl.oms.express.domain.vo.CostInfo;
import cn.jdl.oms.express.domain.vo.Customer;
import cn.jdl.oms.express.domain.vo.Deduction;
import cn.jdl.oms.express.domain.vo.DeductionDelegate;
import cn.jdl.oms.express.domain.vo.Finance;
import cn.jdl.oms.express.domain.vo.FinanceDetail;
import cn.jdl.oms.express.domain.vo.Goods;
import cn.jdl.oms.express.domain.vo.Product;
import cn.jdl.oms.express.shared.common.constant.EnquiryConstants;
import cn.jdl.oms.express.shared.common.constant.OrderConstants;
import cn.jdl.oms.express.shared.common.dict.DeductionEnum;
import cn.jdl.oms.express.shared.common.dict.SystemSubCallerEnum;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.jd.lbs.product.inquiry.dto.request.ProductParam;
import com.jd.lbs.product.inquiry.dto.request.StandardProductDetail;
import com.jd.lbs.product.inquiry.dto.response.ProductInquiryFeeInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Translator
public class UEPC2BEnquiryFacadeTranslator {

    /**
     * 单据状态 默认传0
     */
    private static final int DEFAULT_ORDER_STATUS = 0;

    private static final String INSPECTION_TYPE = "inspectionType";

    /**
     * 计费询价防腐层请求数据转换
     */
    public BillingSplitFacadeRequest toBillingSplitFacadeRequest(ExpressOrderContext context) {
        ExpressOrderModel orderModel = context.getOrderModel();
        BillingSplitFacadeRequest facadeRequest = new BillingSplitFacadeRequest();
        // 运单号
        facadeRequest.setRefOrderFacade(this.toRefOrderFacade(orderModel));
        // 费用明细
        facadeRequest.setFeeInfos((List<ProductInquiryFeeInfo>) context.getExtInfo(ContextInfoEnum.ENQUIRY_FEE_INFOS.getCode()));
        // 抵扣信息
        List<BillingSplitFacadeRequest.DeductionFacade> deductionFacades = this.toDeductionFacades(orderModel, facadeRequest);
        facadeRequest.setDeductionFacades(deductionFacades);
        return facadeRequest;
    }

    /**
     * POP抵扣信息转换
     * @param orderModel
     * @return
     */
    private List<BillingSplitFacadeRequest.DeductionFacade> toDeductionFacades(ExpressOrderModel orderModel, BillingSplitFacadeRequest facadeRequest) {
        List<Deduction> deductions = (List<Deduction>) Optional.ofNullable(orderModel.getFinance())
                .map(Finance::getDeductionDelegate)
                .map(DeductionDelegate::getDeductions)
                .orElse(null);

        if (CollectionUtils.isEmpty(deductions)) {
            return null;
        }

        List<BillingSplitFacadeRequest.DeductionFacade> deductionFacades = new ArrayList<>();

        if (StringUtils.isBlank(deductions.get(0).getCostNo()) || StringUtils.isBlank(deductions.get(0).getProductNo())) {
            // 兼容逻辑 如果dedutionInfo里没有cost
            List<BillingSplitFacadeRequest.DeductionFacade> freightDeductions = new LinkedList<>();
            List<BillingSplitFacadeRequest.DeductionFacade> consumableDeductions = new LinkedList<>();
            for (Deduction deduction : deductions) {
                BillingSplitFacadeRequest.DeductionFacade deductionFacade = new BillingSplitFacadeRequest.DeductionFacade();
                if (DeductionEnum.FREIGHT_DEDUCTION.getDeductionNo().equals(deduction.getDeductionNo())) {
                    // 基础运费抵扣
                    deductionFacade = new BillingSplitFacadeRequest.DeductionFacade();
                    deductionFacade.setDeductionAmount(deduction.getDeductionAmount().getAmount());
                    // 添加到列表
                    deductionFacades.add(deductionFacade);
                    freightDeductions.add(deductionFacade);
                } else if (DeductionEnum.CONSUMABLE_DEDUCTION.getDeductionNo().equals(deduction.getDeductionNo())) {
                    // 耗材抵扣
                    deductionFacade = new BillingSplitFacadeRequest.DeductionFacade();
                    deductionFacade.setDeductionAmount(deduction.getDeductionAmount().getAmount());
                    // 添加到列表
                    deductionFacades.add(deductionFacade);
                    consumableDeductions.add(deductionFacade);
                }
            }

            // 获取费用编码 基础运费 + 耗材
            for (ProductInquiryFeeInfo feeInfo : facadeRequest.getFeeInfos()) {
                if (CollectionUtils.isNotEmpty(freightDeductions) && DeductionEnum.FREIGHT_DEDUCTION.getCostNos().contains(feeInfo.getCostNo())) {
                    this.complementDeductionCostInfo(freightDeductions, feeInfo);
                } else if (CollectionUtils.isNotEmpty(consumableDeductions) && DeductionEnum.CONSUMABLE_DEDUCTION.getCostNos().contains(feeInfo.getCostNo())) {
                    this.complementDeductionCostInfo(consumableDeductions, feeInfo);
                }
            }

        } else {
            for (Deduction deduction : deductions) {
                deductionFacades.add(this.toDeductionFacade(deduction));
            }
        }
        return deductionFacades;
    }

    /**
     * 抵扣信息转换
     * @param deduction
     * @return
     */
    private BillingSplitFacadeRequest.DeductionFacade toDeductionFacade(Deduction deduction) {
        BillingSplitFacadeRequest.DeductionFacade deductionFacade = new BillingSplitFacadeRequest.DeductionFacade();
        deductionFacade.setCostNo(deduction.getCostNo());
        deductionFacade.setProductNo(deduction.getProductNo());
        if (null != deduction.getDeductionAmount()) {
            deductionFacade.setDeductionAmount(deduction.getDeductionAmount().getAmount());
        }
        return deductionFacade;
    }

    /**
     * 补齐抵扣项中的费用信息
     * @param deductionFacades
     * @param feeInfo
     */
    private void complementDeductionCostInfo(List<BillingSplitFacadeRequest.DeductionFacade> deductionFacades, ProductInquiryFeeInfo feeInfo) {
        for (BillingSplitFacadeRequest.DeductionFacade deductionFacade : deductionFacades) {
            deductionFacade.setCostNo(feeInfo.getCostNo());
            deductionFacade.setProductNo(feeInfo.getProductCode());
        }
    }

    /**
     * 关联单信息 - 运单号
     * @param orderModel
     * @return
     */
    private BillingSplitFacadeRequest.RefOrderFacade toRefOrderFacade(ExpressOrderModel orderModel) {
        BillingSplitFacadeRequest.RefOrderFacade refOrderFacade = new BillingSplitFacadeRequest.RefOrderFacade();
        refOrderFacade.setWaybillNo(orderModel.getOrderSnapshot().getRefOrderInfoDelegate().getWaybillNo());
        return refOrderFacade;
    }

    /**
     * 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequest(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest facadeRequest = new BillingEnquiryFacadeRequest();
        if (orderModel.getOrderBusinessIdentity() != null) {
            facadeRequest.setBusinessUnit(orderModel.getOrderBusinessIdentity().getBusinessUnit());
        }
        facadeRequest.setOrderNo(orderModel.orderNo());
        // 青龙业主编码和青龙业主号名称
        facadeRequest.setCustomerFacadeDto(this.toCustomerFacadeDto(orderModel));
        // 运单号
        facadeRequest.setRefOrderFacadeDto(this.toRefOrderFacadeDto(orderModel));
        // 客户、渠道
        facadeRequest.setChannelFacadeDto(this.toChannelFacadeDto(orderModel));
        // 产品信息  用传过来的订单号查出来有都赋值进去
        facadeRequest.setProductFacadeDtoList(this.toProductFacadeDtos(orderModel));
        // 总重量、总体积
        facadeRequest.setCargoFacadeDto(this.toCargoFacadeDto(orderModel));
        // 发件人信息
        facadeRequest.setConsignorFacadeDto(this.toConsignorFacadeDto(orderModel));
        // 收件人信息
        facadeRequest.setConsigneeFacadeDto(this.toConsigneeFacadeDto(orderModel));
        // 财务相关信息
        facadeRequest.setFinanceFacadeDto(this.toFinanceFacadeDto(orderModel));
        // 设置拓展字段
        facadeRequest.setExtendProps(this.toExtendProps(orderModel));
        return facadeRequest;
    }

    /**
     * 获取询价扩展字段
     * @param orderModel
     * @return
     */
    private Map<String, Object> toExtendProps(ExpressOrderModel orderModel) {
        // 设置扩展字段
        Map<String, Object> extendParam = new HashMap<>();
        // 结算方式
        extendParam.put(EnquiryConstants.SETTLEMENT_TYPE, getSettlementType(orderModel));
        // 单据状态 逆向合并支付场景的原单（取最原始的正向单及最新一次逆向之前的所有逆向单，且只取结算方式为到付现结的单子）询价传2-拒收，其他情况均传0
        // 逆向合并支付场景 会调用 toReverseBillingEnquiryFacadeRequest 这个方法改掉这个值
        extendParam.put(EnquiryConstants.ORDER_STATUS, DEFAULT_ORDER_STATUS);
        // 仓配类型(0：纯配；1：仓配)
        extendParam.put(EnquiryConstants.DISTRIBUTION_TYPE, getDistributionType(orderModel));
        // 温层
        extendParam.put(EnquiryConstants.WARM_LAYER, orderModel.getOrderSnapshot().getShipment().getWarmLayer());
        // 托运单重量
        extendParam.put(EnquiryConstants.T_WEIGHT, orderModel.getEnquiry().getExtendProps(EnquiryConstants.T_WEIGHT));
        // 托运单体积
        extendParam.put(EnquiryConstants.T_VOLUME, orderModel.getEnquiry().getExtendProps(EnquiryConstants.T_VOLUME));
        // 托运单总声明价值
        extendParam.put(EnquiryConstants.TOTAL_DECLARED_VALUE, orderModel.getEnquiry().getExtendProps(EnquiryConstants.TOTAL_DECLARED_VALUE));
        // 商品信息的增值服务
        this.toProductDetails(orderModel, extendParam);
        // orderChannel 赋值 下单的systemSubCaller
        // 和产品确认 无需区分厂直 物流平台全量给即可
        if (orderModel.getOrderSnapshot() != null
                && orderModel.getOrderSnapshot().getChannel() != null
        ) {
            extendParam.put(EnquiryConstants.ORDER_CHANNEL, orderModel.getOrderSnapshot().getChannel().getSystemSubCaller());
        }
        return extendParam;
    }

    /**
     * 商品增值产品明细信息
     * @param orderModel
     * @param extendParam
     */
    private void toProductDetails(ExpressOrderModel orderModel, Map<String, Object> extendParam) {
        List<StandardProductDetail> productDetails = new ArrayList<>();
        // 取快照中的商品信息
        List<Goods> goodsList = (List<Goods>) orderModel.getOrderSnapshot().getGoodsDelegate().getGoodsList();
        if (CollectionUtils.isEmpty(goodsList)) {
            return;
        }

        for (Goods goods : goodsList) {
            if (CollectionUtils.isEmpty(goods.getGoodsProductInfos())) {
                // 无增值服务跳过
                continue;
            }

            List<ProductParam> goodsProductParams = new ArrayList<>();
            for (Product goodsProductInfo : goods.getGoodsProductInfos()) {
                // 转换 productInfo 到 StandardProductDetail 添加到list
                if (AddOnProductEnum.UEP_CHECK_GOODS.getCode().equals(goodsProductInfo.getProductNo())) {
                    ProductParam goodsParam = this.toInspectionGoodsParam(goodsProductInfo);
                    if (null != goodsParam) {
                        goodsProductParams.add(goodsParam);
                    }
                }
            }

            if (CollectionUtils.isEmpty(goodsProductParams)) {
                // 无需要传入的增值服务
                continue;
            }

            StandardProductDetail standardProductDetail = new StandardProductDetail();
            standardProductDetail.setProductParams(goodsProductParams);
            standardProductDetail.setDetailNo(goods.getGoodsNo());
            productDetails.add(standardProductDetail);
        }

        if (CollectionUtils.isEmpty(productDetails)) {
            return;
        }

        extendParam.put(EnquiryConstants.DETAIL_LIST, productDetails);
    }

    /**
     * 验货增值服务询价入参转换
     * @param goodsProduct
     * @return
     */
    private ProductParam toInspectionGoodsParam(Product goodsProduct) {
        if (MapUtils.isEmpty(goodsProduct.getProductAttrs())) {
            return null;
        }

        ProductParam productParam = new ProductParam();

        // 产品编码
        productParam.setProductCode(goodsProduct.getProductNo());

        // 产品要素 订单中心负责将产品要素全部传给计费，计费只收验货项的钱，其余0元
        Set<String> inspectionItems = goodsProduct.getProductAttrs().keySet();
        List<Object[]> inspectionTypes = new ArrayList<>();
        for (String inspectionItem : inspectionItems) {
            // 验货项对应值默认 1
            Object[] inspectElem = new Object[]{inspectionItem, 1};
            inspectionTypes.add(inspectElem);
        }
        Map<String, String> productElem = new HashMap<>();
        productElem.put(INSPECTION_TYPE, JSONUtils.beanToJSONDefault(inspectionTypes));

        productParam.setProductElement(productElem);

        return productParam;
    }

    /**
     * 功能描述:  仓配类型(0：纯配；1：仓配)
     * 接单字段deliveryPattern若为1京仓发货 则该字段赋值1
     * 若为2纯配则该字段赋值0；
     * 接单字段deliveryPattern若为空且systemSubCaller为eclp则该字段赋值为1，
     * 其他情况全部赋值0
     * @param orderModel
     * @return
     */
    private Object getDistributionType(ExpressOrderModel orderModel) {
        String deliveryPattern = orderModel.getOrderSnapshot().getShipment().getDeliveryPattern();
        if (OrderConstants.WAREHOUSE_DELIVERY.equals(deliveryPattern)) {
            return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
        } else if (OrderConstants.PURE_DELIVERY.equals(deliveryPattern)) {
            return EnquiryConstants.DISTRIBUTION_PURE;
        } else if (deliveryPattern == null && SystemSubCallerEnum.ECLP.getCode().equals(orderModel.getOrderSnapshot().getChannel().getSystemSubCaller())) {
            return EnquiryConstants.DISTRIBUTION_WARE_HOUSE;
        }

        return EnquiryConstants.DISTRIBUTION_PURE;
    }


    /**
     * 询价扩展字段 - 结算方式
     * @param orderModel
     * @return
     */
    private Object getSettlementType(ExpressOrderModel orderModel) {
        switch (orderModel.getOrderSnapshot().getFinance().getSettlementType()) {
            case CASH_ON_PICK:
                return EnquiryConstants.SETTLEMENT_CASH_ON_PICK;
            case CASH_ON_DELIVERY:
                return EnquiryConstants.SETTLEMENT_CASH_ON_DELIVERY;
            case MONTHLY_PAYMENT:
                return EnquiryConstants.SETTLEMENT_MONTHLY_PAYMENT;
            default: return null;
        }
    }

    private BillingEnquiryFacadeRequest.CustomerFacadeDto toCustomerFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CustomerFacadeDto customerFacadeDto = new BillingEnquiryFacadeRequest.CustomerFacadeDto();
        Customer customer = orderModel.getOrderSnapshot().getCustomer();
        customerFacadeDto.setAccountNo(customer.getAccountNo());
        return customerFacadeDto;
    }

    /**
     * 关联单信息 - 运单号
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.RefOrderFacadeDto toRefOrderFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.RefOrderFacadeDto refOrderFacadeDto = new BillingEnquiryFacadeRequest.RefOrderFacadeDto();
        refOrderFacadeDto.setWaybillNo(orderModel.getOrderSnapshot().getRefOrderInfoDelegate().getWaybillNo());
        return refOrderFacadeDto;
    }

    /**
     * 产品信息
     * @param orderModel
     * @return
     */
    private List<BillingEnquiryFacadeRequest.ProductFacadeDto> toProductFacadeDtos(ExpressOrderModel orderModel) {
        // 需要从快照中获取产品信息
        return EnquiryMapper.INSTANCE.toProductFacadeDtos((List<Product>) orderModel.getOrderSnapshot().getProductDelegate().getProducts());
    }

    /**
     * 渠道信息
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ChannelFacadeDto toChannelFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ChannelFacadeDto channelFacadeDto = new BillingEnquiryFacadeRequest.ChannelFacadeDto();
        channelFacadeDto.setChannelNo(orderModel.getChannel().getChannelNo());
        return channelFacadeDto;
    }

    /**
     * 收件人信息
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsigneeFacadeDto toConsigneeFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsigneeFacadeDto consigneeFacadeDto = new BillingEnquiryFacadeRequest.ConsigneeFacadeDto();
        Consignee consignee = orderModel.getOrderSnapshot().getConsignee();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = toAddressFacadeDto(consignee.getAddress());
        consigneeFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consigneeFacadeDto;
    }

    /**
     * 发件人信息
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.ConsignorFacadeDto toConsignorFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.ConsignorFacadeDto consignorFacadeDto = new BillingEnquiryFacadeRequest.ConsignorFacadeDto();
        Consignor consignor = orderModel.getOrderSnapshot().getConsignor();
        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = toAddressFacadeDto(consignor.getAddress());
        consignorFacadeDto.setAddressFacadeDto(addressFacadeDto);
        return consignorFacadeDto;
    }

    /**
     * 地址信息转换
     * @param address
     * @return
     */
    BillingEnquiryFacadeRequest.AddressFacadeDto toAddressFacadeDto(Address address) {
        if (null == address) {
            return null;
        }

        BillingEnquiryFacadeRequest.AddressFacadeDto addressFacadeDto = new BillingEnquiryFacadeRequest.AddressFacadeDto();
        // 省市县
        addressFacadeDto.setProvinceNoGis(address.getProvinceNo());
        addressFacadeDto.setCityNoGis(address.getCityNo());
        addressFacadeDto.setCountyNoGis(address.getCountyNo());

        return addressFacadeDto;
    }

    /**
     * 货物信息，总重量、总体积、总数量
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.CargoFacadeDto toCargoFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.CargoFacadeDto cargoFacadeDto = new BillingEnquiryFacadeRequest.CargoFacadeDto();
        // 计费数量(从外单里查出来的货品数量)
        cargoFacadeDto.setTotalCargoQuantity(orderModel.getOrderSnapshot().getCargoDelegate().totalCargoQuantity());
        // 计费体积
        cargoFacadeDto.setTotalCargoVolume(orderModel.getEnquiry().getEnquiryVolume().getValue());
        // 计费重量
        cargoFacadeDto.setTotalCargoWeight(orderModel.getEnquiry().getEnquiryWeight().getValue());
        return cargoFacadeDto;
    }

    /**
     * 财务相关信息
     *
     * @param orderModel
     * @return
     */
    private BillingEnquiryFacadeRequest.FinanceFacadeDto toFinanceFacadeDto(ExpressOrderModel orderModel) {
        BillingEnquiryFacadeRequest.FinanceFacadeDto financeFacadeDto = new BillingEnquiryFacadeRequest.FinanceFacadeDto();
        financeFacadeDto.setEnquireTime(new Date());
        //结算方式 这里在查询外单那里已经补在快照里了,传递给计费询价接口
        financeFacadeDto.setSettlementType(orderModel.getOrderSnapshot().getFinance().getSettlementType());

        // 抵扣信息 只有逆向
        // 下了运费保，抵扣编码是OFC传过来的，所以抵扣信息在当前订单的fiance信息
        //financeFacadeDto.setDeductionInfoDtos(toDeductionInfoDtos((List<Deduction>) orderModel.getFinance().getDeductionDelegate().getDeductions()));
        return financeFacadeDto;
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderContext context, BillingEnquiryFacadeResponse response) {
        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        expressOrderModelCreator.setFinanceInfo(EnquiryMapper.INSTANCE.toFinanceInfoDtoNEW(response.getFinanceFacadeDto()));
        if (null != expressOrderModelCreator.getFinanceInfo()) {
            FinanceInfoDto financeInfoDto = expressOrderModelCreator.getFinanceInfo();
            financeInfoDto.setPayDeadline(orderModel.getFinance().getPayDeadline());

            List<CostInfo> costInfos = orderModel.getOrderSnapshot().getFinance().getCostInfos();
            if (CollectionUtils.isNotEmpty(costInfos)) {
                Map<String, CostInfo> collect = costInfos.stream().collect(Collectors.toMap(CostInfo::getCostNo, Function.identity(), (o1, o2) -> o1));
                // 根据costInfo补齐financeDetail中的向谁收
                if (CollectionUtils.isNotEmpty(financeInfoDto.getFinanceDetailInfos())) {
                    for (FinanceDetailInfoDto detailInfoDto : financeInfoDto.getFinanceDetailInfos()) {
                        CostInfo costInfo = collect.get(detailInfoDto.getCostNo());
                        if (null == costInfo) {
                            detailInfoDto.setChargingSource(ChargingSourceEnum.TO_USER.getCode());
                        } else {
                            detailInfoDto.setChargingSource(costInfo.getChargingSource());
                        }
                    }
                }
                // 根据costInfo补齐抵扣信息中的向谁收
                if (orderModel.getFinance().getDeductionDelegate() != null && orderModel.getFinance().getDeductionDelegate().getDeductions() != null) {
                    for (IDeduction iDeduction : orderModel.getFinance().getDeductionDelegate().getDeductions()) {
                        Deduction deduction = (Deduction) iDeduction;
                        CostInfo costInfo = collect.get(deduction.getCostNo());
                        if (null == costInfo) {
                            deduction.setChargingSource(ChargingSourceEnum.TO_USER.getCode());
                        } else {
                            deduction.setChargingSource(costInfo.getChargingSource());
                        }
                    }
                }
            }
        }
        orderModel.complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

    /**
     * 计费询价防腐层请求数据转换
     */
    public BillingEnquiryFacadeRequest toBillingEnquiryFacadeRequestWithUserDiscount(ExpressOrderModel orderModel, Map<String, FinanceDetail> productNo2Fee) {
        BillingEnquiryFacadeRequest facadeRequest = this.toBillingEnquiryFacadeRequest(orderModel);
        // 更新需要用户折扣的产品的 抵扣后金额
        List<BillingEnquiryFacadeRequest.ProductFacadeDto> filteredProductFacadeDtoList = new ArrayList<>(productNo2Fee.size());
        for (BillingEnquiryFacadeRequest.ProductFacadeDto productFacadeDto : facadeRequest.getProductFacadeDtoList()) {
            FinanceDetail financeDetail = productNo2Fee.get(productFacadeDto.getProductNo());
            if (null != financeDetail && null != financeDetail.getDiscountAmount()) {
                BigDecimal amount = financeDetail.getDiscountAmount().getAmount();
                BigDecimal preAmount = financeDetail.getPreAmount().getAmount();
                productFacadeDto.getProductAttrs().put(EnquiryConstants.DEDUCTION_AMOUNT, String.valueOf(amount));
                if (null != amount && null != preAmount) {
                    BigDecimal discountAmount = preAmount.subtract(amount);
                    productFacadeDto.getProductAttrs().put(EnquiryConstants.SOURCE_DEDUCTION_AMOUNT, String.valueOf(discountAmount));
                }
                productFacadeDto.getProductAttrs().put(EnquiryConstants.SOURCE_DEDUCTION_COST_NO, financeDetail.getCostNo());
                filteredProductFacadeDtoList.add(productFacadeDto);
            }
        }
        Map<String, Object> extendParams = facadeRequest.getExtendProps();
        if (null == extendParams) {
            extendParams = new HashMap<>();
            facadeRequest.setExtendProps(extendParams);
        }
        extendParams.put(EnquiryConstants.SETTLEMENT_TYPE, EnquiryConstants.SETTLEMENT_CASH_ON_PICK);

        facadeRequest.setProductFacadeDtoList(filteredProductFacadeDtoList);
        return facadeRequest;
    }

    /**
     * 补全计费结果信息
     */
    public void complementBillingResult(ExpressOrderContext context, BillingEnquiryFacadeResponse response, List<FinanceDetailInfoDto> financeDetailInfoDtos) {
        ExpressOrderModel orderModel = context.getOrderModel();
        ExpressOrderModelCreator expressOrderModelCreator = new ExpressOrderModelCreator();
        expressOrderModelCreator.setFinanceInfo(EnquiryMapper.INSTANCE.toFinanceInfoDtoNEW(response.getFinanceFacadeDto()));
        if (null != expressOrderModelCreator.getFinanceInfo()) {
            FinanceInfoDto financeInfoDto = expressOrderModelCreator.getFinanceInfo();
            financeInfoDto.setFinanceDetailInfos(financeDetailInfoDtos);
            financeInfoDto.setPayDeadline(orderModel.getFinance().getPayDeadline());

            List<CostInfo> costInfos = orderModel.getOrderSnapshot().getFinance().getCostInfos();
            if (CollectionUtils.isNotEmpty(costInfos)) {
                Map<String, CostInfo> collect = costInfos.stream().collect(Collectors.toMap(CostInfo::getCostNo, Function.identity(), (o1, o2) -> o1));
                // 根据costInfo补齐financeDetail中的向谁收
                if (CollectionUtils.isNotEmpty(financeInfoDto.getFinanceDetailInfos())) {
                    for (FinanceDetailInfoDto detailInfoDto : financeInfoDto.getFinanceDetailInfos()) {
                        CostInfo costInfo = collect.get(detailInfoDto.getCostNo());
                        if (null == costInfo) {
                            detailInfoDto.setChargingSource(ChargingSourceEnum.TO_USER.getCode());
                        } else {
                            detailInfoDto.setChargingSource(costInfo.getChargingSource());
                        }
                    }
                }
                // 根据costInfo补齐抵扣信息中的向谁收
                if (orderModel.getFinance().getDeductionDelegate() != null && orderModel.getFinance().getDeductionDelegate().getDeductions() != null) {
                    for (IDeduction iDeduction : orderModel.getFinance().getDeductionDelegate().getDeductions()) {
                        Deduction deduction = (Deduction) iDeduction;
                        CostInfo costInfo = collect.get(deduction.getCostNo());
                        if (null == costInfo) {
                            deduction.setChargingSource(ChargingSourceEnum.TO_USER.getCode());
                        } else {
                            deduction.setChargingSource(costInfo.getChargingSource());
                        }
                    }
                }
            }
        }
        orderModel.complement().complementFinanceInfo(this, expressOrderModelCreator);
    }

}
