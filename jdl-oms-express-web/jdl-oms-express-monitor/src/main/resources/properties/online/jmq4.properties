#JMQ\u57FA\u7840\u914D\u7F6E
jmq4.app=JDLOMSMONITOR
jmq4.userName=JDLOMSMONITOR
jmq4.password=d209fb9a144a435dbcbdda6d8ba15f8d
jmq4.address=nameserver.jmq.jd.local:80

#\u5206\u7247\u662F\u5426\u81EA\u52A8\u964D\u7EA7
jmq4.downgrade=true
#\u964D\u7EA7\u81EA\u52A8\u6062\u590D\u65F6\u957Fms
jmq4.downgradeTime=120000
#\u5931\u8D25\u6B21\u6570
jmq4.errorCount=3

## TOPIC for JMQ2
###\u8BA2\u5355\u4E2D\u53F0MQ start
#\u652F\u4ED8\u8D85\u65F6\u81EA\u52A8\u89E6\u53D1\u53D6\u6D88
jdl.oms.express.cancel.pay.timeout.order.destination=cancel_pay_timeout_order
jdl.oms.express.callback.record.destination=callback_order_record
jdl.oms.express.modify.record.destination=modify_order_record
jdl.oms.express.cancel.record.destination=cancel_order_record
jdl.oms.express.delete.record.destination=delete_order_record
jdl.oms.express.order.snapshot.destination=order_snapshot_msg
jdl.oms.express.retry.order.pdq.destination=retry_order_pdq

#\u53F0\u8D26\u6D41\u6C34\u8BB0\u5F55
jdl.oms.express.order.bank.record.destination=orderBank_record

###\u8BA2\u5355\u4E2D\u53F0MQ end

###\u5916\u90E8MQ
# \u5916\u5355\u9000\u6B3E\u5B8C\u6210\u6D88\u606F
jdl.oms.express.order.refund=OutSideRefundMessage

#\u652F\u4ED8\u6D88\u606F
jdl.oms.express.jd.pay.success.notify.destination=jdpaySuccessNotify

#\u8FD0\u5355\u4FE1\u606F\u66F4\u65B0\u6D88\u606F
jdl.oms.express.waybill.update.info=bd_forward_update_info

#\u8BA2\u5355\u8BB0\u5F55
jdl.oms.express.create.record.destination=create_order_record

#\u8BE2\u4EF7\u8BB0\u5F55
jdl.oms.express.enquiry.record.destination=enquiry_order_record

#\u62E6\u622A\u8BB0\u5F55
jdl.oms.express.intercept.record.destination=intercept_order_record
#\u652F\u4ED8\u8BB0\u5F55
jdl.oms.express.pay.record.destination=pay_order_record
#\u8BA2\u5355\u6062\u590D\u8BB0\u5F55\u6D88\u606F
jdl.oms.express.recover.record.destination=recover_order_record
#\u8BA2\u5355\u91CD\u53D7\u7406\u8BB0\u5F55\u6D88\u606F
jdl.oms.express.reaccept.record.destination=reaccept_order_record
#\u5916\u5355\u53F0\u8D26-\u5BF9\u8D26\u5B8C\u6210\u6D88\u606F
jdl.oms.express.reconciliation.success.destination=OTS_DuiZhang_Success
#C2C\u53F0\u8D26\u521D\u59CB\u5316
jdl.oms.express.c2c.orderBank.init.destination=init_order_bank_c2c
#B2C\u53F0\u8D26\u521D\u59CB\u5316topic
jdl.oms.express.b2c.orderBank.init.destination=init_order_bank_b2c
#c2b\u53F0\u8D26\u521D\u59CB\u5316topic
jdl.oms.express.c2b.orderBank.init.destination=init_order_bank_c2b
#\u8BA2\u5355\u72B6\u6001\u53D8\u66F4\u901A\u77E5topic
jdl.oms.express.order.status.notify.destination=EXPRESS_ORDER_STATUS
#\u8BA2\u5355\u6570\u636E\u6D41\u6C34\u8BB0\u5F55\u6D88\u606F
jdl.oms.express.orderData.record.destination=EXPRESS_ORDER_DATA
#\u8BA2\u5355\u6570\u636E\u53D8\u66F4\u901A\u77E5
jdl.oms.express.orderData.update.notice=EXPRESS_ORDER_DATA_UPDATE_NOTICE
#\u8BA2\u5355\u63A5\u5355\u6210\u529F\u6D88\u606F
jdl.oms.express.createOrder.notice.destination=EXPRESS_ORDER_CREATE_NOTICE
##clp\u51B7\u94FE\u8BA1\u8D39\u7ED3\u679Cmq
jdl.oms.express.cc.fee.info.result.destination=lbs2eclp_fee_infos_result
#\u79D1\u6280\u652F\u4ED8\u7ED3\u679C
jdl.oms.express.pay.return.info.destination= pay_return_info

#\u8BE2\u4EF7\u7CFB\u7EDF\u63A8\u9001\u53D6\u6D88\u6D88\u606Fmq
jdl.oms.express.tms.enquiry.cancel.notify.destination=tms_enquiry_cancel_notify
#\u8BE2\u4EF7\u56DE\u4F20\u6D88\u606Fmq
jdl.oms.express.tms.enquiry.confirm.back.destination=tms_enquiry_confirm_back
#\u51B7\u94FE\u8FD0\u5355\u5F85\u652F\u4ED8\u8FD0\u5355mq
jdl.oms.express.cold.chain.waybill.unpaid.destination=coldchain_waybill_unpaid
#\u51B7\u94FE\u6574\u8F66\u8BE2\u4EF7\u63A8\u8BA1\u8D39mq
jdl.oms.express.cold.chain.push.fee.destination=eclp_to_lbs_enquiry_fee
#\u5FEB\u8FD0TMS\u8BE2\u4EF7\u56DE\u4F20
jdl.oms.express.ldop.middle.enquiry.bill.back.destination=ldop_middle_enquiry_bill_back
#\u8BE2\u4EF7\u8F66\u8F86\u4FE1\u606F\u56DE\u4F20
jdl.oms.express.tms.enquiry.vehicle.driver.destination=ldop_middle_enquiry_vehicle_driver
#\u53D1\u9001B\u7F51\u7279\u6B8A\u8D39\u7528
jdl.oms.express.b.normal.specail.fee=b_normal_specail_fee
# ldop mq when package amount changed
jdl.oms.express.ldop.package.number=ldop_package_number
# terminal notify destination
jdl.oms.express.terminal.notify.destination=ql_erp_receive_finish_xx

# ===========================================================================

#tc\u652F\u4ED8\u8D85\u65F6\u81EA\u52A8\u89E6\u53D1\u53D6\u6D88MQ
jdl.oms.express.tc.cancel.pay.timeout.order.destination=doo_cancel_pay_timeOut_order
#\u5FEB\u8FD0\u53F0\u8D26\u521D\u59CB\u5316topic
jdl.oms.express.freight.orderBank.init.destination=init_order_bank_freight
#\u6539\u5740\u5355\u72B6\u6001\u901A\u77E5
jdl.oms.express.readdress.status.notice.destination=EXPRESS_ORDER_READDRESS_STATUS_NOTICE
#\u8BE2\u4EF7\u63A5\u53E3\u53D1\u9001\u8BE2\u4EF7\u7ED3\u679C\u6D88\u606F
jdl.oms.express.enquiry.record.new.destination=EXPRESS_ORDER_ENQUIRY_RECORD
#\u8BA2\u5355\u652F\u4ED8\u5931\u8D25\u6D88\u606F\u901A\u77E5
jdl.oms.express.payment.fail.destination=EXPRESS_ORDER_PAYMENT_RESULT
jdl.oms.express.customs.status.destination=customs_status
jdl.oms.express.order.tax.destination=estimated_actual_tax
##\u8BA2\u5355\u9000\u6B3E\u8BB0\u5F55topic
jdl.oms.express.refund.record.destination=refund_order_record
#POP\u552E\u540E\u8BA2\u5355\u4E2D\u5FC3\u5185\u90E8\u6570\u636E\u540C\u6B65
jdl.oms.express.order.info.update=EXPRESS_ORDER_INFO_UPDATE
#\u8BA2\u5355\u4E2D\u5FC3\u5F02\u5E38\u91CD\u8BD5jmq
jdl.oms.express.exception.retry.destination=EXPRESS_ORDER_RETRY

#\u6743\u76CA\u6838\u9500\u6D88\u606F\u53D1\u9001
jdl.oms.express.right.confirm=omni_free_user_rights_confirm
#\u6743\u76CA\u91CA\u653E\u6D88\u606F\u53D1\u9001
jdl.oms.express.right.release=omni_free_user_rights_cancel
#\u5FEB\u8FD0\u6574\u8F66\u5F85\u9500\u552E\u786E\u8BA4
jdl.oms.express.wait.sales.confirm.destination=EXPRESS_ORDER_WAIT_SALES_CONFIRM
#\u7EAF\u914D\u8D44\u6E90\u91CA\u653E\u6D88\u606F
jdl.oms.express.order.resource.release.destination=EXPRESS_ORDER_RESOURCE_RELEASE
#send order track topic
jdl.oms.express.order.track.topic=bd_waybill_upload_trace
# readdress 1 order 2 end async enquiry topic
jdl.oms.express.order.readdress.async.enquiry.destination=EXPRESS_ORDER_READDRESS_ENQUIRY
# pay time out task with Timer
jdl.oms.express.pay.timeout.order.destination=EXPRESS_ORDER_PAY_TIMEOUT_TASK
# push mainland invoice
jdl.oms.express.order.invoice.destination=EXPRESS_ORDER_INVOICE
#\u5FEB\u9012\u5F02\u6B65\u8BE2\u4EF7
jdl.oms.express.order.common.async.enquiry.destination=EXPRESS_ORDER_COMMON_ASYNC_ENQUIRY
# topic for ccb2b async enquiry
jdl.oms.express.order.ccb2b.async.enquiry.destination=ESTIMATE_AMOUNT_CCB2B
# topic for releasing coupon
jdl.oms.express.order.common.async.release.coupon.destination=RELEASE_COUPON
# topic for wechat payment retry
jdl.oms.express.order.common.wechat.payment.retry.destination=WECHAT_PAYMENT_RETRY
# topic for refund order
jdl.oms.express.order.common.refund.order.destination=REFUND_ORDER
# topic for release integral
jdl.oms.express.order.common.integral.release.destination=INTEGRAL_RELEASE
# topic for release ious
jdl.oms.express.order.common.ious.release.destination=RELEASE_IOUS
# topic for modify ious
jdl.oms.express.order.common.ious.modify.destination=RELEASE_MODIFY_IOUS
# topic for b2c clear order bank
jdl.oms.express.order.b2c.clear.order.bank.destination=B2C_CLEAR_ORDER_BANK
# topic for reverse freight order bank
jdl.oms.express.order.common.freight.reverse.order.bank.destination=FREIGHT_REVERSE_ORDER_BANK
# topic for ccb2b async enquiry order bank
jdl.oms.express.order.ccb2b.async.enquiry.order.bank.destination=ASYNC_ENQUIRY_ORDER_BANK_CCB2B
# topic for push invoice info UEP
jdl.oms.express.order.common.push.invoice.info.uep.destination=PUSH_INVOICE_INFO_UEP
# topic for create TMS enquiry bill
jdl.oms.express.order.common.create.tms.enquiry.bill.destination=CREATE_TMS_ENQUIRY_BILL
# topic for persist ZC CCB2B issue
jdl.oms.express.order.zc.ccb2b.issue.persist.destination=ZC_CCB2B_ISSUE_PERSIST
# topic for persist ZC CCB2B cancel
jdl.oms.express.order.zc.ccb2b.cancel.persist.destination=ZC_CCB2B_CANCEL_PERSIST
# topic for initialize ZC CCB2B order bank
jdl.oms.express.order.zc.ccb2b.init.order.bank.destination=ZC_CCB2B_INIT_ORDER_BANK
# topic for persist ZC CCB2B create
jdl.oms.express.order.zc.ccb2b.create.persist.destination=ZC_CCB2B_CREATE_PERSIST
# topic for push EBS info freight
jdl.oms.express.order.push.ebs.info.freight.destination=PUSH_EBS_INFO_FREIGHT
# topic for close QR code of POS pay
jdl.oms.express.order.pos.pay.qr.close.destination=POS_PAY_QR_CLOSE
# topic for clear order bank of TMS
jdl.oms.express.order.tms.clear.order.bank.destination=TMS_CLEAR_ORDER_BANK
# topic for automatically write off
jdl.oms.express.order.auto.write.off.destination=AUTO_WRITE_OFF
# topic for refund waybill order
jdl.oms.express.order.common.waybill.refund.order.destination=WAYBILL_REFUND_ORDER
#\u5FEB\u8FD0\u6574\u8F66\u76F4\u8FBE\u62A5\u4EF7\u901A\u77E5CRM
jdl.oms.express.enquiry.notice.crm.destination=taci_adjust_price_task_mq
# \u6536\u53D1\u7BA1\u5BB6
jdl.oms.express.precharge.occupy.destination=EXPRESS_ORDER_PRECHARGE_OCCUPY
## async push ebs
jdl.oms.express.push.ebs.destination=OMS_EXPRESS_PUSH_EBS
# order repository retry
jdl.oms.express.order.retry.repository.destination=EXPRESS_RETRY_ORDER_JMQ
# cancel service order
jdl.oms.express.cancel.service.order=EXPRESS_ORDER_CANCEL_SERVICE_ORDER
# create payment order
jdl.oms.express.create.payment.order.destination=OMS_EXPRESS_CREATE_PAYMENT_ORDER
# release payment order
jdl.oms.express.release.payment.order.destination=OMS_EXPRESS_RELEASE_PAYMENT_ORDER
# Async StandardProductAndDiscountEnquiry
jdl.oms.express.standard.product.and.discount.enquiry.destination=EXPRESS_ORDER_SPAD_ENQUIRY
# topic for batrix exception
jdl.oms.express.order.batrix.exception=batrix_exception
# topic for delivery pickup sync bind
jdl.oms.express.delivery.pickup.sync.bind.destination=DELIVERY_PICKUP_SYNC_BIND
# topic for clear order bank
jdl.oms.express.order.clear.order.bank.destination=CLEAR_ORDER_BANK
# topic for sensitive words check
jdl.oms.express.sensitive.destination=JDL_OMS_SENSITIVE


# ===========================================================================

# jmq from xbp
jdl.oms.express.xbp.destination=XBP_EVENT