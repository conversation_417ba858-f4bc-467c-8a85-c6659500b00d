package cn.jdl.oms.express.worker.message.handler;

import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.address.JdAddressFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.ebs.EBSFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.ebs.EBSFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.ebs.EBSFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.entity.CommonDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.handler.ExpressAbstractHandler;
import cn.jdl.oms.express.domain.infrs.ohs.locals.message.pl.PushEBSJmqMessageDto;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.PaymentStatusEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.exception.JMQRetryException;
import com.jd.lbs.ebs.dto.EbsJfOtherRequest;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import javax.annotation.Resource;

/**
 * 纯配订单推送收入集成
 */
public class OmsExpressPushEbsHandler extends ExpressAbstractHandler {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(OmsExpressPushEbsHandler.class);

    /**
     * 台账facade
     */
    @Resource
    private EBSFacade ebsFacade;

    /**
     * 询价台账redisOp
     */
    @Resource
    private EBSFacadeTranslator ebsFacadeTranslator;

    /**
     * 获取订单详情服务
     */
    @Resource
    private GetOrderFacade getOrderFacade;
    /**
     * 获取订单详情防腐层请求转换
     */
    @Resource
    private GetOrderFacadeTranslator getOrderFacadeTranslator;
    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;



    /**
     * 地址编码转换
     */
    @Resource
    private JdAddressFacade jdAddressFacade;

    @Override
    public boolean handle(CommonDto commonDto) {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_WORKER_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        //台账初始化执行结果对象根据实际的执行结果定义返回具体的业务结果
        try {
            PushEBSJmqMessageDto jmqMessageDto = (PushEBSJmqMessageDto) commonDto;
            EBSFacadeRequest ebsFacadeRequest = jmqMessageDto.getEbsFacadeRequest();
            LOGGER.info("纯配订单推送收入集成开始:{}, request:{}", jmqMessageDto.getOrderNo(), ebsFacadeRequest.getPreId());
            if(ebsFacadeRequest.isTempStorageOrder()){
                if(!tempStorageOrderEbsFacadeRequestValid(ebsFacadeRequest)){
                    String errMsg = "暂存单尚未支付完成无需推送收入集成，orderNo:" + ebsFacadeRequest.getPreId();
                    LOGGER.warn(errMsg);
                    throw new JMQRetryException(errMsg);
                }
            }
            // 构建请求&推送开票信息
            EbsJfOtherRequest request = ebsFacadeTranslator.toServiceEnquiryEBSRequest(ebsFacadeRequest);
            //fixme 寄件地址 京标地址转国标
            request.setRegion(ebsRegionNoJb2Gb(request.getRegion()));
            ebsFacade.pushEBS(request);
            LOGGER.info("纯配订单推送收入集成完成:{}", jmqMessageDto.getOrderNo());
        } catch (Exception e) {
            Profiler.functionError(callerInfo);
            LOGGER.error("纯配订单推送收入集成-异常,再次重试", e);
            throw new JMQRetryException(UnifiedErrorSpec.BasisOrder.INTERNAL_ERROR).withCustom("纯配订单推送收入集成-异常,再次重试!");
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
        return true;
    }

    /**
     *
     * @param ebsFacadeRequest
     */
    private boolean tempStorageOrderEbsFacadeRequestValid(EBSFacadeRequest ebsFacadeRequest) {
        //查询暂存单详情
        GetOrderFacadeRequest getOrderFacadeRequest = new GetOrderFacadeRequest();
        getOrderFacadeRequest.setOrderNo(ebsFacadeRequest.getPreId());
        GetOrderFacadeResponse facadeResponse = getOrderFacade.getOrder(ebsFacadeRequest.getRequestProfile(),
                getOrderFacadeRequest);
        if (facadeResponse == null) {
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.ORDER_GET_FAIL)
                    .withCustom("暂存服务单不存在，tempStorageOrderNo:" + ebsFacadeRequest.getPreId());
        }
        //将订单详情转换成model
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(facadeResponse);
        ExpressOrderModel tempStorageFwOrder = new ExpressOrderModel(orderModelCreator);
        if(PaymentStatusEnum.COMPLETE_PAYMENT != tempStorageFwOrder.getFinance().getPaymentStatus()){
            LOGGER.warn("暂存单:{}尚未支付完成无需推送收入集成", tempStorageFwOrder.orderNo());
            return false;
        }
        ebsFacadeRequest.setTotalAmount(tempStorageFwOrder.getFinance().getDiscountAmount().getAmount());
        return true;
    }

    /**
     * 寄件二级地址京标转国标
     * @param jbProvinceNo
     * @return
     */
    private String ebsRegionNoJb2Gb(String jbProvinceNo){
        if(StringUtils.isBlank(jbProvinceNo)){
            return "";
        }
        AddressInfoDto addressInfoDto = jdAddressFacade.getGBDistrictByJDCode(jbProvinceNo);
        if (addressInfoDto == null) {
            LOGGER.error("推送收入集成失败-未获取到国标地址信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PUSH_EBS_FAIL).withCustom("推送收入集成失败-未获取到国标地址信息");
        }
        return addressInfoDto.getProvinceNo();
    }
}
