package cn.jdl.oms.express.worker.scheduler.ebs;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.domain.dto.AddressInfoDto;
import cn.jdl.oms.express.domain.facade.ExpressOrderModelCreator;
import cn.jdl.oms.express.domain.infrs.acl.facade.address.JdAddressFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.ebs.EBSFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.order.GetOrderFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.ebs.EBSFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeRequest;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeResponse;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderFacadeTranslator;
import cn.jdl.oms.express.domain.infrs.acl.pl.order.GetOrderModelCreatorTranslator;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.dto.AbstractMessageDto;
import cn.jdl.oms.express.domain.infrs.ohs.locals.scheduler.message.SchedulerMessage;
import cn.jdl.oms.express.domain.infrs.ohs.locals.ump.UmpUtil;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.PDQTopicEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import cn.jdl.oms.express.worker.scheduler.AbstractSchedulerHandler;
import com.jd.lbs.ebs.dto.EbsJfOtherRequest;
import com.jd.paq.core.Message;
import com.jd.paq.plugin.IMessage;
import com.jd.paq.plugin.PDQClientException;
import com.jd.paq.plugin.Result;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * 快运推送收入集成
 */
public class FreightPushEBSInfoHandler extends AbstractSchedulerHandler {
    /**
     * log
     */
    private static final Logger LOGGER = LoggerFactory.getLogger(cn.jdl.oms.express.worker.scheduler.invoice.FreightPushInvoiceInfoHandler.class);

    /**
     * 重试最大次数
     */
    private final static int MAX_RETRY_TIMES = 10;

    @Resource
    private UmpUtil umpUtil;

    /**
     * 获取订单详情服务
     */
    @Resource
    private GetOrderFacade getOrderFacade;

    /**
     * 获取订单详情防腐层请求转换
     */
    @Resource
    private GetOrderFacadeTranslator getOrderFacadeTranslator;

    /**
     * 订单详情model转换
     */
    @Resource
    private GetOrderModelCreatorTranslator orderModelCreatorTranslator;

    @Resource
    private EBSFacadeTranslator ebsFacadeTranslator;

    @Resource
    private EBSFacade ebsFacade;

    /**
     * 地址编码转换
     */
    @Resource
    private JdAddressFacade jdAddressFacade;

    /**
     * 推送收入集成
     *
     * @param iMessage
     * @return
     * @throws PDQClientException
     */
    @Override
    public Result execute(IMessage iMessage) throws PDQClientException {
        CallerInfo callerInfo = umpUtil.registerInfo(this.getClass().getName() + ".execute");
        Result result = new Result(Result.SYSTEMERROR);
        try {
            // 设置消息体为重试消息
            if (iMessage instanceof Message) {
                LOGGER.info("推送收入集成任务调度{}开始执行", iMessage.getTopic());

                if (((Message) iMessage).getRedriveCount() >= MAX_RETRY_TIMES) {
                    LOGGER.info("推送收入集成任务调度{},重试次数超过{}次,暂停重试", iMessage.getTopic(), MAX_RETRY_TIMES);
                    Profiler.businessAlarm(UmpKeyConstants.UMP_JDL_PUSH_INVOICE_FAIL, "推送收入集成达最大次数！消息信息:" + JSONUtils.beanToJSONDefault(iMessage));
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                String iMessageContent = iMessage.getMessageBody();
                PDQTopicEnum pdqTopicEnum = PDQTopicEnum.getByTopic(iMessage.getTopic());
                if (null == pdqTopicEnum) {
                    LOGGER.info("推送收入集成任务调度{},未匹配到任务队列,暂停重试", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                // 重试消息体
                SchedulerMessage schedulerMessage = JSONUtils.jsonToBean(iMessageContent, SchedulerMessage.class);
                if (null == schedulerMessage) {
                    LOGGER.info("推送收入集成任务调度{},重试消息体不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }
                AbstractMessageDto messageDto = (AbstractMessageDto) JSONUtils.jsonToBean(schedulerMessage.getDtoJson(), schedulerMessage.getDtoClass());
                if (null == messageDto || null == messageDto.getOrderNo() || null == messageDto.getRequestProfile()) {
                    LOGGER.info("推送收入集成任务调度{},场景业务数据对象不存在,暂停重试", iMessage.getTopic());
                    result.setCode(Result.INVALID_REQUEST);
                    return result;
                }

                // 解析message 获取信息
                RequestProfile requestProfile = messageDto.getRequestProfile();
                requestProfile.setTraceId(requestProfile.getTraceId() + "_" + pdqTopicEnum.getTopic());

                // 查询订单详情
                ExpressOrderModel orderModel = this.getOrderInfo(requestProfile, messageDto.getOrderNo());

                if (OrderTypeEnum.SERVICE_ENQUIRY_ORDER == orderModel.getOrderType()) {
                    //原单
                    ExpressOrderModel originOrderModel = this.getOrderInfo(requestProfile, orderModel.getRefOrderInfoDelegate().getOriginalOrderNo());
                    AddressInfoDto addressInfoDto = jdAddressFacade.getGBDistrictByJDCode(originOrderModel.getConsignor().getAddress().getProvinceNoGis());

                    if (addressInfoDto == null) {
                        LOGGER.error("推送收入集成失败-未获取到国标地址信息");
                        throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.PUSH_EBS_FAIL).withCustom("推送收入集成失败-未获取到国标地址信息");
                    }
                    // 构建请求&推送开票信息
                    EbsJfOtherRequest request = ebsFacadeTranslator.toServiceEnquiryEBSRequest(orderModel, originOrderModel, addressInfoDto.getProvinceNo());
                    ebsFacade.pushEBS(request);

                } else {
                    LOGGER.info("不在推送收入集成的订单业务范围");
                }
                result = new Result(Result.SUCCESS);
                LOGGER.info("推送收入集成完成");
            }
        } catch (BusinessDomainException e) {
            // 调用开票服务业务异常
            LOGGER.error("推送收入集成业务异常: {}", e.fullMessage());
        } catch (Exception e) {
            LOGGER.error("推送收入集成异常,再次重试", e);
            umpUtil.functionError(callerInfo);
        } finally {
            umpUtil.registerInfoEnd(callerInfo);
        }
        return result;
    }

    /**
     * 查询订单详情，并转换为领域模型
     *
     * @param requestProfile
     * @param orderNo
     * @return
     */
    private ExpressOrderModel getOrderInfo(RequestProfile requestProfile, String orderNo) {
        // 查询订单最新数据
        GetOrderFacadeRequest getOrderRequest = new GetOrderFacadeRequest();
        getOrderRequest.setOrderNo(orderNo);
        GetOrderFacadeResponse facadeResponse = getOrderFacade.getOrder(requestProfile, getOrderRequest);
        //将订单详情转换成model
        ExpressOrderModelCreator orderModelCreator = orderModelCreatorTranslator.toExpressOrderModelCreator(facadeResponse);
        return new ExpressOrderModel(orderModelCreator);
    }

}
