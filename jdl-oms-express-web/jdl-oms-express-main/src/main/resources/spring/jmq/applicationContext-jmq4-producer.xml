<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jmq="http://code.jd.com/schema/jmq"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
	http://code.jd.com/schema/jmq
    http://code.jd.com/schema/jmq/jmq-1.1.xsd">
    <!--JMQ应用连接服务配置-->
    <jmq:transport id="jmq4.transport" address="${jmq4.address}" user="${jmq4.userName}" password="${jmq4.password}"
                   app="${jmq4.app}"
                   downgrade="${jmq4.downgrade}" downgradeTime="${jmq4.downgradeTime}" errorCount="${jmq4.errorCount}"/>
    <jmq:producer id="jmq4Producer" retryTimes="2" transport="jmq4.transport"/>

    <!-- jmq4消息生产者 -->
    <!-- 订单状态变更通知生产者 -->
    <bean id="orderStatusNotifyJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.status.notify.destination}"/>
    </bean>

    <!--订单恢复记录生产者-->
    <bean id="recoverOrderMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.recover.record.destination}"/>
    </bean>

    <!--订单重受理记录生产者-->
    <bean id="reacceptOrderMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.reaccept.record.destination}"/>
    </bean>

    <!--询价记录生产者-->
    <bean id="enquiryRecordProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.enquiry.record.destination}"/>
    </bean>

    <!--拦截记录生产者-->
    <bean id="interceptRecordProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.intercept.record.destination}"/>
    </bean>

    <!--支付记录生产者-->
    <bean id="payRecordProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.pay.record.destination}"/>
    </bean>

    <!-- B2C初始化台账任务 -->
    <bean id="b2cInitOrderBankJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.b2c.orderBank.init.destination}"/>
    </bean>

    <!-- c2b初始化台账任务 -->
    <bean id="c2bInitOrderBankJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.c2b.orderBank.init.destination}"/>
    </bean>

    <!-- tc超时未支付取消 -->
    <bean id="tcPayTimeoutCancelProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.tc.cancel.pay.timeout.order.destination}"/>
    </bean>

    <!-- 订单数据流水任务 -->
    <bean id="orderDataFlowMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.orderData.record.destination}"/>
    </bean>

    <!-- 订单数据变更通知 -->
    <bean id="orderDataUpdateNoticeMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.orderData.update.notice}"/>
    </bean>

    <!-- 接单消息通知 -->
    <bean id="createOrderNoticeMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.createOrder.notice.destination}"/>
    </bean>

    <!-- 快运初始化台账任务 -->
    <bean id="freightInitOrderBankJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.freight.orderBank.init.destination}"/>
    </bean>

    <!-- 改址单状态通知 -->
    <bean id="readdressStatusNoticeProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.readdress.status.notice.destination}"/>
    </bean>

    <!--询价接口发送询价结果消息-->
    <bean id="expressOrderEnquiryRecordProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.enquiry.record.new.destination}"/>
    </bean>

    <!--支付失败消息通知-->
    <bean id="expressOrderPayFailProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.payment.fail.destination}"/>
    </bean>

    <!--订单退款记录生产者-->
    <bean id="refundRecordProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.refund.record.destination}"/>
    </bean>

    <!--订单中心内部数据同步通知-->
    <bean id="orderDataUpdateMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.info.update}"/>
    </bean>

    <!-- 订单中心通用异常重试 -->
    <bean id="expressRetryMsgProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.exception.retry.destination}"/>
    </bean>

    <!-- 订单状态妥投/拒收时进行权益核销-->
    <bean id="rightConfirmMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.right.confirm}"/>
    </bean>

    <!-- 订单状态取消时进行权益释放-->
    <bean id="rightReleaseMessageProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.right.release}"/>
    </bean>

    <!-- 快运整车待销售确认生产者 -->
    <bean id="waitSalesConfirmProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.wait.sales.confirm.destination}"/>
    </bean>

    <!-- 纯配资源释放生产者 -->
    <bean id="orderResourceReleaseJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.resource.release.destination}"/>
    </bean>

    <!-- 快递业务发送全程跟踪生产者 -->
    <bean id="orderTrackProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.track.topic}"/>
    </bean>

    <!-- 改址异步询价 -->
    <bean id="readdressAsyncEnquiryJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.readdress.async.enquiry.destination}"/>
    </bean>

    <!-- 超时未支付任务 -->
    <bean id="payTimeoutTaskProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.pay.timeout.order.destination}"/>
    </bean>

    <!-- 纯配通用发票推送 -->
    <bean id="expressInvoiceJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.invoice.destination}"/>
    </bean>

    <!-- 纯配异步询价消息 -->
    <bean id="commonAsyncEnquiryJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.common.async.enquiry.destination}"/>
    </bean>

    <!-- 快运整车直达报价通知CRM -->
    <bean id="enquiryNoticeCRMJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.enquiry.notice.crm.destination}"/>
    </bean>

    <!-- 收发管家异步预占JMQ -->
    <bean id="prechargeOccupyJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.precharge.occupy.destination}"/>
    </bean>

    <!-- 推送ebs收入集成 -->
    <bean id="pushEBSJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.push.ebs.destination}"/>
    </bean>

    <!-- 订单持久化重试 -->
    <bean id="retryOrderJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.order.retry.repository.destination}"/>
    </bean>

    <!-- 服务单取消JMQ -->
    <bean id="cancelServiceOrderJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.cancel.service.order}"/>
    </bean>

    <!-- 创建支付单 -->
    <bean id="createPaymentOrderJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.create.payment.order.destination}"/>
    </bean>

    <!-- 释放支付单 -->
    <bean id="releasePaymentOrderJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.release.payment.order.destination}"/>
    </bean>

    <!-- 发送异步事后折询价JMQ -->
    <bean id="asyncStandardProductAndDiscountEnquiryJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.standard.product.and.discount.enquiry.destination}"/>
    </bean>

    <!-- 送取同步绑定或解除绑定关联单JMQ -->
    <bean id="deliveryPickupSyncBindJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.delivery.pickup.sync.bind.destination}"/>
    </bean>

    <!-- 敏感词对比JMQ -->
    <bean id="sensitiveWordsJmqProducer" class="cn.jdl.oms.express.domain.infrs.ohs.locals.message.producer.impl.JMQMessageProducer">
        <property name="producer" ref="jmq4Producer"/>
        <property name="topic" value="${jdl.oms.express.sensitive.destination}"/>
    </bean>

    <!--  PDQ topic 到 JMQ4 topic映射 -->
    <bean id="pdqToJMQ4TopicMapper" class="cn.jdl.oms.express.shared.common.dict.PDQToJMQ4TopicMapper">
        <constructor-arg index="0">
            <map key-type="java.lang.String">
                <entry key="ESTIMATE_AMOUNT_CCB2B" value="${jdl.oms.express.order.ccb2b.async.enquiry.destination}" />
                <entry key="RELEASE_COUPON" value="${jdl.oms.express.order.common.async.release.coupon.destination}" />
                <entry key="WECHAT_PAYMENT_RETRY" value="${jdl.oms.express.order.common.wechat.payment.retry.destination}" />
                <entry key="REFUND_ORDER" value="${jdl.oms.express.order.common.refund.order.destination}" />
                <entry key="INTEGRAL_RELEASE" value="${jdl.oms.express.order.common.integral.release.destination}" />
                <entry key="RELEASE_IOUS" value="${jdl.oms.express.order.common.ious.release.destination}" />
                <entry key="RELEASE_MODIFY_IOUS" value="${jdl.oms.express.order.common.ious.modify.destination}" />
                <entry key="B2C_CLEAR_ORDER_BANK" value="${jdl.oms.express.order.b2c.clear.order.bank.destination}" />
                <entry key="FREIGHT_REVERSE_ORDER_BANK" value="${jdl.oms.express.order.common.freight.reverse.order.bank.destination}" />
                <entry key="ASYNC_ENQUIRY_ORDER_BANK_CCB2B" value="${jdl.oms.express.order.ccb2b.async.enquiry.order.bank.destination}" />
                <entry key="PUSH_INVOICE_INFO_UEP" value="${jdl.oms.express.order.common.push.invoice.info.uep.destination}" />
                <entry key="CREATE_TMS_ENQUIRY_BILL" value="${jdl.oms.express.order.common.create.tms.enquiry.bill.destination}" />
                <entry key="ZC_CCB2B_ISSUE_PERSIST" value="${jdl.oms.express.order.zc.ccb2b.issue.persist.destination}" />
                <entry key="ZC_CCB2B_CANCEL_PERSIST" value="${jdl.oms.express.order.zc.ccb2b.cancel.persist.destination}" />
                <entry key="ZC_CCB2B_INIT_ORDER_BANK" value="${jdl.oms.express.order.zc.ccb2b.init.order.bank.destination}" />
                <entry key="ZC_CCB2B_CREATE_PERSIST" value="${jdl.oms.express.order.zc.ccb2b.create.persist.destination}" />
                <entry key="PUSH_EBS_INFO_FREIGHT" value="${jdl.oms.express.order.push.ebs.info.freight.destination}" />
                <entry key="POS_PAY_QR_CLOSE" value="${jdl.oms.express.order.pos.pay.qr.close.destination}" />
                <entry key="TMS_CLEAR_ORDER_BANK" value="${jdl.oms.express.order.tms.clear.order.bank.destination}" />
                <entry key="AUTO_WRITE_OFF" value="${jdl.oms.express.order.auto.write.off.destination}" />
                <entry key="WAYBILL_REFUND_ORDER" value="${jdl.oms.express.order.common.waybill.refund.order.destination}" />
                <entry key="CLEAR_ORDER_BANK" value="${jdl.oms.express.order.clear.order.bank.destination}" />
            </map>
        </constructor-arg>
    </bean>
</beans>
