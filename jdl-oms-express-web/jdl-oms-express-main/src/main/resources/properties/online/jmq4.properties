#JMQ\u57FA\u7840\u914D\u7F6E
jmq4.app=JDLOMSEXPRESS
jmq4.userName=JDLOMSEXPRESS
jmq4.password=4ec4980b5cdb4191a3691180ff2f25c3
jmq4.address=nameserver.jmq.jd.local:80

#\u5206\u7247\u662F\u5426\u81EA\u52A8\u964D\u7EA7
jmq4.downgrade=true
#\u964D\u7EA7\u81EA\u52A8\u6062\u590D\u65F6\u957Fms
jmq4.downgradeTime=120000
#\u5931\u8D25\u6B21\u6570
jmq4.errorCount=3

#tc\u652F\u4ED8\u8D85\u65F6\u81EA\u52A8\u89E6\u53D1\u53D6\u6D88MQ
jdl.oms.express.tc.cancel.pay.timeout.order.destination=doo_cancel_pay_timeOut_order
#\u5FEB\u8FD0\u53F0\u8D26\u521D\u59CB\u5316topic
jdl.oms.express.freight.orderBank.init.destination=init_order_bank_freight
#\u6539\u5740\u5355\u72B6\u6001\u901A\u77E5
jdl.oms.express.readdress.status.notice.destination=EXPRESS_ORDER_READDRESS_STATUS_NOTICE
#\u8BE2\u4EF7\u63A5\u53E3\u53D1\u9001\u8BE2\u4EF7\u7ED3\u679C\u6D88\u606F
jdl.oms.express.enquiry.record.new.destination=EXPRESS_ORDER_ENQUIRY_RECORD
#\u8BA2\u5355\u652F\u4ED8\u5931\u8D25\u6D88\u606F\u901A\u77E5
jdl.oms.express.payment.fail.destination=EXPRESS_ORDER_PAYMENT_RESULT
jdl.oms.express.customs.status.destination=customs_status
jdl.oms.express.order.tax.destination=estimated_actual_tax
##\u8BA2\u5355\u9000\u6B3E\u8BB0\u5F55topic
jdl.oms.express.refund.record.destination=refund_order_record
#POP\u552E\u540E\u8BA2\u5355\u4E2D\u5FC3\u5185\u90E8\u6570\u636E\u540C\u6B65
jdl.oms.express.order.info.update=EXPRESS_ORDER_INFO_UPDATE
#\u8BA2\u5355\u4E2D\u5FC3\u5F02\u5E38\u91CD\u8BD5jmq
jdl.oms.express.exception.retry.destination=EXPRESS_ORDER_RETRY

#\u6743\u76CA\u6838\u9500\u6D88\u606F\u53D1\u9001
jdl.oms.express.right.confirm=omni_free_user_rights_confirm
#\u6743\u76CA\u91CA\u653E\u6D88\u606F\u53D1\u9001
jdl.oms.express.right.release=omni_free_user_rights_cancel
#\u5FEB\u8FD0\u6574\u8F66\u5F85\u9500\u552E\u786E\u8BA4
jdl.oms.express.wait.sales.confirm.destination=EXPRESS_ORDER_WAIT_SALES_CONFIRM
#\u7EAF\u914D\u8D44\u6E90\u91CA\u653E\u6D88\u606F
jdl.oms.express.order.resource.release.destination=EXPRESS_ORDER_RESOURCE_RELEASE
#send order track topic
jdl.oms.express.order.track.topic=bd_waybill_upload_trace
# readdress 1 order 2 end async enquiry topic
jdl.oms.express.order.readdress.async.enquiry.destination=EXPRESS_ORDER_READDRESS_ENQUIRY
# pay time out task with Timer
jdl.oms.express.pay.timeout.order.destination=EXPRESS_ORDER_PAY_TIMEOUT_TASK
# push mainland invoice
jdl.oms.express.order.invoice.destination=EXPRESS_ORDER_INVOICE
#\u5FEB\u9012\u5F02\u6B65\u8BE2\u4EF7
jdl.oms.express.order.common.async.enquiry.destination=EXPRESS_ORDER_COMMON_ASYNC_ENQUIRY
# topic for ccb2b async enquiry
jdl.oms.express.order.ccb2b.async.enquiry.destination=ESTIMATE_AMOUNT_CCB2B
# topic for releasing coupon
jdl.oms.express.order.common.async.release.coupon.destination=RELEASE_COUPON
# topic for wechat payment retry
jdl.oms.express.order.common.wechat.payment.retry.destination=WECHAT_PAYMENT_RETRY
# topic for refund order
jdl.oms.express.order.common.refund.order.destination=REFUND_ORDER
# topic for release integral
jdl.oms.express.order.common.integral.release.destination=INTEGRAL_RELEASE
# topic for release ious
jdl.oms.express.order.common.ious.release.destination=RELEASE_IOUS
# topic for modify ious
jdl.oms.express.order.common.ious.modify.destination=RELEASE_MODIFY_IOUS
# topic for b2c clear order bank
jdl.oms.express.order.b2c.clear.order.bank.destination=B2C_CLEAR_ORDER_BANK
# topic for reverse freight order bank
jdl.oms.express.order.common.freight.reverse.order.bank.destination=FREIGHT_REVERSE_ORDER_BANK
# topic for ccb2b async enquiry order bank
jdl.oms.express.order.ccb2b.async.enquiry.order.bank.destination=ASYNC_ENQUIRY_ORDER_BANK_CCB2B
# topic for push invoice info UEP
jdl.oms.express.order.common.push.invoice.info.uep.destination=PUSH_INVOICE_INFO_UEP
# topic for create TMS enquiry bill
jdl.oms.express.order.common.create.tms.enquiry.bill.destination=CREATE_TMS_ENQUIRY_BILL
# topic for persist ZC CCB2B issue
jdl.oms.express.order.zc.ccb2b.issue.persist.destination=ZC_CCB2B_ISSUE_PERSIST
# topic for persist ZC CCB2B cancel
jdl.oms.express.order.zc.ccb2b.cancel.persist.destination=ZC_CCB2B_CANCEL_PERSIST
# topic for initialize ZC CCB2B order bank
jdl.oms.express.order.zc.ccb2b.init.order.bank.destination=ZC_CCB2B_INIT_ORDER_BANK
# topic for persist ZC CCB2B create
jdl.oms.express.order.zc.ccb2b.create.persist.destination=ZC_CCB2B_CREATE_PERSIST
# topic for push EBS info freight
jdl.oms.express.order.push.ebs.info.freight.destination=PUSH_EBS_INFO_FREIGHT
# topic for close QR code of POS pay
jdl.oms.express.order.pos.pay.qr.close.destination=POS_PAY_QR_CLOSE
# topic for clear order bank of TMS
jdl.oms.express.order.tms.clear.order.bank.destination=TMS_CLEAR_ORDER_BANK
# topic for automatically write off
jdl.oms.express.order.auto.write.off.destination=AUTO_WRITE_OFF
# topic for refund waybill order
jdl.oms.express.order.common.waybill.refund.order.destination=WAYBILL_REFUND_ORDER
#\u5FEB\u8FD0\u6574\u8F66\u76F4\u8FBE\u62A5\u4EF7\u901A\u77E5CRM
jdl.oms.express.enquiry.notice.crm.destination=taci_adjust_price_task_mq
# \u6536\u53D1\u7BA1\u5BB6
jdl.oms.express.precharge.occupy.destination=EXPRESS_ORDER_PRECHARGE_OCCUPY
## async push ebs
jdl.oms.express.push.ebs.destination=OMS_EXPRESS_PUSH_EBS
# order repository retry
jdl.oms.express.order.retry.repository.destination=EXPRESS_RETRY_ORDER_JMQ
# cancel service order
jdl.oms.express.cancel.service.order=EXPRESS_ORDER_CANCEL_SERVICE_ORDER
# create payment order
jdl.oms.express.create.payment.order.destination=OMS_EXPRESS_CREATE_PAYMENT_ORDER
# release payment order
jdl.oms.express.release.payment.order.destination=OMS_EXPRESS_RELEASE_PAYMENT_ORDER
# Async StandardProductAndDiscountEnquiry
jdl.oms.express.standard.product.and.discount.enquiry.destination=EXPRESS_ORDER_SPAD_ENQUIRY
# topic for batrix exception
jdl.oms.express.order.batrix.exception=batrix_exception
# topic for delivery pickup sync bind
jdl.oms.express.delivery.pickup.sync.bind.destination=DELIVERY_PICKUP_SYNC_BIND
# topic for clear order bank
jdl.oms.express.order.clear.order.bank.destination=CLEAR_ORDER_BANK
# topic for sensitive words check
jdl.oms.express.sensitive.destination=JDL_OMS_SENSITIVE