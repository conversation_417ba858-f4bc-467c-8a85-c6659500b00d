package cn.jdl.oms.express.b2c;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.application.service.CreateExpressOrderServiceImpl;
import cn.jdl.oms.express.domain.infrs.acl.facade.exchangecurrency.ExchangeCurrencyFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.tms.WaybillNoFacade;
import cn.jdl.oms.express.domain.ohs.translator.CreateExpressOrderTranslator;
import cn.jdl.oms.express.model.CreateExpressOrderRequest;
import cn.jdl.oms.express.model.CreateExpressOrderResponse;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.text.ParseException;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class HMCreateOrderTestForWs {

    @Resource
    private CreateExpressOrderServiceImpl createExpressOrderServiceImpl;

    @Resource
    private CreateExpressOrderTranslator expressOrderTranslator;

    @Resource
    private WaybillNoFacade waybillNoFacade;

    @Resource
    private ExchangeCurrencyFacade exchangeCurrencyFacade;

    @Test
    public void test() throws ParseException {
        String orderNo = "wangsong1111";
        RequestProfile requestProfile = JSON.parseObject("{\"ext\":{},\"locale\":\"zh_CN\",\"tenantId\":\"1000\",\"timeZone\":\"GMT+8\",\"traceId\":\"" + orderNo + "\"}", RequestProfile.class);
        CreateExpressOrderRequest request = JSON.parseObject("{\n" +
                "    \"businessIdentity\": {\n" +
                "        \"businessStrategy\": \"BOpenExpress\",\n" +
                "        \"businessType\": \"simple_service\",\n" +
                "        \"businessUnit\": \"cn_jdl_b2c\"\n" +
                "    },\n" +
                "    \"cargoInfos\": [\n" +
                "        {\n" +
                "            \"cargoDimension\": {\n" +
                "                \"height\": 0,\n" +
                "                \"length\": 0,\n" +
                "                \"unit\": \"CM\",\n" +
                "                \"width\": 0\n" +
                "            },\n" +
                "            \"cargoName\": \"托寄物\",\n" +
                "            \"cargoQuantity\": {\n" +
                "                \"unit\": \"件\",\n" +
                "                \"value\": 1\n" +
                "            },\n" +
                "            \"cargoType\": \"776\",\n" +
                "            \"cargoVolume\": {\n" +
                "                \"unit\": \"CM3\",\n" +
                "                \"value\": 0.032\n" +
                "            },\n" +
                "            \"cargoWeight\": {\n" +
                "                \"unit\": \"KG\",\n" +
                "                \"value\": 1.0\n" +
                "            },\n" +
                "            \"extendProps\": {\n" +
                "                \"category\": \"衣物\"\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"channelInfo\": {\n" +
                "        \"channelNo\": \"0030001\",\n" +
                "        \"channelOperateTime\": 1727777353557,\n" +
                "        \"channelOrderNo\": \"dewu-bddh131231231231\",\n" +
                "        \"customerOrderNo\": \"dewu-bddh111212213\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        },\n" +
                "        \"systemCaller\": \"JOS\",\n" +
                "        \"systemSubCaller\": \"JOS\"\n" +
                "    },\n" +
                "    \"consigneeInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"广东省广州市番禺区化龙镇山门大道700号\",\n" +
                "            \"addressGis\": \"山门大道700号\",\n" +
                "            \"addressSource\": 2,\n" +
                "            \"cityName\": \"广州市\",\n" +
                "            \"cityNameGis\": \"广州市\",\n" +
                "            \"cityNo\": \"1601\",\n" +
                "            \"cityNoGis\": \"1601\",\n" +
                "            \"conflictLevel\": -1,\n" +
                "            \"countyName\": \"番禺区\",\n" +
                "            \"countyNameGis\": \"番禺区\",\n" +
                "            \"countyNo\": \"36953\",\n" +
                "            \"countyNoGis\": \"36953\",\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"preciseGis\": -1,\n" +
                "            \"provinceName\": \"广东\",\n" +
                "            \"provinceNameGis\": \"广东\",\n" +
                "            \"provinceNo\": \"19\",\n" +
                "            \"provinceNoGis\": \"19\",\n" +
                "            \"regionNo\": \"CN\",\n" +
                "            \"townName\": \"化龙镇\",\n" +
                "            \"townNameGis\": \"化龙镇\",\n" +
                "            \"townNo\": \"50399\",\n" +
                "            \"townNoGis\": \"50399\"\n" +
                "        },\n" +
                "        \"consigneeMobile\": \"***********\",\n" +
                "        \"consigneeName\": \"dwh测试单\",\n" +
                "        \"consigneePhone\": \"18612580602\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"consignorInfo\": {\n" +
                "        \"addressInfo\": {\n" +
                "            \"address\": \"上海市嘉定区叶城路1118号\",\n" +
                "            \"addressGis\": \"叶城路1118号\",\n" +
                "            \"addressSource\": 2,\n" +
                "            \"cityName\": \"嘉定区\",\n" +
                "            \"cityNameGis\": \"嘉定区\",\n" +
                "            \"cityNo\": \"2826\",\n" +
                "            \"cityNoGis\": \"2826\",\n" +
                "            \"countyName\": \"嘉定工业区\",\n" +
                "            \"countyNameGis\": \"嘉定工业区\",\n" +
                "            \"countyNo\": \"51950\",\n" +
                "            \"countyNoGis\": \"51950\",\n" +
                "            \"fenceTrusted\": 1,\n" +
                "            \"latitude\": \"31.36298\",\n" +
                "            \"longitude\": \"121.24492\",\n" +
                "            \"preciseGis\": 111,\n" +
                "            \"provinceName\": \"上海\",\n" +
                "            \"provinceNameGis\": \"上海\",\n" +
                "            \"provinceNo\": \"2\",\n" +
                "            \"provinceNoGis\": \"2\",\n" +
                "            \"regionNo\": \"CN\"\n" +
                "        },\n" +
                "        \"consignorMobile\": \"***********\",\n" +
                "        \"consignorName\": \"测试dwh\",\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"customerInfo\": {\n" +
                "        \"accountNo\": \"10K43816\"\n" +
                "    },\n" +
                "    \"extendProps\": {\n" +
                "        \"extendInfos\": \"{\\\"cargoValueAddedAlgo\\\":\\\"71536e538c17495984bd86260f89c999200\\\"}\",\n" +
                "        \"waybillType\": \"1\",\n" +
                "        \"sequenceNo\": \"1841057775797796864\",\n" +
                "        \"customerInfoExtendProps\": \"{}\"\n" +
                "    },\n" +
                "    \"financeInfo\": {\n" +
                "        \"deductionInfos\": [\n" +
                "\n" +
                "        ],\n" +
                "        \"paymentStage\": 2,\n" +
                "        \"settlementType\": 2\n" +
                "    },\n" +
                "    \"operator\": \"dewu\",\n" +
                "    \"orderSubType\": \"DELIVERY\",\n" +
                "    \"orderType\": \"700\",\n" +
                "    \"productInfos\": [\n" +
                "        {\n" +
                "            \"productNo\": \"ed-m-0001\",\n" +
                "            \"productType\": 1\n" +
                "        },\n" +
                "        {\n" +
                "            \"parentNo\": \"ed-m-0001\",\n" +
                "            \"productAttrs\": {\n" +
                "                \"rejectAuditType\": \"once\",\n" +
                "                \"rejectAuditNumbers\": \"1\"\n" +
                "            },\n" +
                "            \"productNo\": \"ed-a-0005\",\n" +
                "            \"productType\": 2\n" +
                "        }\n" +
                "    ],\n" +
                "    \"refOrderInfo\": {\n" +
                "        \"extendProps\": {\n" +
                "\n" +
                "        }\n" +
                "    },\n" +
                "    \"shipmentInfo\": {\n" +
                "        \"extendProps\": {\n" +
                "            \"shipmentExtendProps\": \"{}\"\n" +
                "        },\n" +
                "        \"pickupType\": 1,\n" +
                "        \"serviceRequirements\": {\n" +
                "\n" +
                "        }\n" +
                "    }\n" +
                "}", CreateExpressOrderRequest.class);

        CreateExpressOrderResponse response = createExpressOrderServiceImpl.createOrder(requestProfile, request);
        System.out.println(JSONUtils.beanToJSONDefault(response));
    }
}
