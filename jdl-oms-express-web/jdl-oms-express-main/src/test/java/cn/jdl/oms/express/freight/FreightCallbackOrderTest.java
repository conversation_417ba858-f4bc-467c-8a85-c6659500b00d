package cn.jdl.oms.express.freight;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.model.CallBackExpressOrderRequest;
import cn.jdl.oms.express.model.CallBackExpressOrderResponse;
import cn.jdl.oms.express.service.CallBackExpressOrderService;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * @description:快运回传测试
 * @author:yangyusong1
 * @createdate:2023-03-23 10:44
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class FreightCallbackOrderTest {

    @Resource
    private CallBackExpressOrderService callBackExpressOrderServiceImpl;

    @Test
    public void test() {
        RequestProfile requestProfile = JSON.parseObject("{\"ext\": {},\"locale\": \"zh_CN\",\"tenantId\": \"1000\",\"timeZone\": \"GMT+8\",\"traceId\": \"1612345234234560\"}", RequestProfile.class);

        String requestStr =
                "{\n" +
                "      \"businessIdentity\": {\n" +
                "            \"businessStrategy\": \"BFreight\",\n" +
                "            \"businessType\": \"transport\",\n" +
                "            \"businessUnit\": \"cn_jdl_freight-service\"\n" +
                "      },\n" +
                "      \"orderNo\":\"ECO1000010006422862\",\n" +
                "      \"channelInfo\": {\n" +
                "            \"channelOperateTime\": \"2021-01-12 12:00:00\",\n" +
                "            \"systemCaller\": \"Shop\",\n" +
                "            \"systemSubCaller\": \"Shop\"\n" +
                "      },\n" +
                "      \"operationType\": \"1\",\n" +
                "      \"operator\": \"EasyOne\",\n" +
                "      \"remark\": \"快运回传测试\"\n" +
                "}";

        CallBackExpressOrderRequest request = JSON.parseObject(requestStr,CallBackExpressOrderRequest.class);
        request.setExecutedStatus("150");
        CallBackExpressOrderResponse response = callBackExpressOrderServiceImpl.callBackOrder(requestProfile, request);
        System.out.println(JSONUtils.beanToJSONDefault(response));
    }

    @Test
    public void testReverse() {
        RequestProfile requestProfile = JSON.parseObject("{\"ext\": {},\"locale\": \"zh_CN\",\"tenantId\": \"1000\",\"timeZone\": \"GMT+8\",\"traceId\": \"1612345234234560\"}", RequestProfile.class);

        String requestStr =
                "{\n" +
                "      \"businessIdentity\": {\n" +
                "            \"businessStrategy\": \"BFreight\",\n" +
                "            \"businessType\": \"reverse_transport\",\n" +
                "            \"businessUnit\": \"cn_jdl_freight-consumer\"\n" +
                "      },\n" +
                "      \"orderNo\":\"FO0010002077684\",\n" +
                "      \"channelInfo\": {\n" +
                "            \"channelOperateTime\": \"2021-01-12 12:00:00\",\n" +
                "            \"systemCaller\": \"Shop\",\n" +
                "            \"systemSubCaller\": \"Shop\"\n" +
                "      },\n" +
                "      \"operationType\": \"1\",\n" +
                "      \"operator\": \"EasyOne\",\n" +
                "      \"remark\": \"快运回传测试\"\n" +
                "}";

        CallBackExpressOrderRequest request = JSON.parseObject(requestStr,CallBackExpressOrderRequest.class);
        request.setExecutedStatus("150");
        CallBackExpressOrderResponse response = callBackExpressOrderServiceImpl.callBackOrder(requestProfile, request);
        System.out.println(JSONUtils.beanToJSONDefault(response));
    }

}
