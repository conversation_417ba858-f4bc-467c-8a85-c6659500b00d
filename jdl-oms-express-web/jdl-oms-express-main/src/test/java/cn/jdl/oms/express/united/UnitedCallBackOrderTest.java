package cn.jdl.oms.express.united;

import cn.jdl.batrix.spec.RequestProfile;
import cn.jdl.oms.express.application.service.CallBackExpressOrderServiceImpl;
import cn.jdl.oms.express.model.CallBackExpressOrderRequest;
import cn.jdl.oms.express.model.CallBackExpressOrderResponse;
import cn.jdl.oms.express.model.ModifyExpressOrderRequest;
import cn.jdl.oms.express.shared.common.utils.JSONUtils;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.text.ParseException;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:./applicationContext.xml"})
@ActiveProfiles("test")
public class UnitedCallBackOrderTest {

    private static final Logger LOGGER = LoggerFactory.getLogger(UnitedCallBackOrderTest.class);

    @Resource
    private CallBackExpressOrderServiceImpl callBackExpressOrderServiceImpl;

    /**
     * 修改增值服务触发询价
     * @throws ParseException
     */
    @Test
    public void test() throws ParseException {

        CallBackExpressOrderResponse response = callBackExpressOrderServiceImpl.callBackOrder(getRequestProfile(), getCallBackExpressOrderRequest());
        LOGGER.info(JSONUtils.beanToJSONDefault(response));

    }

    private CallBackExpressOrderRequest getCallBackExpressOrderRequest() {
        String requestStr = "{\n" +
                "    \"businessIdentity\": {\n" +
                "        \"businessStrategy\": \"BWLStandardFreight\",\n" +
                "        \"businessType\": \"express\",\n" +
                "        \"businessUnit\": \"cn_jdl_united_c2c\"\n" +
                "    },\n" +
                "    \"channelInfo\": {\n" +
                "        \"channelOperateTime\": 1713164246915,\n" +
                "        \"systemCaller\": \"ExpressOFC\"\n" +
                "    },\n" +
                "    \"executedStatus\": \"-640\",\n" +
                "    \"operator\": \"17962\",\n" +
                "    \"orderNo\": \"EO0020039200216\"\n" +
                "}";

        return JSONUtils.jsonToBean(requestStr, CallBackExpressOrderRequest.class);
    }

    private ModifyExpressOrderRequest getModifyExpressOrderRequestEnquiry() {
        String requestStr = "{\n" +
                "    \"orderNo\":\"ECO1000010017722640\",\n" +
                "    \"businessIdentity\":{\n" +
                "        \"businessStrategy\": \"BWLStandardFreight\",\n" +
                "        \"businessType\": \"express\",\n" +
                "        \"businessUnit\": \"cn_jdl_united_c2c\"\n" +
                "    },\n" +
                "    \"channelInfo\":{\n" +
                "        \"channelNo\":\"0030001\",\n" +
                "        \"channelOperateTime\":1679542555441,\n" +
                "        \"customerOrderNo\":\"freight100000040\",\n" +
                "        \"systemCaller\":\"JOS\",\n" +
                "        \"systemSubCaller\":\"1\"\n" +
                "    },\n" +
                "      \"productInfos\":[\n" +
                "      {\n" +
                "            \"extendProps\":{\n" +
                "                \"operateType\":\"1\"\n" +
                "      },\n" +
                "            \"productNo\": \"fr-a-0006\",\n" +
                "            \"productType\": 2\n" +
                "      }\n" +
                "    ],\n" +
                "    \"extendProps\":{\n" +
                "        \"modifySceneRule\":\"afterPickUp\"\n" +
                "    },\n" +
                "    \"initiatorType\":4,\n" +
                "    \"operator\":\"EasyOne\",\n" +
                "    \"remark\":\"快运修改测试\",\n" +
                "    \"modifiedFields\":{\n" +
                "        \"productInfos\":\"1\"\n" +
                "    }\n" +
                "}\n";

        return JSONUtils.jsonToBean(requestStr, ModifyExpressOrderRequest.class);
    }

    private RequestProfile getRequestProfile() {
        return JSON.parseObject("{\n" +
                "\t\"ext\": {},\n" +
                "\t\"locale\": \"zh_CN\",\n" +
                "\t\"tenantId\": \"1000\",\n" +
                "\t\"timeZone\": \"GMT+8\",\n" +
                "\t\"traceId\": \"16122478551555561\"\n" +
                "}", RequestProfile.class);
    }
}