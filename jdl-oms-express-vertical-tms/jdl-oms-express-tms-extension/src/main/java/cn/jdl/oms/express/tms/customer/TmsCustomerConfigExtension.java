package cn.jdl.oms.express.tms.customer;

import cn.jdl.oms.express.domain.bc.ExpressOrderContext;
import cn.jdl.oms.express.domain.bo.CustomerConfig;
import cn.jdl.oms.express.domain.extension.ExpressOrderProduct;
import cn.jdl.oms.express.domain.extension.customer.ICustomerConfigExtension;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerConfigFacade;
import cn.jdl.oms.express.domain.infrs.acl.facade.customer.CustomerFacade;
import cn.jdl.oms.express.domain.infrs.acl.pl.customer.DeptResponse;
import cn.jdl.oms.express.domain.model.ExpressOrderModel;
import cn.jdl.oms.express.domain.spec.dict.OrderTypeEnum;
import cn.jdl.oms.express.shared.common.constant.UmpKeyConstants;
import cn.jdl.oms.express.shared.common.dict.TraderOperateStateEnum;
import cn.jdl.oms.express.shared.common.dict.UnifiedErrorSpec;
import cn.jdl.oms.express.shared.common.dict.UnifiedSubErrorSpec;
import cn.jdl.oms.express.shared.common.exception.AbilityExtensionException;
import cn.jdl.oms.express.shared.common.exception.BusinessDomainException;
import com.jd.matrix.sdk.annotation.Extension;
import com.jd.ump.profiler.CallerInfo;
import com.jd.ump.profiler.proxy.Profiler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

/**
 * tms客户配置校验扩展
 *
 * <AUTHOR>
 */
@Extension(code = ExpressOrderProduct.CODE)
public class TmsCustomerConfigExtension implements ICustomerConfigExtension {

    private static final Logger LOGGER = LoggerFactory.getLogger(TmsCustomerConfigExtension.class);

    /**
     * 客户配置信息
     */
    @Resource
    private CustomerConfigFacade customerConfigFacade;

    /**
     * 事业部信息
     */
    @Resource
    private CustomerFacade customerFacade;

    @Override
    public void execute(ExpressOrderContext expressOrderContext) throws AbilityExtensionException {
        CallerInfo callerInfo = Profiler.registerInfo(this.getClass().getName() + ".execute"
                , UmpKeyConstants.JDL_OMS_EXPRESS_APP_CODE
                , UmpKeyConstants.METHOD_ENABLE_HEART
                , UmpKeyConstants.METHOD_ENABLE_TP);
        try {
            LOGGER.info("运力平台客户配置信息校验开始");
            // 账号信息校验
            checkBasicTraderInfo(expressOrderContext);
            // 事业部信息校验
            checkEbuInfo(expressOrderContext);
            LOGGER.info("运力平台客户配置信息校验结束");
        } catch (BusinessDomainException e) {
            LOGGER.error("运力平台客户配置信息失败", e);
            throw e;
        } catch (Exception e) {
            LOGGER.error("运力平台客户配置信息校验异常", e);
            Profiler.functionError(callerInfo);
            throw new AbilityExtensionException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL, e);
        } finally {
            Profiler.registerInfoEnd(callerInfo);
        }
    }

    /**
     * 账号信息校验
     */
    private void checkBasicTraderInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel expressOrderModel = expressOrderContext.getOrderModel();

        CustomerConfig customerConfig = customerConfigFacade.getCustomerConfig(expressOrderContext);
        // 青龙业主号校验
        if (customerConfig == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关客户信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0007.desc());
        }

        // 补全账号id
        expressOrderModel.getComplementModel().complementAccountId(this, customerConfig.getCustomerId());
        // 补全账号名称
        expressOrderModel.getComplementModel().complementAccountName(this, customerConfig.getCustomerName());

        //青龙业主号校验
        if (!TraderOperateStateEnum.normal.getCode().equals(customerConfig.getTraderOperateState())) {
            LOGGER.error("青龙业主号:{}状态异常，TraderOperateState:{}"
                    , expressOrderModel.getCustomer().getAccountNo()
                    , customerConfig.getTraderOperateState());
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withCustom("客户配置信息校验失败,商家青龙业主号状态异常");
        }

    }

    /**
     * 校验事业部信息
     */
    private void checkEbuInfo(ExpressOrderContext expressOrderContext) {
        ExpressOrderModel orderModel = expressOrderContext.getOrderModel();
        //获取事业部信息
        DeptResponse deptInfo = customerFacade.getDept(orderModel.getCustomer().getAccountNo2());
        if (deptInfo == null) {
            LOGGER.error("客户配置信息校验失败，未查到相关事业部信息");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0020.desc());
        }
        if (deptInfo.getExpireStatus()) {
            LOGGER.error("客户配置信息校验失败，事业部逾期");
            throw new BusinessDomainException(UnifiedErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL)
                    .withSubCode(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0022.subCode())
                    .withCustom(UnifiedSubErrorSpec.BasisOrder.CUSTOMER_CONFIG_VALIDATE_FAIL_0022.desc());
        }
    }

}
