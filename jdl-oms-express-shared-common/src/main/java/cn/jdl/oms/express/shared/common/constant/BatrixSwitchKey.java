package cn.jdl.oms.express.shared.common.constant;

/**
 * batrix ducc集成
 * ducc KEY值常量类
 */
public class BatrixSwitchKey {

    /**
     * 不允许改址的增值服务黑名单
     */
    public static final String READDRESS_ADD_ON_PRODUCT_BLACKLIST = "readdressAddOnProductBlackList";

    /**
     * 允许改址的订单状态白名单
     */
    public static final String READDRESS_ORDER_STATUS_WHITELIST = "readdressOrderStatusWhiteList";

    /**
     * 改址后复重量方次数上限
     */
    public static final String MODIFY_RECORD_LIMIT_VALUE = "modifyRecordLimitValue";

    /**
     * 同步外单计费后折扣信息总开关true：开，false：关
     */
    public static final String LDOP_DISCOUNT_DETAIL_COMPLEMENT_SWITCH = "ldopDiscountDetailComplementSwitch" ;

    /**
     * 简易预分拣获取接驳点承接的业务身份白名单
     * 为空都允许；不为空匹配白名单允许；为null则都不允许
     */
    public static final String PRESORT_TRANSFER_BUSINESS_UNIT_WHITELIST = "presortTransferBusinessUnitWhiteList";

    /**
     * 预约取件时间校验开关 true 新逻辑 false 老逻辑
     */
    public static final String PICKUP_TIME_VALIDATE_SWITCH = "pickupTimeValidateSwitch";

    /**
     * 改址发票merchantID开关 true 新逻辑 false 老逻辑
     */
    public static final String READDRESS_INVOICE_MERCHANT_SWITCH = "readdressInvoiceMerchantSwitch";

    /**
     * 数据同步保留原单专业市场折扣开关true：开，false：关
     */
    public static final String DATA_SYNC_SNAPSHOT_DISCOUNT_SWITCH = "dataSyncSnapshotDiscountSwitch" ;

    /**
     * 改址记录超时取消，取消外单台账操作开关
     */
    public static final String READDRESS_RECORD_CANCEL_CLEAR_OTS_SWITCH = "readdressRecordCancelClearOtsSwitch" ;

    /**
     * 不允许改址的拦截状态黑名单
     */
    public static final String READDRESS_ORDER_INTERCEPT_TYPE_BLACK_LIST = "readdressOrderInterceptTypeWhiteList";

    /**
     * 合单上限
     */
    public static final String MERGE_ORDER_LIMIT = "mergeOrderLimit";

    /**
     * 非238来源，询价不覆盖（保留原有）附加费开关
     */
    public static final String ENQUIRY_RESERVE_ATTACH_FEE_SWITCH = "enquiryReserveAttachFeeSwitch";

    /**
     * 非238来源，询价不覆盖（保留原有）附加费编码名单
     */
    public static final String ENQUIRY_RESERVE_ATTACH_FEE_WHITE_LIST = "enquiryReserveAttachFeeWhiteList";

    /**
     * 改址异步询价下发失败异常处理 true 新逻辑转pdq重试， false 老逻辑抛出异常
     */
    public static final String READDRESS_ASYNC_ENQUIRY_ISSUE_RETRY_PDQ_SWITCH = "readdressAsyncEnquiryIssueRetryPdqSwitch";

    /**
     * 敏感词校验，修改策略白名单,配置规则：[名单值][][]
     */
    public static final String SENSITIVE_WORDS_MODIFY_RULE_WHITE_LIST = "sensitiveWordsModifyRuleWhiteList";

    /**
     * 特殊流程，能力节点直接从敏感词到下发，跳过中间流程节点,修改策略名单：[名单值][][]
     */
    public static final String SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_ISSUE_RULE_LIST = "specialFlowFromSensitiveWordsToIssueRuleList";

    /**
     * 防重降级业务单元白名单，多个","分割，ALL全部
     */
    public static final String ANTI_REPEAT_DOWN_UNIT_WHITE_LIST = "antiRepeatDownUnitWhiteList";

    /**
     * 初始化台账-写-降级，多个","分割，ALL全部
     */
    public static final String INIT_ORDER_BANK_WRITE_CACHE_DOWN_UNIT_WHITE_LIST = "initOrderBankWriteCacheDownUnitWhiteList";
    /**
     * 初始化台账-读-降级，多个","分割，ALL全部
     */
    public static final String INIT_ORDER_BANK_READ_CACHE_DOWN_UNIT_WHITE_LIST = "initOrderBankReadCacheDownUnitWhiteList";

    /**
     * 港澳订单B商家source=5 全部传开关 true：开，false：关
     */
    public static final String HKMO_B_MERCHANT_DATA_SOURCE_SWITCH = "hkmoBmerchantDataSourceSwitch" ;

    /**
     * 重复产品编码校验跳过 true：开，false：关
     */
    public static final String SKIP_CHECK_DUPLICATION_PRODUCT_NO = "skipCheckDuplicationProductNo" ;

    /**
     * 仓配快递信任上游预计送达时间开关
     *
     * -- 仓配快递，即supplyChainDelivery=1（是否仓配快递=是）时
     * -- 修改和接单：
     * -- 配送信息-预计送达时间，如果上游有就透传，上游没有就空着，无需将产品中心返回的做补充
     */
    public static final String SUPPLY_CHAIN_DELIVERY_TRUST_PLAN_DELIVERY_TIME_SWITCH = "supplyChainDeliveryTrustPlanDeliveryTimeSwitch";

    /**
     * C2C支付方式数据同步开关：true:同步；false不同步
     */
    public static final String C2C_PAYMENT_TYPE_SYNC_SWITCH = "c2cPaymentTypeSyncSwitch";

    /**
     * 允许"派送中"修改期望送达时间开关
     */
    public static final String MODIFY_EXPECT_DELIVERY_TIME_ON_DELIVERING_SWITCH = "modifyExpectDeliveryTimeOnDeliveringSwitch";

    /**
     * ECard城市校验切换开关：true,校验；false：不校验
     */
    public static final String VAL_E_CARD_CITY_SWITCH = "valECardCitySwitch";

    /**
     * C2C终端修改校验货品信息切换开关：true,校验；false：不校验
     */
    public static final String C2C_PDA_MODIFY_VAL_CARGO_SWITCH = "c2cPDAModifyValCargoSwitch";

    /**
     * 快运修改校验结算方式变更切换开关：true,校验；false：不校验
     */
    public static final String FREIGHT_MODIFY_CHECK_SETTLEMENT_TYPE_SWITCH = "freightModifyCheckSettlementTypeSwitch";

    /**
     * 快运修改校验查询运单详情切换开关：true,查询；false：不查询
     */
    public static final String FREIGHT_MODIFY_QUERY_WAYBILL_SWITCH = "freightModifyQueryWaybillSwitch";

    /**
     * 防并发修改、询价公用锁白名单，[名单值][][]，ALL全部
     */
    public static final String ANTI_RE_LOCK_MODIFY_ENQUIRY_WHITE_LIST = "antiReLockModifyEnquiryWhiteList";

    /**
     * 终端询价的订单，不同步费用明细开关：true,同步；false：不同步
     */
    public static final String TERMINAL_ENQUIRY_FINANCE_DETAIL_SYNC_SWITCH = "terminalEnquiryFinanceDetailSyncSwitch";

    /**
     * C2B取件单-多方收费-改址一单到底-月结运费允许改址的-费用编码白名单
     */
    public static final String C2B_READDRESS_1ORDER2END_COST_CHARGING_WHITE_LIST = "c2bReaddress1Order2EndCostChargingWhiteList";

    /**
     * 不允许改址一单到底的-支付方式-黑名单
     */
    public static final String READDRESS_1ORDER2END_PAY_TYPE_BLACK_LIST = "readdress1Order2EndPayTypeBlackList";

    /**
     * 派送中 不允许改址的增值服务黑名单
     */
    public static final String DELIVERY_READDRESS_ADD_ON_PRODUCT_BLACKLIST = "deliveryReaddressAddOnProductBlackList";

    /**
     * 派送中 不允许改址的青龙全程跟踪状态节点黑名单
     */
    public static final String DELIVERY_READDRESS_TRACK_STATE_BLACKLIST = "deliveryReaddressTrackStateBlackList";

    /**
     * 派送中改址校验开关
     */
    public static final String DELIVERY_READDRESS_VALID_SWITCH = "deliveryReaddressValidSwitch";

    /**
     * 改址一单到底全程跟踪状态节点校验开关
     */
    public static final String READDRESS_TRACK_STATE_VALID_SWITCH = "readdressTrackStateValidSwitch";

    /**
     * 校验地址长度开关 true：开，false：关
     */
    public static final String ADDRESS_LENGTH_VALIDATION = "addressLengthValidation" ;

    /**
     * 特殊流程，能力节点直接从敏感词到持久化，跳过中间流程节点,修改策略名单：[名单值][][]
     */
    public static final String SPECIAL_FLOW_FROM_SENSITIVE_WORDS_TO_REPOSITORY_RULE_LIST = "specialFlowFromSensitiveWordsToRepositoryRuleList";

    /**
     * 快运POS到付，ECard打标逻辑调整开关，true，新逻辑，false，老逻辑
     */
    public static final String FREIGHT_POS_DF_E_CARD_SWITCH = "freightPosDfECardSwitch";

    /**
     * 询价场景需要支付成功校验的-业务身份-白名单
     */
    public static final String ENQUIRY_PAY_SUCCESS_VALID_BUSINESS_UNIT_WHITE_LIST = "enquiryPaySuccessValidBusinessUnitWhiteList";

    /**
     * 接单前置校验，支持业务身份白名单：[名单值][][]
     */
    public static final String CREATE_PRE_CHECK_BUSINESS_UNIT_LIST = "createPreCheckBusinessUnitList";

    /**
     * boxCode size max value check customer white list
     */
    public static final String BOX_CODE_SIZE_MAX_VALUE_CHECK_CUSTOMER_WHITE_LIST = "boxCodeSizeMaxValueCheckCustomerWhiteList";

    /**
     * freight boxCode size max value check customer white list
     */
    public static final String FREIGHT_BOX_CODE_SIZE_MAX_VALUE_CHECK_CUSTOMER_WHITE_LIST = "freightBoxCodeSizeMaxValueCheckCustomerWhiteList";

    /**
     * 箱号校验-箱号最大个数
     */
    public static final String BOX_CODE_SIZE_MAX_VALUE = "boxCodeSizeMaxValue";

    /**
     * 箱号校验-箱号属性最大长度
     */
    public static final String BOX_CODE_STR_LENGTH_MAX_VALUE = "boxCodeStrLengthMaxValue";

    /**
     * 数据同步-COD修改标记不同步，支持业务身份白名单：[名单值][][]
     */
    public static final String WAYBILL_INFO_COD_TIMES_BUSINESS_UNIT_LIST = "waybillInfoCodTimesBusinessUnitList";

    /**
     * 仓配订单，派送预分拣实际网络类型为强B，不处理揽收预分拣：true：不处理，新流程；false：处理，旧流程
     */
    public static final String PRESORT_SKIP_START_STATION_SWITCH = "presortSkipStartStationSwitch";

    /**
     * 数据库锁冲突是否用UMP打点计数
     */
    public static final String UMP_REGISTER_GET_LOCK_ERROR_SWITCH = "umpRegisterGetLockErrorSwitch";

    /**
     * 改址一单到底：派送中改址，协商再投校验开关
     */
    public static final String NEGOTIATION_REDELIVERY_READDRESS_VALID_SWITCH = "negotiationRedeliveryReaddressValidSwitch";

    /**
     * 信任产品中心预计送达时间的增值服务
     */
    public static final String TRUST_PRODUCT_CENTER_ADDONS = "trustProductCenterAddons";

    /**
     * 敏感词校验切换 true：老流程，false：新流程 默认新流程
     */
    public static final String SENSITIVE_WORDS_SWITCH = "sensitiveWordsSwitch" ;

    /**
     * 敏感词校验切换,按渠道流量标识切换， true：新流程，false：老流程 默认新流程
     */
    public static final String SENSITIVE_WORDS_SWITCH_CHANNEL_FLAG = "sensitiveWordsSwitchChannelFlag" ;

    /**
     * 敏感词校验切换，业务身份名单，命中名单新流程，否则老流程
     */
    public static final String SENSITIVE_WORDS_SWITCH_BUSINESS_UNIT_LIST = "sensitiveWordsSwitchBusinessUnitList";

    /**
     * 敏感词校验切换SystemSubCaller名单， 命中名单新流程，否则老流程
     */
    public static final String SENSITIVE_WORDS_SWITCH_SYSTEM_SUB_CALLER_LIST = "sensitiveWordsSwitchSystemSubCallerList";

    /**
     * 敏感词校验切换 true：老流程，false：新流程 默认新流程
     */
    public static final String SENSITIVE_WORDS_EXCEPTION_SWITCH = "sensitiveWordsExceptionSwitch" ;

    /**
     * 敏感词校验拒单tag list [] 分割
     */
    public static final String SENSITIVE_WORDS_TAGS = "sensitiveWordsTags" ;

    /**
     * 敏感词校验单次批量请求数量
     */
    public static final String SENSITIVE_WORDS_SIZE = "sensitiveWordsSize" ;

    /**
     * 询问保险黑名单
     */
    public static final String INSURANCE_BLACK_LIST = "insuranceBlackList";

    /**
     * 询价校验支付成功开关跳过港澳条件的开关
     */
    public static final String ENQUIRY_PAY_SUCCESS_VALID_SKIP_HK_MO_SWITCH = "enquiryPaySuccessValidSkipHKMOSwitch";

    /**
     * 终端来源的修改，不校验产品运营模式开关
     */
    public static final String NOT_CHECK_OPERATION_MODE_FOR_TERMINAL = "notCheckOperationModeForTerminal";

    /**
     * 不允许拦截改址的拦截状态黑名单
     */
    public static final String INTERCEPT_READDRESS_INTERCEPT_TYPE_BLACK_LIST = "interceptReaddressInterceptTypeBlackList";

    /**
     * 拦截一单到底揽收后允许修改的增值服务白名单
     */
    public static final String INTERCEPT_READDRESS_AFTER_PICKUP_ALLOW_MODIFY_PRODUCT_WHITE_LIST = "interceptReaddressAfterPickupAllowModifyProductWhiteList";

    /**
     * 【快运】拦截一单到底揽收后允许修改的增值服务白名单
     */
    public static final String FREIGHT_INTERCEPT_READDRESS_AFTER_PICKUP_ALLOW_MODIFY_PRODUCT_WHITE_LIST = "freightInterceptReaddressAfterPickupAllowModifyProductWhiteList";

    /**
     * 拒收一单到底允许修改的增值服务白名单
     */
    public static final String REJECT_READDRESS_ALLOW_MODIFY_PRODUCT_WHITE_LIST = "rejectReaddressAllowModifyProductWhiteList";

    /**
     * 允许拦截一单到底的-业务身份-白名单
     */
    public static final String INTERCEPT_READDRESS_1ORDER2END_BUSINESS_UNIT_WHITE_LIST = "interceptReaddress1Order2EndBusinessUnitWhiteList";

    /**
     * 快运-改址一单到底-不允许改址的拦截状态黑名单
     */
    public static final String FREIGHT_READDRESS_ORDER_INTERCEPT_TYPE_BLACK_LIST = "freightReaddressOrderInterceptTypeBlackList";

    /**
     * 数据同步使用DataSyncAntiConcurrentUtil防并发
     */
    public static final String USE_DATA_SYNC_ANTI_CONCURRENT_UTIL_SWITCH = "useDataSyncAntiConcurrentUtilSwitch";

    /**
     * 执行校验的业务身份：数据同步时，校验修改接口并发锁
     */
    public static final String BUSINESS_UNIT_ANTI_CONCURRENT_MODIFY = "businessUnitAntiConcurrentModify";

    /**
     * 执行校验的业务身份：数据同步时，校验修改财务接口并发锁
     */
    public static final String BUSINESS_UNIT_ANTI_CONCURRENT_MODIFY_FINANCE = "businessUnitAntiConcurrentModifyFinance";

    /**
     * 执行校验的业务身份：数据同步时，校验操作台帐并发锁（多个接口会用台帐锁）
     */
    public static final String BUSINESS_UNIT_ANTI_CONCURRENT_ORDER_BANK_LOCK = "businessUnitAntiConcurrentOrderBankLock" ;

    /**
     * 快运B2C/快递B2C接单初始化是否补全发起补签标识：true：补全，新流程；false：不补全，旧流程
     */
    public static final String INIT_RE_SIGN_FLAG_SWITCH = "initReSignFlagSwitch";

    /**
     * 比对渠道信息开关：true：开启，false：关闭
     */
    public static final String COMPARE_CHANNEL_SWITCH = "compareChannelSwitch";

    /**
     * 国际订单不支持E卡校验开关：
     * true：开启，校验不支持E卡
     * false：关闭，不校验
     */
    public static final String INTL_ORDER_ECARD_VALID_SWITCH = "intlOrderECardValidSwitch";
    /**
     * 数据同步不覆盖折扣
     */
    public static final String WAYBILL_DISCOUNTS_UPDATE_BLACK_LIST = "waybillDisCountsUpdateBlackList";

    /**
     * 快递B2C跳过零售店铺校验开关：true：跳过，新逻辑；false：不跳过，旧逻辑
     */
    public static final String SKIP_VALIDATE_VENDER_ID_SWITCH = "skipValidateVenderIdSwitch";

    /**
     * 业务监控数据上报开关
     */
    public static final String BIZ_MONITOR_DATA_UPLOAD_SWITCH = "bizMonitorDataUploadSwitch";

    /**
     * 修改台账是否抛异常，true:抛；false：不抛
     */
    public static final String MODIFY_ORDER_BANK_THROW_EXCEPTION_SWITCH = "modifyOrderBankThrowExceptionSwitch";
    /**
     * 修改场景剔除原单运营模式，调产品中心重新获取-业务身份白名单，多个","分割，ALL全部
     */
    public static final String OPERATION_MODE_DELETE_PRO_UNIT_WHITE_LIST = "operationModeDeleteProUnitWhiteList";

    /**
     * 修改场景-仓配接配校验-剔除主产品运营模式开关 true：需要单独识别仓配接配场景不剔除；false：不需要单独识别场景【会剔除】
     */
    public static final String SUPPLY_CHAIN_DELIVERY_DELETE_OPERATION_MODE_SWITCH = "supplyChainDeliveryDeleteOperationModeSwitch";

    /**
     * 修改场景剔除原单运营模式，调产品中心重新获取-systemSubCaller白名单，多个","分割，ALL全部
     */
    public static final String OPERATION_MODE_DELETE_PRO_SYSTEM_SUB_CALLER_WHITE_LIST = "operationModeDeleteProSystemSubCallerWhiteList";

    /**
     * C2C修改不校验产品运营模式总开关
     */
    public static final String NOT_CHECK_OPERATION_MODE_FOR_C2C_MODIFY = "notCheckOperationModeForC2CModify";

    /**
     * 修改场景剔除原单运营模式，调产品中心重新获取-修改策略白名单，多个","分割，ALL全部
     */
    public static final String OPERATION_MODE_DELETE_PRO_MODIFY_RULE_WHITE_LIST = "operationModeDeleteProModifyRuleWhiteList";

    /**
     * 修改场景-揽收后-剔除主产品运营模式开关 true 剔除，false 不剔除
     */
    public static final String AFTER_PICKUP_DELETE_OPERATION_MODE_SWITCH = "afterPickupDeleteOperationModeSwitch";

    /**
     * 非厂直订单跳过店铺校验开关
     */
    public static final String NOT_VC_ORDER_SKIP_SHOP_CHECK_SWITCH = "notVCOrderSkipShopCheckSwitch";

    /**
    * 预分拣参数转换是否传预计送达时间deliveryPromiseTime：true：传，新流程；false：不传，旧流程
     */
    public static final String PRESORT_RPC_TRANSLATOR_DELIVERY_PROMISE_TIME_SWITCH = "presortRpcTranslatorDeliveryPromiseTimeSwitch";

    /**
     * 融合C2C快递主产品
     */
    public static final String UNITED_C2C_EXPRESS_MAIN_PRODUCTS = "unitedC2CExpressMainProducts";

    /**
     * 融合C2C快运主产品
     */
    public static final String UNITED_C2C_TRANSPORT_MAIN_PRODUCTS = "unitedC2CTransportMainProducts";

    /**
     * 禁止产品互改的增值产品
     */
    public static final String UNITED_C2C_EXCHANGE_PROHIBIT_ADDON_PRODUCTS ="unitedC2CExchangeProhibitAddonProducts";

    /**
     * 是否允许融合产品互改，1是、0否。其他值，跳过DUCC配置，走判断逻辑
     */
    public static final String ALLOW_UNITED_PRODUCT_EXCHANGE = "allowUnitedProductExchange";

    /**
     * 月结改址校验开关
     */
    public static final String MONTH_SETTLE_READDRESS_VALID_SWITCH = "monthSettleReaddressValidSwitch";

    /**
     * 改址异步询价持久化失败异步重试mq处理 true：jmq4处理,false: pdq处理
     */
    public static final String READDRESS_ASYNC_ENQUIRY_REPOSITORY_RETRY_MQ_SWITCH = "readdressAsyncEnquiryRepositoryRetryMqSwitch";

    /**
     * C2C回传支付成功，映射开关，true：映射：false，不映射
     */
    public static final String C2C_CALLBACK_PAYMENT_SUCCESS_SWITCH = "c2cCallbackPaymentSuccessSwitch";

    /**
     * 是否允许删除生成服务询价单的增值产品
     */
    public static final String SERVICE_ORDER_DELETE_SWITCH = "serviceOrderDeleteSwitch";

    /**
     * 自提暂存单推送开票开关
     */
    public static final String SELF_PICKUP_TEMPORARY_STORAGE_PUSH_INVOICE_SWITCH = "selfPickupTemporaryStoragePushInvoiceSwitch";

    /**
     * 询价后保留原单附加费开关 true-保留,false-不保留
     */
    public static final String RESERVE_SNAPSHOT_ATTACH_FEES_AFTER_ENQUIRY_SWITCH = "reserveSnapshotAttachFeesAfterEnquirySwitch";

    /**
     * C2C逆向单询价，原单费用计算规则切换开关，true：新逻辑-直接使用订单折后金额：false：老逻辑：根据费用明细和折扣重新计算
     */
    public static final String C2C_REVERSE_ORIGIN_ORDER_ENQUIRY_SWITCH = "c2cReverseOriginOrderEnquirySwitch";

    /**
     * UEP合单并发锁开关
     */
    public static final String UEP_MERGE_LOCK_SWITCH = "uepMergeLockSwitch";

    /**
     * 改址一单到底-同城同站-跳过询价写台账-开关 true-跳过；false：不跳过
     */
    public static final String READDRESS_SAME_SITE_SKIP_ENQUIRY_SWITCH = "readdressSameSiteSkipEnquirySwitch";

    /**
     * 德邦落地配，修改策略为揽收后修改，允许修改的增值服务白名单
     */
    public static final String DP_DELIVERY_AFTER_PICK_UP_PRODUCT_WHITE_LIST = "dpDeliveryAfterPickUpProductWhiteList";

    /**
     * 归因接入worker总开关
     */
    public static final String BATRIX_TRACER_WORKER_SWITCH = "batrixTracerWorkerSwitch";

    /**
     * 归因接入worker记录handler名单
     */
    public static final String BATRIX_TRACER_ALLOW_HANDLER = "batrixTracerAllowHandler";

    /**
     * 权益获取销售单号逻辑开关：true：商品信息：false：关联单
     */
    public static final String AFTER_SALES_NOS_SWITCH = "afterSalesNosSwitch";
    /**
     * 调计费询价是否传InquiryType开关，true：传，false：不传
     */
    public static final String ENQUIRY_EXTEND_INQUIRY_TYPE_SWITCH = "enquiryExtendInquiryTypeSwitch";
    /**
     * 零售订单校验，orderType，厂直订单订单类型名单
     */
    public static final String JD_VC_ORDER_ORDER_TYPE_LIST = "jdVcOrderOrderTypeList";

    /**
     * 零售商城厂直订单校验降级开关 true-降级回老逻辑只判断 tradeMold=1010
     */
    public static final String JD_VC_ORDER_VALID_DOWNGRADE_SWITCH = "jdVcOrderValidDowngradeSwitch";



    /**
     * 允许下发的数据修改记录的修改项
     */
    public static final String ISSUE_ALLOW_MODIFY_DATA_ITEM = "issueAllowModifyDataItem";
    /**
     * 物资周转1或3时，不执行预分拣校验开关，true：开启，不校验，false：校验
     */
    public static final String MATERIAL_TURNOVER_PRESORT_SWITCH = "materialTurnoverPresortSwitch";
    /**
     * 物资周转是否允许修改开关:true：不允许修改，false:允许修改，默认true
     */
    public static final String B2C_MATERIAL_TURNOVER_NOT_ALLOW_MODIFY_SWITCH = "b2cMaterialTurnoverNotAllowModifySwitch";

    /**
     * 产品校验后回填路由代码到配送信息扩展字段
     */
    public static final String BACK_FILL_SHIPMENT_VRS_PRODUCT_CODE_SWITCH = "backFillShipmentVrsProductCodeSwitch";

    /**
     * 服务加能力开关：true：开启；false：关闭
     */
    public static final String SERVICE_PLUS_SWITCH = "servicePlusSwitch";

    /**
     * 服务加能力异常是否抛异常拒单开关：true：抛异常 拒单；false：不抛异常 不拒单
     */
    public static final String SERVICE_PLUS_EXCEPTION_SWITCH = "servicePlusExceptionSwitch";

    /**
     * 芝麻代扣创建支付单时的下单金额
     */
    public static final String ALIPAY_CREATE_ORDER_AMOUNT = "alipayCreateOrderAmount";

    /**
     * 快递B2C-修改-国补修改校验-总开关：true：开启；false：关闭
     */
    public static final String GB_B2C_MODIFY_VALID_SWITCH = "gbB2cModifyValidSwitch";

    /**
     * 快递B2C-询价-剔除国补激活校验-总开关：true：开启；false：关闭
     */
    public static final String GB_B2C_ENQUIRY_CULL_SWITCH = "gbB2cEnquiryCullSwitch";

    /**
     * 无需揽收跳过揽收预分拣
     */
    public static final String NO_PICKUP_SKIP_START_PRESORT = "noPickupSkipStartPresort";

    /**
     * JDL转DP新流程开关
     */
    public static final String JDL_TO_DP_SWITCH = "jdlToDpSwitch";

    /**
     * 快手小单开票开关
     */
    public static final String KUAI_SHOU_INVOICE  = "kuaiShouInvoice";

    /**
     * UEP更新支付状态黑名单
     */
    public static final String UEP_PAYMENT_STATUS_BLACK_LIST = "uepPaymentStatusBlackList";

    /**
     * 终端修改标识初始化开关:true,持久化能力设置值；false,初始化能力设置值
     */
    public static final String TERMINAL_MODIFY_FLAG_INIT_SWITCH  = "terminalModifyFlagInitSwitch";


    /**
     * 香港离岛区列表
     */
    public static final String HK_OUTLYING_ISLANDS_LIST = "hkOutlyingIslandsList";
    /**
     * 香港离岛区列,最大重量限制-默认30 kg
     */
    public static final String HK_OUTLYING_ISLANDS_WEIGHT_MAX_VALUE = "hkOutlyingIslandsWeightMaxValue";
    /**
     * 香港离岛区列,最大体积限制-默认3375000 cm3
     */
    public static final String HK_OUTLYING_ISLANDS_VOLUME_MAX_VALUE = "hkOutlyingIslandsVolumeMaxValue";
    /**
     * 香港离岛区列,最大尺寸长宽高限制-默认150 cm
     */
    public static final String HK_OUTLYING_ISLANDS_DIMENSION_MAX_VALUE = "hkOutlyingIslandsDimensionMaxValue";

    /**
     * 逆向更新不报错的状态
     */
    public static final String UPDATE_REVERSE_STATUS_NOT_THROW_EXCEPTION = "updateReverseStatusNotThrowException";

    /**
     * B2C接单初始化台账处理写台账失败后，先款是否同步拒单开关：true：拒单；false：异步补偿 不拒单
     */
    public static final String B2C_CREATE_BANK_FAIL_SYNC_CANCEL_ORDER_SWITCH = "b2cCreateBankFailSyncCancelOrderSwitch";

    /**
     * 修改白名单履约账号
     */
    public static final String MODIFY_WHITE_ACCOUNT_NO_LIST = "modifyWhiteAccountNoList";

    /**
     * 快运 C网跳过删除等通知派送开关
     */
    public static String SKIP_DELETE_WAIT_NOTICE_DELIVERY_SWITCH = "skipDeleteWaitNoticeDeliverySwitch";

    /**
     * PDQ迁移到Batrix的topic列表，[名单值][][]，ALL全部
     */
    public static final String MOVING_PDQ_TO_BATRIX_TOPICS = "movingPdqToBatrixTopics";

    /**
     * 改址寄付后款卡控开关
     */
    public static final String READDRESS_JF_VALID_SWITCH = "readdressJfValidSwitch";

    /**
     * 融合C2C快递主产品
     */
    public static final String UNITED_B2C_EXPRESS_MAIN_PRODUCTS = "unitedB2CExpressMainProducts";

    /**
     * 融合C2C快运主产品
     */
    public static final String UNITED_B2C_TRANSPORT_MAIN_PRODUCTS = "unitedB2CTransportMainProducts";

    /**
     * 仓配接配接货仓是否跳过揽收预分拣：订单标识是否仓配为是（orderSign.supplyChainDelivery=1）且派送信息交付模式为接货仓（deliveryPattern=1）
     */
    public static final String SUPPLY_CHAIN_DELIVERY_SKIP_START_STATION_PRESORT = "supplyChainDeliverySkipStartStationPresort";

    /**
     * 快递C2C改址剔除主产品运营模式开关:true-剔除，false-不处理
     */
    public static final String C2C_READDRESS_OPERATION_MODE_DELETE_SWITCH = "c2cReaddressOperationModeDeleteSwitch";

    /**
     * 快递C2C-拦截一单到底-修改-产品校验降级-开关：true-开，降级；false-关，不降级
     */
    public static final String C2C_INTERCEPT_THROUGH_ORDER_PRODUCT_VALID_DOWNGRADE_SWITCH = "c2cInterceptThroughOrderProductValidDownGradeSwitch";

    /**
     * 仓配接配出库包裹信息更新业务身份
     */
    public static String OUTBOUND_PACKAGE_UPDATE_BUSINESS_UNIT_LIST = "outboundPackageUpdateBusinessUnitList";

    /**
     * 仓配接配运输中包裹数量为零报警
     */
    public static String ALARM_IF_SUPPLY_OFC_CARGO_QUANTITY_ZERO_SWITCH = "alarmIfSupplyOFCCargoQuantityZeroSwitch";

    /**
     * 数据同步终端切百川后跳过计费重体 true 跳过 false 同步
     */
    public static String TERMINAL_ENQUIRY_SKIP_ENQUIRY_INFO = "terminalEnquirySkipEnquiryInfo";

    /**
     * 大件下单是否挂新异常8051484
     */
    public static final String LAS_KA_PRE_CREATE_HOLD_SWITCH = "lasKaPreCreateHoldSwitch";

    /**
     * 数据同步B2C费用明细costNo使用feeType兜底的业务身份
     */
    public static final String SET_FEE_TYPE_IF_COST_NO_NULL_BUSINESS_UNIT_LIST = "setFeeTypeIfCostNoNullBusinessUnitList";

    /**
     * 送取同步，取件单下单绑定派送单时，派送单状态黑名单
     */
    public static String DELIVERY_PICKUP_SYNC_ORDER_STATUS_BLACKLIST = "deliveryPickupSyncOrderStatusBlackList";

    /**
     * 优化台账008001错误码开关
     */
    public static String ORDER_BANK_ERROR_CODE_SWITCH = "orderBankErrorCodeSwitch";

    /**
     * 送取同步，C2B接单校验开关
     */
    public static String DELIVERY_PICKUP_SYNC_VALIDATE_CREATE_SWITCH = "deliveryPickupSyncValidateCreateSwitch";

    /**
     * 送取同步，C2B/B2C修改校验开关
     */
    public static String DELIVERY_PICKUP_SYNC_VALIDATE_MODIFY_SWITCH = "deliveryPickupSyncValidateModifySwitch";

    /**
     * POP出海需求开关
     */
    public static final String B2C_CHECK_JD_POP_SWITCH = "b2cCheckJdPopSwitch";

    /**
     * 渠道编码校验新旧流程切换开关 true 缓存校验 false 接口校验
     */
    public static final String BASIC_CACHE_SWITCH = "basicCacheSwitch";

    /**
     * 渠道编码校验跳过开关 true 跳过校验（降级） false 正常校验
     */
    public static final String SKIP_CHANNEL_NO_VALID = "skipChannelNoValid";


    /**
     * 自定义托寄物客服审核流程开关:true,执行流程；false,跳过流程
     */
    public static final String CSS_AUDIT_CUSTOMS_SWITCH  = "cssAuditCustomsSwitch";


    /**
     * b2c校验收寄件人信息
     */
    public static final String B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK = "b2cModifyConsigneeAndConsignorCheck";
    /**
     * 防重锁释放逻辑切换开关 true 新逻辑 false 老逻辑
     */
    public static String DELETE_ANTI_REPEAT_LOCK_SWITCH = "deleteAntiRepeatLockSwitch";

    /**
     * e卡和折扣互斥逻辑 true 新逻辑 false 老逻辑
     */
    public static final String E_CARD_DISCOUNT_SWITCH = "eCardDiscountSwitch";

    /**
     * 有改址记录的订单是否允许修改订单结算方式，true：不允许修改,false:允许修改
     */
    public static final String HAVE_READDRESS_NOT_ALLOW_MODIFY_SETT_SWITCH = "haveReaddressNotAllowModifySettSwitch";

    /**
     * 对比关联单开关
     */
    public static final String COMPARE_REF_ORDER_SWITCH = "compareRefOrderSwitch";

    /**
     * 送取同步，自营运单不存在报警开关
     */
    public static String DELIVERY_PICKUP_SYNC_JD_RETAIL_ORDER_NOT_EXIST_ALARM_SWITCH = "deliveryPickupSyncJDRetailOrderNotExistAlarmSwitch";

    /**
     * 香港深度合作自提柜校验开关
     */
    public static final String VALIDATE_HK_SELF_PICKUP_LOCKER_SWITCH = "validateHKSelfPickupLockerSwitch";

    /**
     * 香港深度合作自提柜校验代收货款开关
     */
    public static final String VALIDATE_HK_SELF_PICKUP_LOCKER_COD_SWITCH = "validateHKSelfPickupLockerCODSwitch";;

    /**
     * 收件地址是香港澳门到付不能送自提点校验开关
     */
    public static final String VALIDATE_HK_MO_SELF_PICKUP_SETTLEMENT_TYPE_SWITCH = "validateHKMOSelfPickupSettlementTypeSwitch";


    /**
     * 接货退货拦截场景的回传请求不处理
     */
    public static final String JHTH_INTERCEPT_CALLBACK_NOT_NEED_PROCESS= "jhthInterceptCallbackNotNeedProcess";

    /**
     * 一单一标产品映射systemSubCaller黑名单
     */
    public static final String PRODUCT_MAPPING_SYSTEM_SUB_CALLER_BLACKLIST = "productMappingSystemSubCallerBlacklist";

    /**
     *  商家免赔白名单
     */
    public static final String SELLER_LIABILITY_LIST = "sellerLiabilityList";

    /**
     * 京喜售后 非商责类型 ：[1160700]
     * 7天无理由-1160700
     * 商品出现异常-1160703
     * 与商品描述不符-1160704
     * 包裹/收货异常-1160705
     * 发票问题-1160706
     * 其他-1160707
     */
    public static final String NON_SELLER_LIABILITY_TYPE = "NonSellerLiabilityType";

    /**
     * 预分拣 是否匹配DP资源 业务开关,true:开关开，处理；false: 开关关，不处理
     */
    public static final String PRESORT_USE_DEPPON_SWITCH = "presortUseDepponSwitch";


    /**
     * 快运寄付拦截一单到底cod修改校验开关 true-校验，不许修改；false-不校验，允许修改
     */
    public static final String FREIGHT_JF_INTERCEPT_THROUGH_ORDER_MODIFY_COD_VALID_SWITCH = "freightJFInterceptThroughOrderModifyCodValidSwitch";

    /**
     * 合同物流订单防重和ECLP防重顺序调整开关，true：先执行订单防重；false：先执行ECLP防重
     */
    public static final String CONTRACT_ANTI_REPEAT_ECLP_SWITCH = "contractAntiRepeatEclpSwitch";
    /**
     * 不做预分拣校验的主产品编码
     */
    public static String SKIP_PRESORT_MAIN_PRODUCTS = "skipPresortMainProducts";

    /**
     * 不允许修改订单的主产品编码
     */
    public static String MODIFY_ORDER_BLACK_MAIN_PRODUCT_LIST = "modifyOrderBlackMainProductList";

    /**
     * B2C跨城急送询价开关
     */
    public static String B2C_KCJS_ENQUIRY_SWITCH = "b2cKcjsEnquirySwitch";

    /**
     * 无需数据同步的主产品
     */
    public static String SKIP_WAYBILL_UPDATE_MAIN_PRODUCT_LIST = "skipWaybillUpdateMainProductList";

    /**
     * C2C回传退款成功，映射开关，true：映射：false，不映射
     */
    public static final String C2C_CALLBACK_REFUND_SUCCESS_SWITCH = "callbackRefundSuccessSwitch";

    /**
     * 冷链B2B预分拣参数orderId是否取customerOrderNo
     */
    public static final String SET_CUSTOMER_ORDER_NO_TO_ORDER_ID_SWITCH = "setCustomerOrderNoToOrderIdSwitch";
    /**
     * 交管12123-商家自计费模式-信任商家计费金额开关
     * true:[开 信任商家计费金额，用商家计费金额写账];
     * false:[关 不信任商家计费金额，比价金额相等写账，不能告警]
     */
    public static final String JG12123_TRUST_CUSTOMER_BILLING_AMT_SWITCH = "jg12123TrustCustomerBillingAmtSwitch";


    /**
     * B2C先款取消ofc来源触发退款开关
     */
    public static final String B2C_XK_OFC_CANCEL_REFUND_SWITCH = "b2cXkOFCCancelRefundSwitch";

    /**
     * jg12123 推送收入集成开关
     * true 开关开-推送收入集成
     * false 开关关-不推送收入集成
     */
    public static final String JG12123_PUSH_EBS_SWITCH = "jg12123PushEbsSwitch";

    /**
     * 平台应收明细同步开关
     */
    public static String ECAP_FEE_DETAILS_NOTICE_HANDLER_SWITCH = "ecapFeeDetailsNoticeHandlerSwitch";

    /**
     * 平台应收明细同步的业务身份
     */
    public static String ECAP_FEE_DETAILS_NOTICE_HANDLER_BUSINESS_UNIT_LIST = "ecapFeeDetailsNoticeBusinessUnitList";

    /**
     * 状态切换开关
     */
    public static final String EXPRESS_STATUS_BACK_SWITCH = "expressStatusBackSwitch";

    /**
     * 切换的状态列表
     */
    public static final String EXPRESS_STATUS_BACK_LIST = "expressStatusBackList";


    /**
     * 退款成功mq消息消费环境隔离处理开关true:处理环境隔离,false:不处理环境隔离
     */
    public static final String REFUND_SUCCESS_ENV_ISOLATION_SWITCH = "refundSuccessEnvIsolationSwitch";

    /**
     * 敏感词AB对比消息发送
     */
    public static String SENSITIVE_AB_SWITCH = "sensitiveAbSwitch";

    /**
     * 偏远流向开关
     */
    public static final String REMOTE_ROTE_SWITCH = "remoteRoteSwitch";

    /**
     * 状态逆序是否阻塞流程业务身份名单
     */
    public static final String STATUS_REVERSE_UNBLOCK_BUSINESS_UNIT_LIST = "statusReverseUnblockBusinessUnitList";

    /**
     * UEP用户打折subCaller白名单
     */
    public static final String UEP_USER_DISCOUNT_SUB_CALLER_WHITE_LIST = "uepUserDiscountSubCallerWhiteList";

    /**
     * UEP用户打折的产品名单
     */
    public static final String UEP_USER_DISCOUNT_PRODUCT_LIST = "uepUserDiscountProductsList";

    /**
     * lq-a-0028大件信息采集服务校验流程开关，true-新流程，false-旧流程
     */
    public static final String LAS_INFO_COLLECT_SWITCH = "lasInfoCollectSwitch";
    /**
     * jg12123 配送单创建关联支付单模式
     * true 走支付单接单流程
     * false 单独持久化流程
     */
    public static final String JG12123_DELIVERY_CREATE_PAY_ORDER_SERVICE_SWITCH = "jg12123DeliveryCreatePayOrderServiceSwitch";

    /**
     * 防重降级开关
     */
    public static final String ANTI_REPEAT_DOWN_GRADE_SWITCH = "antiRepeatDownGradeSwitch";

    /**
     * 逆向单或改址单，接单删除原单增值产品名单
     */
    public static String REVERSE_OR_READDRESS_DELETE_ORIGINAL_PRODUCT_NO_LIST = "reverseOrReaddressDeleteOriginalProductNoList";

    /**
     * 收件地址类型receiveAddressType校验开关
     */
    public static final String RECEIVE_ADDRESS_TYPE_CHECK_SWITCH = "receiveAddressTypeCheckSwitch";

    /**
     * 快运揽收前允许修改寄件人地址的子渠道名单
     */
    public static String ALLOW_MODIFY_CONSIGNOR_ADDRESS_SYSTEM_SUB_CALLER_LIST = "allowModifyConsignorAddressSystemSubCallerList";

    /**
     * 异步下发业务身份名单
     */
    public static final String ASYNC_ISSUE_BUSINESS_UNIT_LIST = "asyncIssueBusinessUnitList";
    /**
     * 异步下发SystemCaller名单
     */
    public static final String ASYNC_ISSUE_SYSTEM_CALLER_LIST = "asyncIssueSystemCallerList";

    /**
     * 修改持久化，修改产品信息KEY增加产品类型开关，true加产品类型，false不加产品类型，老逻辑
     */
    public static String MODIFY_REPOSITORY_PRODUCT_KEY_ADD_PRODUCT_TYPE = "modifyRepositoryProductKeyAddProductType";

    /**
     * 沃尔玛0包裹开关：true：校验，false：不校验
     */
    public static final String WALMART_ZERO_PACKAGE_SWITCH = "walmartZeroPackageSwitch";

    /**
     * 拍拍质检订单不做地址替换开关，true-新流程，false-旧流程
     */
    public static final String B2C_PAIPAI_CHECK_JD_ORDER_SWITCH = "b2cPaipaiCheckJdOrderSwitch";

    /**
     * C2C开票的支付方式
     */
    public static final String C2C_INVOICE_PAYMENT_TYPES = "c2cInvoicePaymentTypes";


    /**
     * 快运B2C是否限制命中京喜订单开关：true：校验，false：不校验
     */
    public static final String FREIGHT_B2C_JINGXI_QUANTUO_CHECK_SWITCH = "freightB2cJingXiQuanTuoCheckSwitch";

    /**
     * 默认关联关系扩展点开关，true：存在，false：null
     */
    public static final String DEFAULT_RELATION_EXTENSION_SWITCH = "defaultRelationExtensionSwitch";

    /**
     * 准时保新流程开关，true-新流程，false-旧流程
     */
    public static String ON_TIME_GUARANTEE_PROCESS_SWITCH = "onTimeGuaranteeProcessSwitch";

    /**
     * 回传时需要广播产品信息的运单执行状态
     */
    public static final String BROADCAST_PRODUCT_INFO_EXECUTED_STATUS_LIST = "broadcastProductInfoExecutedStatusList";
}