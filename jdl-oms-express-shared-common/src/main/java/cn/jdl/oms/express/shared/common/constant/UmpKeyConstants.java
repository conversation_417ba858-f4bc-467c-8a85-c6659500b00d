package cn.jdl.oms.express.shared.common.constant;

/**
 * @ProjectName：com.jdl.cp.oms.doo.shared.common.constant
 * @Package： com.jdl.cp.oms.doo.shared.common.constant
 * @ClassName: UmpKeyConstants
 * @Description: ump统一监控常量
 * @Author： <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate 2020/12/28  11:01 上午
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public class UmpKeyConstants {


    /**
     *
     */
    public static final boolean METHOD_ENABLE_HEART = false;
    /**
     *
     */
    public static final boolean METHOD_ENABLE_TP = true;
    /**
     * 监控应用名称
     */
    public static final String JDL_OMS_EXPRESS_APP_CODE = "express.oms.jdl.cn";

    /**
     * 监控应用名称
     */
    public static final String JDL_OMS_WORKER_EXPRESS_APP_CODE = "worker.express.oms.jdl.cn";
    /**
     * 参数校验未通过业务报警
     */
    public static final String UMP_JDL_OMS_PDQ_FAIL_JMQ_FAIL = "cn.jdl.oms.express.pdq.fail.jmq.fail";
    /**
     * 参数校验未通过业务报警
     */
    public static final String UMP_JDL_OMS_VALIDATE_ALARM_MONITOR = "cn.jdl.oms.express.service.validate.alarm";
    /**
     * apiResult信息转换异常报警
     */
    public static final String UMP_JDL_OMS_RESULT_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.result.exception.alarm";
    /**
     * 结果信息response转换异常报警
     */
    public static final String UMP_JDL_OMS_RESPONSE_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.response.exception.alarm";
    /**
     * 重复提交结果信息转换异常
     */
    public static final String UMP_JDL_OMS_ANTI_ORDER_DATA_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.anti.order.data.exception.alarm";
    /**
     * 纯配接单失败人工报警
     */
    public static final String UMP_JDL_OMS_CREATE_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.create.exception.alarm";
    /**
     * 纯配修改失败人工报警
     */
    public static final String UMP_JDL_OMS_MODIFY_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.modify.exception.alarm";
    /**
     * 纯配询价失败自定义业务报警
     */
    public static final String UMP_JDL_OMS_ENQUIRY_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.enquiry.exception.alarm";
    /**
     * 纯配取消失败人工报警
     */
    public static final String UMP_JDL_OMS_CANCEL_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.cancel.exception.alarm";
    /**
     * 纯配删单失败人工报警
     */
    public static final String UMP_JDL_OMS_DELETE_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.delete.exception.alarm";
    /**
     * 纯配恢复失败人工报警
     */
    public static final String UMP_JDL_OMS_RECOVER_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.recover.exception.alarm";
    /**
     * 纯配删单失败人工报警
     */
    public static final String UMP_JDL_OMS_CALLBACK_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.callback.exception.alarm";
    /**
     * 纯配修改订单财务失败人工报警
     */
    public static final String UMP_JDL_OMS_MODIFY_FINANCE_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.modify.finance.exception.alarm";
    /**
     * 纯配重处理失败人工报警
     */
    public static final String UMP_JDL_OMS_REACCEPT_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.reaccept.exception.alarm";
    /**
     * 纯配支付失败人工报警
     */
    public static final String UMP_JDL_OMS_PAY_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.pay.exception.alarm";

    /**
     * 参数校验未通过异常报警
     */
    public static final String UMP_JDL_OMS_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.exception.alarm";
    /**
     * 参数校验未通过异常报警
     */
    public static final String UMP_JDL_OMS_SENSITIVE_ALARM_MONITOR = "cn.jdl.oms.express.service.sensitive.alarm";
    /**
     * 产品校验相关报警
     */
    public static final String UMP_JDL_OMS_PRODUCT_ALARM_MONITOR = "cn.jdl.oms.express.service.product.alarm";
    /**
     * 对外提供服务业务身份类型监控配置
     */
    public static final String UMP_JDL_OMS_APPLICATION_SERVICE_MONITOR = "cn.jdl.oms.express.";

    /**
     * 运单信息变更MQ消费监控(work)
     */
    public static final String UMP_JDL_OMS_WORKER_WAYBILL_UPDATE_INFO = "cn.jdl.oms.worker.waybill.update.info.alarm";

    /**
     * 台账流水记录MQ
     */
    public static final String UMP_JDL_OMS_ORDER_BANK_FLOW_FAILURE = "cn.jdl.oms.express.order.bank.flow.failure.alarm";

    /**
     * 台账加锁失败
     */
    public static final String UMP_JDL_OMS_ORDER_BANK_LOCK_FAIL = "cn.jdl.oms.express.order.bank.trylock.alarm";

    /**
     * 异步询价写台账
     */
    public static final String UMP_JDL_OMS_ASYN_ENQUIRY_ORDER_BANK_FAIL = "cn.jdl.oms.express.asyn.enquiry.orderbank.alarm";

    /**
     * 订单超时取消业务报警
     */
    public static final String UMP_JDL_OMS_CANCEL_PAY_TIME_OUT_ORDER_ALARM_MONITOR = "cn.jdl.oms.express.cancel.pay.time.out.order.alarm";

    /**
     * 异步询价释放积分异常
     */
    public static final String UMP_JDL_OMS_ASNY_RELEASE_POINTS_ORDER_ALARM_MONITOR = "cn.jdl.oms.express.asny.release.points.order.alarm";

    /**
     * 改址单支付成功但订单已取消报警
     */
    public static final String UMP_JDL_OMS_PAY_SUCCESS_BUT_ORDER_CANCEL = "cn.jdl.oms.pay.success.but.order.cancel.alarm";


    /**
     * 台账异常
     */
    public static final String UMP_JDL_OMS_ORDER_BANK_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.orderbank.exception.alarm";

    /**
     * 台账异常(worker)
     */
    public static final String UMP_JDL_OMS_WORKER_ORDER_BANK_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.worker.orderbank.exception.alarm";

    /**
     * 台账清理异常
     */
    public static final String UMP_JDL_OMS_ORDER_BANK_CLEAR_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.order.bank.clear.exception.alarm";

    /**
     * 回传预分拣结果通知异常
     */
    public static final String UMP_JDL_OMS_CALLBACK_PRESORT_NOTICE_EXCEPTION_ALARM = "cn.jdl.oms.express.callback.presort.notice.exception.alarm";
    /**
     * 发送全程跟踪异常
     */
    public static final String UMP_JDL_OMS_CALLBACK_SEND_ORDER_TRACK_EXCEPTION_ALARM = "cn.jdl.oms.express.callback.send.order.track.exception.alarm";

    /**
     * 修改发送全程跟踪异常
     */
    public static final String UMP_JDL_OMS_MODIFY_SEND_ORDER_TRACK_EXCEPTION_ALARM = "cn.jdl.oms.express.modify.send.order.track.exception.alarm";
    /**
     * 状态通知异常
     */
    public static final String UMP_JDL_OMS_CALLBACK_ORDER_STATUS_NOTICE_EXCEPTION_ALARM = "cn.jdl.oms.express.callback.order.status.notice.exception.alarm";
    /**
     * 回传开票异常
     */
    public static final String UMP_JDL_OMS_PUSH_INVOICE_EXCEPTION_ALARM = "cn.jdl.oms.express.callback.push.invoice.exception.alarm";
    /**
     * 回传推送收入集成异常
     */
    public static final String UMP_JDL_OMS_PUSH_EBS_EXCEPTION_ALARM = "cn.jdl.oms.express.callback.push.ebs.exception.alarm";
    /**
     * 发送订单数据流水异常
     */
    public static final String UMP_JDL_OMS_ORDER_FIELD_DATA_NOTIFY_EXCEPTION_ALARM = "cn.jdl.oms.express.order.field.data.notify.exception.alarm";

    /**
     * PDQ任务执行监控配置
     */
    public static final String UMP_JDL_OMS_WORKER_HANDLER_MONITOR = "cn.jdl.oms.express.worker.pdq.";

    /**
     * 微信免密取消异常
     */
    public static final String UMP_JDL_OMS_WECHAT_PAYMENT_EXCEPTION_ALARM = "cn.jdl.oms.wechat.payment.exception.alarm";

    /**
     * 鸡毛信释放pdq异常
     */
    public static final String UMP_JDL_OMS_IOT_RELEASE_EXCEPTION_ALARM = "cn.jdl.oms.iot.release.exception.alarm";

    /**
     * 退款异常
     */
    public static final String UMP_JDL_OMS_REFUND_EXCEPTION_ALARM = "cn.jdl.oms.refund.exception.alarm";

    /**
     * 优惠券释放异常pdq异常
     */
    public static final String UMP_JDL_OMS_COUPON_RELEASE_EXCEPTION_ALARM = "cn.jdl.oms.coupon.release.exception.alarm";

    /**
     * 原单取消绑定新单异常
     */
    public static final String UMP_JDL_OMS_ORIGINAL_UNBIND_EXCEPTION_ALARM = "cn.jdl.oms.original.unbind.exception.alarm";

    /**
     * 积分释放pdq异常
     */
    public static final String UMP_JDL_OMS_INTEGRAL_RELEASE_EXCEPTION_ALARM = "cn.jdl.oms.integral.release.exception.alarm";

    /**
     * 白条预授权pdq异常
     */
    public static final String UMP_JDL_OMS_IOUS_RELEASE_EXCEPTION_ALARM = "cn.jdl.oms.ious.release.exception.alarm";

    /**
     * 违禁品黑名单校验明细数量大于100
     */
    public static final String UMP_JDL_OMS_CHECK_CONTRABAND_BLACK_LIST_FAIL = "cn.jdl.oms.express.check.contraband.black.list.alarm";

    /**
     * 不允许询价告警
     */
    public static final String UMP_JDL_OMS_NOI_ALLOW_ENQUIRY_ORDER_ALARM = "cn.jdl.oms.express.not.allow.enquiry.alarm";

    /**
     * 打印次数记录流水异常
     */
    public static final String UMP_JDL_OMS_PRINT_RECORD_ALARM = "cn.jdl.oms.express.print.record.alarm";

    /**
     * 敏感词校验失败监控
     */
    public static final String UMP_JDL_OMS_SENSITIVE_WORDS_VALIDATE_FAIL = "cn.jdl.oms.express.sensitive.words.validate.fail.alarm";

    /**
     * 数据变更通知下游mq发送异常
     */
    public static final String UMP_JDL_OMS_ORDER_UPDATE_NOTICE = "cn.jdl.oms.express.order.data.update.notice";

    /**
     * 运输方式枚举识别失败
     */
    public static final String UMP_JDL_OMS_TRANSPORT_TYPE_UNRECOGNIZED = "cn.jdl.oms.express.transportType.recognize.fail.alarm";

    /**
     * 运营模式枚举识别失败
     */
    public static final String UMP_JDL_OMS_OPERATION_MODE_UNRECOGNIZED = "cn.jdl.oms.express.operationMode.recognize.fail.alarm";

    /**
     * 关联关系存储调用失败
     */
    public static final String UMP_JDL_OMS_ORDER_RELATION_CREATE_FAIL = "cn.jdl.oms.express.order.relation.create.fail.alarm";

    /**
     * 获取台账机构异常
     */
    public static final String UMP_JDL_OMS_ORDER_BANK_ORG_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.orderbank.org.exception.alarm";

    /**
     * 营业部预占资源及校验能力异常
     */
    public static final String UMP_JDL_OMS_DEPARTMENT_RESOURCE_CHECK_PREEMPT_ALARM_MONITOR = "cn.jdl.oms.express.departmentResourceCheckPreempt.exception.alarm";

    /**
     * 保险服务请求失败
     */
    public static final String UMP_JDL_INSURANCE_CALL_FAIL = "cn.jdl.oms.express.insurance.call.failed.alarm";

    /**
     * 资源预占失败
     */
    public static final String UMP_JDL_RESOURCE_PREEMPT_FAIL = "cn.jdl.oms.express.resource.preempt.failed.alarm";

    /**
     * 对账失败需人工介入场景
     */
    public static final String UMP_JDL_DUIZHANG_FAIL = "cn.jdl.oms.express.duizhang.failed.alarm";

    /**
     * 开票信息推送失败，需人工介入场景
     */
    public static final String UMP_JDL_PUSH_INVOICE_FAIL = "cn.jdl.oms.express.push.invoice.failed.alarm";

    /**
     * 接单持久化，超时异常，需删除订单 业务报警
     */
    public static final String UMP_JDL_CREATE_REPOSITORY_TIMEOUT_DELETE = "cn.jdl.oms.express.create.repository.timeout.delete.alarm";
    /**
     * 删除订单缓存key失败 业务报警
     */
    public static final String UMP_JDL_DELETE_REDIS_CACHE_FAIL = "cn.jdl.oms.express.delete.redis.cache.fail.alarm";
    /**
     * 取消释放箱号失败,业务报警
     */
    public static final String UMP_JDL_CANCEL_RELEASE_BOX_CODE = "cn.jdl.oms.express.cancel.release.boxCode.alarm";

    /**
     * 合单次数打点
     */
    public static final String UMP_JDL_OMS_MERGE_ORDER_MONITOR = "cn.jdl.oms.express.merge.order.count";

    /**
     * 订单中心异步重试异常
     */
    public static final String UMP_JDL_OMS_RETRY_ALARM = "cn.jdl.oms.express.retry.alarm";

    /**
     * 订单中心释放子单防重缓存异常
     */
    public static final String UMP_JDL_OMS_RELEASE_CHILD_ANTI_REPEAT_ALARM = "cn.jdl.oms.express.release.child.anti.repeat.alarm";

    /**
     * 推lbs计费运费为空，业务告警
     */
    public static final String UMP_JDL_OMS_PUSH_LBS_FEE_ALARM = "cn.jdl.oms.express.push.lbs.fee.alarm";

    /**
     * 创建TMS询价单失败，业务报警
     */
    public static final String UMP_JDL_CREATE_TMS_ENQUIRY_BILL_PDQ_FAIL = "cn.jdl.oms.express.create.tms.enquiry.bill.pdq.fail";

    /**
     * 发送待销售确认异步任务消息失败，业务报警
     */
    public static final String UMP_JDL_OMS_CREATE_WAIT_SALES_CONFIRM_JMQ_FAIL = "cn.jdl.oms.express.create.wait.sales.confirm.jmq.fail";

    /**
     * 预热承接 JSF-消费者-下游不支持且无默认规则匹配
     */
    public static final String UMP_JDL_EXPRESS_YR_SKIP_FAIL = "cn.jdl.oms.express.yr.skip.fail.alarm";

    /**
     * 发送关闭支付二维码消息失败，业务报警
     */
    public static final String UMP_JDL_POS_PAY_QR_CLOSE_PDQ_FAIL = "cn.jdl.oms.express.pos.pay.qr.close.pdq.fail";

    /**
     * 前置校验失败业务报警
     */
    public static final String UMP_JDL_OMS_PRECHECK_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.service.precheck.exception.alarm";


    /**
     * 压测forceBot承接 JSF-消费者-下游不支持且无默认规则匹配
     */
    public static final String UMP_JDL_EXPRESS_YC_FORCE_BOT_FAIL = "cn.jdl.oms.express.yc.forceBot.fail.alarm";
    /**
     * 修改记录查询失败
     */
    public static final String UMP_JDL_OMS_MODIFY_RECORD_EXCEPTION_ALARM_MONITOR = "cn.jdl.oms.express.modify.record.exception.alarm";

    /**
     * 改址一单到底退款金额有误
     */
    public static final String UMP_JDL_OMS_READDRESS_REFUND_AMOUNT_ABNORMAL = "cn.jdl.oms.express.readdress.refund.amount.abnormal.alarm";

    /**
     * 改址一单到底支付超时取消任务构建异常
     */
    public static final String UMP_JDL_OMS_READDRESS_PAY_TIMEOUT_CREATE_FAIL = "cn.jdl.oms.express.readdress.pay.timeout.create.fail.alarm";

    /**
     * 改址一单到底退款金额过大
     */
    public static final String UMP_JDL_OMS_READDRESS_REFUND_AMOUNT_OVERLOAD = "cn.jdl.oms.express.readdress.refund.amount.overload.alarm";

    /**
     * 改址一单到底退款记录异常
     */
    public static final String UMP_JDL_OMS_READDRESS_REFUND_RECORD_ALARM = "cn.jdl.oms.express.readdress.refund.record.alarm";

    /**
     * 运单号释放失败人工报警
     */
    public static final String UMP_JDL_OMS_WAYBILLNO_RELEASE_ALARM = "cn.jdl.oms.express.waybillno.release.alarm";

    /**
     * 发送自动核销消息失败，业务报警
     */
    public static final String UMP_JDL_AUTO_WRITE_OFF_PDQ_FAIL = "cn.jdl.oms.express.auto.write.off.pdq.fail";
    /**
     * 改址后复重量方次数已达上限
     */
    public static final String UMP_JDL_OMS_READDRESS_ENQUIRY_LIMIT_ABNORMAL = "cn.jdl.oms.express.readdress.enquiry.limit.abnormal.alarm";

    /**
     * 改址异步询价
     */
    public static final String UMP_JDL_OMS_READDRESS_ASYNC_ENQUIRY_ORDER__FAIL = "cn.jdl.oms.express.readdress.async.enquiry.order.alarm";

    /**
     * 简易预分拣接驳站点获取异常
     */
    public static final String UMP_JDL_OMS_READDRESS_PRESORT_TRANSFER_ALARM = "cn.jdl.oms.express.presort.transfer.alarm";

    /**
     * 包裹维度附加费 涉及包裹数过多报警
     */
    public static final String UMP_JDL_OMS_SURCHARGE_PACKAGE_NUM_ALARM = "cn.jdl.oms.express.surcharge.package.num.alarm";

    /**
     * 快运询价参数非法报警
     */
    public static final String UMP_JDL_OMS_FREIGHT_ENQUIRY_INVALID_DATA = "cn.jdl.oms.express.freight.enquiry.invalid.data.alarm";

    /**
     * 物流平台保证相同客户单号订单号唯一逻辑异常，业务报警
     */
    public static final String UMP_JDL_UEP_OMS_UNI_ORDER_NO_FAIL = "cn.jdl.oms.express.uep.uni.order.no.fail";

    /**
     * 改址超时取消-取消外单台账-发起退款-通知业务审核
     */
    public static final String UMP_JDL_OMS_READDRESS_RECORD_CANCEL_ALARM = "cn.jdl.oms.express.readdress.record.cancel.alarm";

    /**
     * 包裹维度附加费 查询运单包裹称重流水报警
     */
    public static final String UMP_JDL_OMS_SURCHARGE_PACKAGE_OPE_QUERY_ALARM = "cn.jdl.oms.express.surcharge.package.ope.query.alarm";

    /**
     * 特殊快运修改下发失败报警
     */
    public static final String UMP_JDL_OMS_SPECIAL_FREIGHT_MODIFY_ISSUE_FAIL = "cn.jdl.oms.express.special.freight.modify.issue.fail";

    /**
     * 产品编码重复
     */
    public static final String UMP_JDL_OMS_DUPLICATION_PRODUCT_NO = "cn.jdl.oms.express.duplication.product.no.alarm";

    /**
     * 终端修改切百川，仍触发了数据同步监控
     */
    public static final String UMP_JDL_OMS_TERMINAL_MODIFY_SYNC_ALARM = "cn.jdl.oms.express.terminal.modify.sync.alarm";

    /**
     * 快运整车直达报价通知CRM失败，业务报警
     */
    public static final String UMP_JDL_OMS_ENQUIRY_QUOTE_NOTICE_CRM_FAIL = "cn.jdl.oms.express.enquiry.quote.notice.crm.fail";

    /**
     * 快运整车直达询价取消通知CRM失败，业务报警
     */
    public static final String UMP_JDL_OMS_ENQUIRY_CANCEL_NOTICE_CRM_FAIL = "cn.jdl.oms.express.enquiry.cancel.notice.crm.fail";

    /**
     * 预收款停用金额为空
     */
    public static final String UMP_JDL_OMS_PRE_CHARGE_MONEY_FAIL = "cn.jdl.oms.express.preChargeMoney.alarm";

    /**
     * 京东帮送数据同步
     */
    public static final String UMP_JDL_OMS_JDBS_WB_UPDATE_FAIL = "cn.jdl.oms.express.jdbs.alarm";

    /**
     * 外单台账merchantID识别异常报警
     */
    public static final String UMP_JDL_OMS_OTS_MERCHANT_ID_ALARM = "cn.jdl.oms.express.ots.merchantID.alarm";

    /**
     * 支付完成的订单重新询价
     */
    public static final String UMP_JDL_OMS_ENQUIRY_AFTER_COMPLETE_PAYMENT = "cn.jdl.oms.express.enquiry.afterCompletePayment.alarm";

    /**
     * 事后价金额异常
     */
    public static final String UMP_JDL_OMS_MTD_ENQUIRY_RESULT_ALARM = "cn.jdl.oms.express.mtd.enquiry.alarm";

    /**
     * 自提暂存单询价未返回暂存费用报警
     */
    public static final String UMP_JDL_OMS_ENQUIRY_NO_TEMPORARY_FEE = "cn.jdl.oms.express.enquiry.no.temporary.fee.alarm";

    /**
     * 暂存费计费无果
     */
    public static final String JDL_OMS_ZCF_JF_NO_RESULT_ALARM = "cn.jdl.oms.express.zcf.jf.no.result.alarm";

    /**
     * 逆向单询价原单折后金额为空或0，台账金额不为空
     */
    public static final String UMP_JDL_OMS_REVERSE_ORIGIN_ORDER_MONEY_NULL_ALARM = "cn.jdl.oms.express.reverse.origin.order.money.null.alarm";

    /**
     *
     */
    public static final String UMP_JDL_OMS_REVERSE_ORIGIN_ORDER_BANK_MONEY_NULL_ALARM = "cn.jdl.oms.express.reverse.origin.order.bank.money.null.alarm";

    /**
     * 权益获取售后服务单号结果为空告警
     */
    public static final String UMP_JDL_OMS_RIGHT_AFTER_SALE_NOS_EMPTY_ALARM = "cn.jdl.oms.express.right.afterSaleNos.empty.alarm";

    /**
     * 权益获取售后服务单号结果为空告警-UEP订单
     */
    public static final String UMP_JDL_OMS_RIGHT_AFTER_SALE_NOS_EMPTY_UEP_ALARM = "cn.jdl.oms.express.right.afterSaleNos.empty.uep.alarm";

    /**
     * 服务加回调异常报警
     */
    public static final String UMP_JDL_OMS_SERVICE_PLUS_CALLBACK_ALARM = "cn.jdl.oms.express.domain.ability.serviceplus.ServicePlusCallbackAbility.alarm";

    public static final String UMP_JDL_OMS_ALIPAY_TOO_BIG_PAY_AMOUNT = "cn.jdl.oms.express.alipay.tooBigPayAmount.alarm";

    /**
     * 回传异步事后折异常
     */
    public static final String UMP_JDL_OMS_ASYNC_SPDE_ALARM = "cn.jdl.oms.express.domain.flow.enquiry.AsyncStandardProductAndDiscountEnquiryFlowNode.alarm";

    /**
     * Batrix异常恢复报警
     */
    public static final String UMP_JDL_OMS_WORKER_BATRIX_EXCEPTION_RESUME_ALARM = "cn.jdl.oms.express.worker.rpc.impl.BatrixExceptionResumeApiImpl.resumeException";

    /**
     * 仓配接配，订单已出库，包裹数仍然为零，报警
     */
    public static final String UMP_JDL_OMS_SUPPLY_OFC_CARGO_QUANTITY_ZERO_SWITCH = "cn.jdl.oms.express.supply.ofc.cargo.quantity.zero.alarm";

    public static final String UMP_JDL_OMS_B2C_MODIFY_CONSIGNEE_AND_CONSIGNOR_CHECK_FAIL_ALARM = "cn.jdl.oms.express.b2c.modify.consignee.and.consignor.check.fail.alarm";

    /**
     * 送取同步异步绑定关联单失败报警
     */
    public static final String UMP_JDL_OMS_JMQ_HANDLER_DELIVERY_PICKUP_SYNC_BIND_FAIL_ALARM = "cn.jdl.oms.express.worker.message.handler.DeliveryPickupSyncBindJmqHandler.bindFail.alarm";

    /**
     * 送取同步异步绑定关联单发送消息失败报警
     */
    public static final String UMP_JDL_OMS_SEND_DELIVERY_PICKUP_SYNC_BIND_MESSAGE_FAIL_ALARM = "cn.jdl.oms.express.send.delivery.pickup.sync.bind.message.fail.alarm";

    /**
     * 送取同步派送单不存在报警
     */
    public static final String UMP_JDL_OMS_DELIVERY_PICKUP_SYNC_DELIVERY_ORDER_NOT_EXIST_ALARM = "cn.jdl.oms.express.delivery.pickup.sync.delivery.order.not.exist.alarm";

    /**
     * 送取同步接单校验失败报警
     */
    public static final String UMP_JDL_OMS_DELIVERY_PICKUP_SYNC_VALIDATE_CREATE_FAIL_ALARM = "cn.jdl.oms.express.delivery.pickup.sync.validate.create.fail.alarm";

    /**
     * 送取同步修改校验失败报警
     */
    public static final String UMP_JDL_OMS_DELIVERY_PICKUP_SYNC_VALIDATE_MODIFY_FAIL_ALARM = "cn.jdl.oms.express.delivery.pickup.sync.validate.modify.fail.alarm";

    /**
     * 送取同步接单校验跳过报警
     */
    public static final String UMP_JDL_OMS_DELIVERY_PICKUP_SYNC_PRE_CHECK_SKIP_ALARM = "cn.jdl.oms.express.delivery.pickup.sync.pre.check.skip.alarm";

    /**
     * 待支付金额空异常
     */
    public static final String UMP_JDL_OMS_PENDING_MONEY_ALARM = "cn.jdl.oms.express.pending.money.alarm";

    /**
     * PDQ迁移JMQ获取业务ID出错报警
     */
    public static final String UMP_JDL_OMS_PDQ_TO_JMQ_GET_BUSINESS_ID_EXCEPTION_ALARM = "cn.jdl.oms.express.pdq.to.jmq.get.business.id.exception.alarm";

    /**
     * 对比关联单异常报警
     */
    public static final String UMP_JDL_OMS_COMPARE_REF_ORDER_EXCEPTION_ALARM = "cn.jdl.oms.express.compare.ref.order.exception.alarm";

    /**
     * 香港深度合作自提柜校验报警
     */
    public static final String UMP_JDL_OMS_VALIDATE_HK_SELF_PICKUP_LOCKER_FAIL_ALARM = "cn.jdl.oms.express.validate.hk.self.pickup.locker.fail.alarm";

    /**
     * 收件地址是香港澳门到付不能送自提点报警
     */
    public static final String UMP_JDL_OMS_VALIDATE_HK_MO_SELF_PICKUP_SETTLEMENT_TYPE_FAIL_ALARM = "cn.jdl.oms.express.validate.hk.mo.self.pickup.settlement.type.fail.alarm";
    /**
     * 商家自计费模式比价异常
     */
    public static final String UMP_JDL_OMS_CUSTOMER_BILLING_AMT_DIFF_ALARM = "cn.jdl.oms.customer.billing.amt.diff.alarm";

    /**
     * 德邦询价异常处理，存在多条异常记录告警
     */
    public static final String DP_ENQUIRY_EXCEPTION_HANDLE_MULTI_ALARM = "cn.jdl.oms.dp.enquiry.exception.handle.multi.alarm";

    /**
     * 大件lq-a-0028揽收后修改卡控校验不通过
     */
    public static final String UMP_JDL_OMS_LAS_INFO_COLLECT_VALIDATE_FAIL_ALARM = "cn.jdl.oms.express.las.info.collect.validate.fail.alarm";

    /**
     * receiveAddressType新增校验卡单告警
     */
    public static final String UMP_JDL_OMS_RECEIVE_ADDRESS_TYPE_CHECK_ALARM = "cn.jdl.oms.express.receive.address.type.check.alarm";

    /**
     * 异步下发失败报警
     */
    public static final String UMP_JDL_OMS_CREATE_ASYNC_ISSUE_FAIL = "cn.jdl.oms.express.create.async.issue.fail";


    /**
     * e卡开发票异常
     */
    public static final String UMP_JDL_OMS_ECARD_INVOICE_ALARM = "cn.jdl.oms.express.ecard.invoice.alarm";

    /**
     * 准时保处理流程报错报警
     */
    public static final String UMP_JDL_OMS_ON_TIME_GUARANTEE_PROCESS_FAIL_ALARM = "cn.jdl.oms.express.on.time.guarantee.process.fail.alarm";

}