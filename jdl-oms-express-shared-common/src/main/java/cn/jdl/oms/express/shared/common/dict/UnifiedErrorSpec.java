package cn.jdl.oms.express.shared.common.dict;

/**
 * @ProjectName：cn.jdl.oms.express.shared.commmon.dict
 * @Package： cn.jdl.oms.express.shared.commmon.dict
 * @ClassName: ExpressOrderErrorSpec
 * @Description:
 * @Author： wa<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @CreateDate 2020/12/24  1:44 下午
 * @Copyright: Copyright (c)2020 JDL.CN All Right Reserved
 * @Since: JDK 1.8
 * @Version： V1.0
 */
public final class UnifiedErrorSpec {

    private UnifiedErrorSpec() {
    }

    /**
     * 错误码分段说明: 10000~~11000通用预留,错误码新增原则上不允许跳步,每次新增加一
     * 为避免多分支新增冲突，先到文档增加 https://joyspace.jd.com/pages/dq7S6DBKmbzvMRc3IyqZ
     */
    public enum BasisOrder implements ExpressOrderErrorSpec {
        INTERNAL_ERROR("10000", "系统内部错误"),
        ORDER_NOT_EXIST("10001", "订单不存在"),
        REPEAT_SUBMIT("10002", "重复提交"),
        ORDERNO_WAYBILLNO_ALL_BLANK("10003", "订单号和运单号不能同时为空"),
        REPEAT_CANCEL("10004", "重复取消"),
        NOT_NEED_RETRY("10005","不需要重试的业务异常码"),
        REPEAT_CREATE_ORDER("10007", "重复下单"),
        BASIC_INFO_VALIDATE_FAIL("11001", "参数校验失败"),
        SENSITIVE_WORDS_FAIL("11002", "敏感词校验失败"),
        ADDRESS_ANALYSIS_FAIL("11003", "地址解析失败"),
        CUSTOMER_VALIDATE_FAIL("11004", "客户信息校验失败"),
        CUSTOMER_CONFIG_VALIDATE_FAIL("11005", "客户配置信息校验失败"),
        PRODUCT_VALIDATE_FAIL("11006", "产品信息校验失败"),
        PRESORT_VALIDATE_FAIL("11007", "预分拣信息校验失败"),
        WAYBILLNO_VALIDATE_FAIL("11008", "运单号校验失败"),
        WAYBILLNO_GENERATE_FAIL("11009", "运单号生成失败"),
        ORDERNO_GENERATE_FAIL("11010", "订单号生成失败"),
        ENQUIRY_FAIL("11011", "询价失败"),
        ORDER_STATUS_VALIDATE_FAIL("11012", "订单状态校验失败"),
        ORDER_ISSUE_FAIL("11013", "订单下发失败"),
        ORDER_REPOSITORY_FAIL("11014", "订单存储失败"),
        ORDER_GET_FAIL("11015", "订单查询失败"),
        ORDER_INFO_NO_CHANGE("11016", "订单信息未发生变化"),
        LEDGER_VALIDATE_FAIL("11017", "台账校验失败"),
        WAY_BILL_CANCEL_FAIL("11018", "运单取消失败"),
        WAREHOUSE_OCCUPY_FAIL("11019", "京仓资源占用失败"),
        WAREHOUSE_RELEASE_FAIL("11020", "京仓资源释放失败"),
        IOT_OCCUPY_FAIL("11021", "鸡毛信资源预占失败"),
        IOT_RELEASE_FAIL("11022", "鸡毛信资源释放失败"),
        COUPON_OCCUPY_FAIL("11023", "优惠券预占失败"),
        COUPON_RELEASE_FAIL("11024", "优惠券释放失败"),
        COUPON_VERIFY_FAIL("11025", "优惠券校验失败"),
        ORG_QUERY_FAIL("11026", "机构号查询失败"),
        LEDGER_CLEAN_FAIL("11027", "台账清理失败"),
        WAYBILL_UPDATE_INFO_FAIL("11028", "运单信息变更处理失败"),
        REVERSE_ORDER_BANK_FAIL("11029", "逆向询价台账异步化失败"),
        QUERY_ORDER_BANK_FAIL_DETAIL("11030", "外单台账获取实收明细失败"),
        QUERY_ORDER_BANK_FAIL_TOTAL("11031", "外单台账获取有效台账总账失败"),
        EPT_REFUND_APPLY_FAIL("11032", "退款申请接口申请退款失败"),
        WHITE_NOTE_OCCUPY_FAIL("11033", "白条预授预占失败"),
        WHITE_NOTE_RELEASE_FAIL("11034", "白条预授释放失败"),
        REFUND_INSERT_DB_FAIL("11035", "退款持久化数据失败"),
        ORDER_TIME_OUT_CANCEL_FAIL("11036", "订单超时取消执行失败"),
        BIND_ORIGINAL_ORDER_FAIL("11037","绑定原单失败"),
        REFUND_FAIL("11039","退款失败"),
        WAY_BILL_DETAIL_FIND_FAIL("11040","运单详情接口查询失败"),
        WE_CHAT_PAYMENT_FAIL("11041","微信免密单取消失败"),
        INTEGRAL_OCCUPY_FAIL("11042","积分占用异常"),
        INTEGRAL_RELEASE_FAIL("11043","积分释放异常"),
        ECARD_GET_FAIL("11044","E卡信息查询异常"),
        ONLINE_PAYMENT_CANCEL_FAIL("11045","不支持先款订单支付成功后取消"),
        CASHON_DELIVERY_CANCEL_FAIL("11046","不支持后款订单已下发后取消"),
        ORIGINAL_ORDER_MODIFY_FAIL("11047", "原单修改失败"),
        PRODUCT_CLEAR_FAIL("11047", "产品清理失败"),

        BOX_CODE_CHECK_FAIL("11048","箱号校验失败"),
        ANTI_DESENSITIZATION_CHECK_FAIL("11049","反脱敏校验失败"),
        JD_ORDER_VALIDATE_FAIL("11050","零售订单校验失败"),
        CHANNEL_CODE_CHECK_FAIL("11051", "渠道信息编码校验失败"),
        JD_ORDER_GET_FAIL("11052","零售订单详情查询失败"),
        CHECK_CONTRABAND_FAIL("11053","违禁品校验失败"),
        BOX_CODE_CACHE_CLEAR_FAIL("11054","箱号缓存清理失败"),
        ORDER_CONTROL_FAIL("11055", "订单控单-商家产品适配异常"),
        ORDER_CONTROL_SUCC("11056", "订单拦截-商家产品适配异常"),
        GENERATE_PACKAGE_CODE_FAIL("11057", "包裹号生成失败"),
        MODIFY_BLANK_VALIDATE_FAIL("11058", "修改黑名单校验失败"),
        OWN_SEND_CABINET_CODE_FAIL("11059", "自寄柜校验失败"),
        GET_OWN_SEND_CABINET_CODE_FAIL("11060", "获取自寄柜数据失败"),
        ORDER_TRACK_FAIL("11061", "上传全链路跟踪失败"),
        ORDER_REPOSITORY_ES_FAIL("11062", "订单存储ES失败"),
        COUPON_RELEASE_MODIFY_FAIL("11063", "原优惠券释放成功,其他信息修改失败"),
        MODIFY_ORDER_REPLACE_MAIN_PRODUCT_FAIL("11064", "订单修改替换主产品失败"),
        NOT_SUPPORT_CASH_ON_DELIVERY("11065", "该客户不允许使用到付现结"),
        ENQUIRY_CONFIRMED_ORDER_NOT_ALLOW_CONSUMER_MODIFY("11066", "订单费用信息已确认，不允许用户发起修改"),
        PRINT_RECORD_FAIL("11067","打印流水记录异常"),
        PICKUP_CODE_FAIL("11068", "取件码获取失败"),
        ORDER_STATUS_CHANGE_NOTIFY("11069", "订单状态变更通知失败"),
        PICKUP_TIME_FAIL("11070", "揽收时间范围获取失败"),
        CREATE_ORDER_RELATION_FAIL("11071", "创建关联关系失败"),
        CREATE_OPERATION_FAIL("11072", "运营能力校验失败"),
        APPOINT_RETURN_INFO_LOSS("11073","指定退货地址信息不全"),
        YUN_ORDER_GET_FAIL("11079","根据运单号查询实收信息失败"),
        PARAM_VALUE_READ_FAIL("11080", "配置中心-配置方案读取失败"),
        DEPARTMENT_RESOURCE_CHECK_PREEMPT__FAIL("11078", "营业部资源校验预占失败"),
        INSURANCE_PREEMPT_FAIL("11076", "保险预占失败"),
        INSURANCE_RELEASE_FAIL("11077", "保险释放失败"),
        QUERY_ORDER_RELATION_FAIL("11082", "查询关联关系失败"),
        PUSH_INVOICE_FAIL("11083", "发票信息推送失败"),
        BOX_CHARGE_QUERY_FAIL("11074", "包装信息查询失败"),
        GET_WAY_BILL_NO_FAIL("11088", "获取运单号失败"),
        CANCEL_READDRESS_FAIL("11089", "取消改址失败"),
        ORDER_STATUS_REPOSITORY_FAIL("11090", "订单状态流水持久化失败"),
        PAYMENT_FAIL("11096", "支付失败"),
        REGION_SERVICE_ABILITY_CHECK_FAIL("11084", "区域服务能力校验失败"),
        PRODUCT_BASE_INFO_QUERY_FAIL("11085", "产品基础信息查询失败"),
        FEE_INFO_RESULT_FAIL("11086", "计费结果消息处理失败"),
        CENTER_FRESH_FEE_FAIL("11087", "同步外单寄付现结费用消息处理失败"),
        DELIVERY_PICKUP_SYNC_FAIL("11093", "送取同步下单失败"),
        DELETE_ORDER_RELATION_FAIL("11097", "删除关联关系失败"),
        TAX_MESSAGE_HANDLE_FAIL("11094", "税金消息处理失败"),
        CUSTOMS_STATUS_MESSAGE_HANDLE_FAIL("11095", "关服系统状态同步消息处理失败"),
        ORDER_QUANTITY_LIMIT_FAIL("11092", "单量限制失败"),
        REPEAT_REFUND("11099", "重复退款"),
        MERGE_ORDER_FAIL("11100", "合单失败"),
        MODIFY_ORDER_FAIL("11101", "订单修改失败"),
        MODIFY_VALIDATE_FAIL_COD("11105", "修改项校验失败，已修改过代收货款，不允许多次修改"),
        MODIFY_VALIDATE_FAIL_CONSIGNEE("11106", "修改项校验失败，已修改过收件人信息，不允许多次修改"),
        PUSH_EXCEPTION_CENTER_FAIL("11107", "异常中心写入失败"),
        RIGHT_PREOCCUPY_FAIL("11111", "权益预占失败"),
        ADJUSTMENT_FEE_FAIL("11098", "调节费计算失败"),
        VEHICLE_CHECK_VALIDATE_FAIL("11108", "车型校验失败"),
        CCB2B_BATCH_ENQUIRY_CREATE_SYNC_FAIL("11109", "整车询价同步下单失败"),
        CHILD_ORDER_CREATE_FAIL("11110", "子单创建失败"),
        COLD_CHAIN_WAYBILL_UNPAID("11117", "冷链运单待支付处理失败"),
        BLUE_DRAGON_TRACK_FAIL("11121", "上传青龙链路跟踪失败"),
        PAY_PROCESSED("11119", "支付处理中"),
        BUILD_ENQUIRY_ORDER_FAIL("11102", "生成询价单失败"),
        PUSH_EBS_FAIL("11103", "推送收入集成失败"),
        CANCEL_ENQUIRY_ORDER_FAIL("11104", "取消询价单失败"),
        PUSH_FEE_FAIL("11122", "推计费失败"),

        CREATE_TMS_ENQUIRY_BILL("11113", "创建TMS询价单服务失败"),
        CANCEL_TMS_ENQUIRY_BILL("11114", "取消TMS询价单服务失败"),
        WAIT_SALES_CONFIRM_FAIL("11115", "待销售确认异步任务执行失败"),
        REACCEPT_FAIL("11116", "重新受理失败"),
        TMS_ENQUIRY_CONFIRM_BACK_FEE_ILLEGAL("11118", "TMS询价回传费用非法"),
        CONFIRM_TMS_ENQUIRY_BILL("11123", "确认TMS询价单服务失败"),
        EXCHANGE_CURRENCY_FAIL("11120", "换汇失败"),
        OCCUPY_SHIPPING_LINE_FAIL("11125", "仓位预占失败"),
        CANCEL_SHIPPING_LINE_FAIL("11126", "仓位取消失败"),
        QUERY_STAFF_AND_SITE_FAIL("11127", "查询员工及站点基础信息失败"),
        SEND_ASYN_ORDER_TRACK("11128", "发送全程跟踪失败"),
        POS_PAY_QR_CLOSE_FAIL("11129", "关闭支付二维码失败"),
        LEDGER_CREATE_FAIL("11130", "台账创建失败"),
        WAYBILLNO_RELEASE_FAIL("11131", "运单号释放失败"),
        OFF_PAYMENT_UPDATE_ORDER_STATUS_FAIL("11132", "总代系统核销失败"),
        CARGO_WAYBILL_CODE_RULE_FAIL("11133", "包裹号规则校验失败"),
        UNICODE_CHECK_FAIL("11134","唯一码校验失败"),
        MODIFY_RECORD_LIMIT_MAX_FAIL("11135", "变更记录已达上限"),
        MULTI_ADDRESS_ANALYSIS_FAIL("11136", "多地址校验失败"),
        MODIFY_MAIN_EXT_FAIL("11137", "主档扩展字段修改失败"),
        PRODUCT_SURCHARGE_FETCH_FAIL("11138", "产品附加费获取失败"),
        REAL_PRICE_ZERO_REFUND_FAIL("11140", "外单实收金额为0，退款失败"),
        UNITED_IDENTITY_TRANSFER_FAIL("11141", "融合身份产品互转失败"),
        PRECHARGE_ACCOUNT_OP_FAIL("11142", "收发管家账户操作异常"),
        COMPLETE_PAYMENT_SERVICE_ENQUIRY_ORDER_CANCEL_FAIL("11144", "已支付的自提暂存服务单不允许取消"),
        PAYMENT_ORDER_CREATE_FAIL("11146", "芝麻代扣支付单创建失败"),
        PAYMENT_ORDER_CANCEL_FAIL("11147", "芝麻代扣支付单取消失败"),
        PAYMENT_ORDER_PAY_FAIL("11148", "芝麻代扣支付单支付失败"),
        SERVICE_PLUS_FAIL("11143", "服务加服务异常"),
        OUTBOUND_PACKAGE_UPDATE_FAIL("11145", "仓配接配出库包裹信息更新异常"),
        REJECT_READDRESS_REPEAT_FAIL("11149", "已执行拒收一单流程，不可进行拒收换单"),
        REJECT_READDRESS_LOCK_FAIL("11150", "拒收一单流程正在执行中，不可进行拒收一单或者拒收换单，稍后再试"),
        REJECT_READDRESS_ORDER_STATUS_VALIDATE_FAIL("11151", "当前订单状态不是拒收，不允许进行拒收一单到底修改"),
        ASYNC_SPDE_FAIL("11152", "异步事后折接口调用失败"),
        DELIVERY_PICKUP_SYNC_BIND_FAIL("11153", "送取同步绑定或解绑关联单失败"),
        CUSTOMER_BILLING_PRO_FAIL("11154", "商家自计费处理异常"),
        CANCEL_ORDER_FAIL("11155", "关联订单取消失败"),
        WORKER_ANTI_CONCURRENT_FAIL("11156", "Worker并发锁校验失败"),
        CREATE_ORDER_FAIL("11157", "关联订单创建失败"),
        QUERY_BSC_EXCEPTION_FAIL("11159", "查询神行异常失败"),
        UPDATE_BSC_EXCEPTION_FAIL("11160", "更新神行异常失败"),
        ADDRESS_DISTANCE_FAIL("11161", "地址距离计算失败"),
        ;

        /**
         * 错误码
         */
        private String code;
        /**
         * 错误码描述
         */
        private String desc;

        BasisOrder(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @Override
        public String code() {
            return code;
        }


        @Override
        public String desc() {
            return desc;
        }
    }
}
